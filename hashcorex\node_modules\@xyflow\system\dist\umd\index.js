!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).XYFlowSystem={})}(this,(function(t){"use strict";const e={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:t=>`Node type "${t}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:t=>`The old edge with id=${t} does not exist.`,error009:t=>`Marker type "${t}" doesn't exist.`,error008:(t,{id:e,sourceHandle:n,targetHandle:o})=>`Couldn't create edge for ${t} handle id: "${"source"===t?n:o}", edge id: ${e}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:t=>`Edge type "${t}" not found. Using fallback type "default".`,error012:t=>`Node with id "${t}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(t="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${t}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},n=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],o={"node.a11yDescription.default":"Press enter or space to select a node. Press delete to remove it and escape to cancel.","node.a11yDescription.keyboardDisabled":"Press enter or space to select a node. You can then use the arrow keys to move the node around. Press delete to remove it and escape to cancel.","node.a11yDescription.ariaLiveMessage":({direction:t,x:e,y:n})=>`Moved selected node ${t}. New position, x: ${e}, y: ${n}`,"edge.a11yDescription.default":"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.","controls.ariaLabel":"Control Panel","controls.zoomIn.ariaLabel":"Zoom In","controls.zoomOut.ariaLabel":"Zoom Out","controls.fitView.ariaLabel":"Fit View","controls.interactive.ariaLabel":"Toggle Interactivity","minimap.ariaLabel":"Mini Map","handle.ariaLabel":"Handle"};var r,i,a;t.ConnectionMode=void 0,(r=t.ConnectionMode||(t.ConnectionMode={})).Strict="strict",r.Loose="loose",t.PanOnScrollMode=void 0,(i=t.PanOnScrollMode||(t.PanOnScrollMode={})).Free="free",i.Vertical="vertical",i.Horizontal="horizontal",t.SelectionMode=void 0,(a=t.SelectionMode||(t.SelectionMode={})).Partial="partial",a.Full="full";var s,u,l;t.ConnectionLineType=void 0,(s=t.ConnectionLineType||(t.ConnectionLineType={})).Bezier="default",s.Straight="straight",s.Step="step",s.SmoothStep="smoothstep",s.SimpleBezier="simplebezier",t.MarkerType=void 0,(u=t.MarkerType||(t.MarkerType={})).Arrow="arrow",u.ArrowClosed="arrowclosed",t.Position=void 0,(l=t.Position||(t.Position={})).Left="left",l.Top="top",l.Right="right",l.Bottom="bottom";const c={[t.Position.Left]:t.Position.Right,[t.Position.Right]:t.Position.Left,[t.Position.Top]:t.Position.Bottom,[t.Position.Bottom]:t.Position.Top};const h=t=>"id"in t&&"source"in t&&"target"in t,d=t=>"id"in t&&"internals"in t&&!("source"in t)&&!("target"in t),f=(t,e=[0,0])=>{const{width:n,height:o}=D(t),r=t.origin??e,i=n*r[0],a=o*r[1];return{x:t.position.x-i,y:t.position.y-a}},p=(t,e={})=>{if(0===t.size)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return t.forEach((t=>{if(void 0===e.filter||e.filter(t)){const e=S(t);n=b(n,e)}})),P(n)},g=(t,e)=>{const n=new Set;return t.forEach((t=>{n.add(t.id)})),e.filter((t=>n.has(t.source)||n.has(t.target)))};function m({nodeId:t,nextPosition:n,nodeLookup:o,nodeOrigin:r=[0,0],nodeExtent:i,onError:a}){const s=o.get(t),u=s.parentId?o.get(s.parentId):void 0,{x:l,y:c}=u?u.internals.positionAbsolute:{x:0,y:0},h=s.origin??r;let d=i;if("parent"!==s.extent||s.expandParent)u&&B(s.extent)&&(d=[[s.extent[0][0]+l,s.extent[0][1]+c],[s.extent[1][0]+l,s.extent[1][1]+c]]);else if(u){const t=u.measured.width,e=u.measured.height;t&&e&&(d=[[l,c],[l+t,c+e]])}else a?.("005",e.error005());const f=B(d)?v(n,d,s.measured):n;return void 0!==s.measured.width&&void 0!==s.measured.height||a?.("015",e.error015()),{position:{x:f.x-l+(s.measured.width??0)*h[0],y:f.y-c+(s.measured.height??0)*h[1]},positionAbsolute:f}}const y=(t,e=0,n=1)=>Math.min(Math.max(t,e),n),v=(t={x:0,y:0},e,n)=>({x:y(t.x,e[0][0],e[1][0]-(n?.width??0)),y:y(t.y,e[0][1],e[1][1]-(n?.height??0))});function x(t,e,n){const{width:o,height:r}=D(n),{x:i,y:a}=n.internals.positionAbsolute;return v(t,[[i,a],[i+o,a+r]],e)}const w=(t,e,n)=>t<e?y(Math.abs(t-e),1,e)/e:t>n?-y(Math.abs(t-n),1,e)/e:0,_=(t,e,n=15,o=40)=>[w(t.x,o,e.width-o)*n,w(t.y,o,e.height-o)*n],b=(t,e)=>({x:Math.min(t.x,e.x),y:Math.min(t.y,e.y),x2:Math.max(t.x2,e.x2),y2:Math.max(t.y2,e.y2)}),M=({x:t,y:e,width:n,height:o})=>({x:t,y:e,x2:t+n,y2:e+o}),P=({x:t,y:e,x2:n,y2:o})=>({x:t,y:e,width:n-t,height:o-e}),E=(t,e=[0,0])=>{const{x:n,y:o}=d(t)?t.internals.positionAbsolute:f(t,e);return{x:n,y:o,width:t.measured?.width??t.width??t.initialWidth??0,height:t.measured?.height??t.height??t.initialHeight??0}},S=(t,e=[0,0])=>{const{x:n,y:o}=d(t)?t.internals.positionAbsolute:f(t,e);return{x:n,y:o,x2:n+(t.measured?.width??t.width??t.initialWidth??0),y2:o+(t.measured?.height??t.height??t.initialHeight??0)}},N=(t,e)=>P(b(M(t),M(e))),z=(t,e)=>{const n=Math.max(0,Math.min(t.x+t.width,e.x+e.width)-Math.max(t.x,e.x)),o=Math.max(0,Math.min(t.y+t.height,e.y+e.height)-Math.max(t.y,e.y));return Math.ceil(n*o)},k=t=>!isNaN(t)&&isFinite(t),A=(t,e)=>{},T=(t,e=[1,1])=>({x:e[0]*Math.round(t.x/e[0]),y:e[1]*Math.round(t.y/e[1])}),I=({x:t,y:e},[n,o,r],i=!1,a=[1,1])=>{const s={x:(t-n)/r,y:(e-o)/r};return i?T(s,a):s},$=({x:t,y:e},[n,o,r])=>({x:t*r+n,y:e*r+o});function C(t,e){if("number"==typeof t)return Math.floor(.5*(e-e/(1+t)));if("string"==typeof t&&t.endsWith("px")){const e=parseFloat(t);if(!Number.isNaN(e))return Math.floor(e)}if("string"==typeof t&&t.endsWith("%")){const n=parseFloat(t);if(!Number.isNaN(n))return Math.floor(e*n*.01)}return console.error(`[React Flow] The padding value "${t}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}const H=(t,e,n,o,r,i)=>{const a=function(t,e,n){if("string"==typeof t||"number"==typeof t){const o=C(t,n),r=C(t,e);return{top:o,right:r,bottom:o,left:r,x:2*r,y:2*o}}if("object"==typeof t){const o=C(t.top??t.y??0,n),r=C(t.bottom??t.y??0,n),i=C(t.left??t.x??0,e),a=C(t.right??t.x??0,e);return{top:o,right:a,bottom:r,left:i,x:i+a,y:o+r}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}(i,e,n),s=(e-a.x)/t.width,u=(n-a.y)/t.height,l=Math.min(s,u),c=y(l,o,r),h=e/2-(t.x+t.width/2)*c,d=n/2-(t.y+t.height/2)*c,f=function(t,e,n,o,r,i){const{x:a,y:s}=$(t,[e,n,o]),{x:u,y:l}=$({x:t.x+t.width,y:t.y+t.height},[e,n,o]),c=r-u,h=i-l;return{left:Math.floor(a),top:Math.floor(s),right:Math.floor(c),bottom:Math.floor(h)}}(t,h,d,c,e,n),p=Math.min(f.left-a.left,0),g=Math.min(f.top-a.top,0);return{x:h-p+Math.min(f.right-a.right,0),y:d-g+Math.min(f.bottom-a.bottom,0),zoom:c}},O=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0;function B(t){return void 0!==t&&"parent"!==t}function D(t){return{width:t.measured?.width??t.width??t.initialWidth??0,height:t.measured?.height??t.height??t.initialHeight??0}}function L(t,{snapGrid:e=[0,0],snapToGrid:n=!1,transform:o,containerBounds:r}){const{x:i,y:a}=q(t),s=I({x:i-(r?.left??0),y:a-(r?.top??0)},o),{x:u,y:l}=n?T(s,e):s;return{xSnapped:u,ySnapped:l,...s}}const Y=t=>({width:t.offsetWidth,height:t.offsetHeight}),X=t=>t?.getRootNode?.()||window?.document,R=["INPUT","SELECT","TEXTAREA"];const V=t=>"clientX"in t,q=(t,e)=>{const n=V(t),o=n?t.clientX:t.touches?.[0].clientX,r=n?t.clientY:t.touches?.[0].clientY;return{x:o-(e?.left??0),y:r-(e?.top??0)}},Z=(t,e,n,o,r)=>{const i=e.querySelectorAll(`.${t}`);return i&&i.length?Array.from(i).map((e=>{const i=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),type:t,nodeId:r,position:e.getAttribute("data-handlepos"),x:(i.left-n.left)/o,y:(i.top-n.top)/o,...Y(e)}})):null};function G({sourceX:t,sourceY:e,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:a,targetControlY:s}){const u=.125*t+.375*r+.375*a+.125*n,l=.125*e+.375*i+.375*s+.125*o;return[u,l,Math.abs(u-t),Math.abs(l-e)]}function j(t,e){return t>=0?.5*t:25*e*Math.sqrt(-t)}function F({pos:e,x1:n,y1:o,x2:r,y2:i,c:a}){switch(e){case t.Position.Left:return[n-j(n-r,a),o];case t.Position.Right:return[n+j(r-n,a),o];case t.Position.Top:return[n,o-j(o-i,a)];case t.Position.Bottom:return[n,o+j(i-o,a)]}}function W({sourceX:t,sourceY:e,targetX:n,targetY:o}){const r=Math.abs(n-t)/2,i=n<t?n+r:n-r,a=Math.abs(o-e)/2;return[i,o<e?o+a:o-a,r,a]}const K=({source:t,sourceHandle:e,target:n,targetHandle:o})=>`xy-edge__${t}${e||""}-${n}${o||""}`;const U={[t.Position.Left]:{x:-1,y:0},[t.Position.Right]:{x:1,y:0},[t.Position.Top]:{x:0,y:-1},[t.Position.Bottom]:{x:0,y:1}},Q=({source:e,sourcePosition:n=t.Position.Bottom,target:o})=>n===t.Position.Left||n===t.Position.Right?e.x<o.x?{x:1,y:0}:{x:-1,y:0}:e.y<o.y?{x:0,y:1}:{x:0,y:-1},J=(t,e)=>Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2));function tt(t){return t&&!(!t.internals.handleBounds&&!t.handles?.length)&&!!(t.measured.width||t.width||t.initialWidth)}function et(t){if(!t)return null;const e=[],n=[];for(const o of t)o.width=o.width??1,o.height=o.height??1,"source"===o.type?e.push(o):"target"===o.type&&n.push(o);return{source:e,target:n}}function nt(e,n,o=t.Position.Left,r=!1){const i=(n?.x??0)+e.internals.positionAbsolute.x,a=(n?.y??0)+e.internals.positionAbsolute.y,{width:s,height:u}=n??D(e);if(r)return{x:i+s/2,y:a+u/2};switch(n?.position??o){case t.Position.Top:return{x:i+s/2,y:a};case t.Position.Right:return{x:i+s,y:a+u/2};case t.Position.Bottom:return{x:i+s/2,y:a+u};case t.Position.Left:return{x:i,y:a+u/2}}}function ot(t,e){return t&&(e?t.find((t=>t.id===e)):t[0])||null}function rt(t,e){if(!t)return"";if("string"==typeof t)return t;return`${e?`${e}__`:""}${Object.keys(t).sort().map((e=>`${e}=${t[e]}`)).join("&")}`}const it={nodeOrigin:[0,0],nodeExtent:n,elevateNodesOnSelect:!0,defaults:{}},at={...it,checkEquality:!0};function st(t,e){const n={...t};for(const t in e)void 0!==e[t]&&(n[t]=e[t]);return n}function ut(t,e,n,o){const{elevateNodesOnSelect:r,nodeOrigin:i,nodeExtent:a}=st(it,o),s=t.parentId,u=e.get(s);if(!u)return void console.warn(`Parent node ${s} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);!function(t,e){if(!t.parentId)return;const n=e.get(t.parentId);n?n.set(t.id,t):e.set(t.parentId,new Map([[t.id,t]]))}(t,n);const l=r?1e3:0,{x:c,y:h,z:d}=function(t,e,n,o,r){const{x:i,y:a}=e.internals.positionAbsolute,s=D(t),u=f(t,n),l=B(t.extent)?v(u,t.extent,s):u;let c=v({x:i+l.x,y:a+l.y},o,s);"parent"===t.extent&&(c=x(c,s,e));const h=lt(t,r),d=e.internals.z??0;return{x:c.x,y:c.y,z:d>h?d:h}}(t,u,i,a,l),{positionAbsolute:p}=t.internals,g=c!==p.x||h!==p.y;(g||d!==t.internals.z)&&e.set(t.id,{...t,internals:{...t.internals,positionAbsolute:g?{x:c,y:h}:p,z:d}})}function lt(t,e){return(k(t.zIndex)?t.zIndex:0)+(t.selected?e:0)}function ct(t,e,n,o=[0,0]){const r=[],i=new Map;for(const n of t){const t=e.get(n.parentId);if(!t)continue;const o=i.get(n.parentId)?.expandedRect??E(t),r=N(o,n.rect);i.set(n.parentId,{expandedRect:r,parent:t})}return i.size>0&&i.forEach((({expandedRect:e,parent:i},a)=>{const s=i.internals.positionAbsolute,u=D(i),l=i.origin??o,c=e.x<s.x?Math.round(Math.abs(s.x-e.x)):0,h=e.y<s.y?Math.round(Math.abs(s.y-e.y)):0,d=Math.max(u.width,Math.round(e.width)),f=Math.max(u.height,Math.round(e.height)),p=(d-u.width)*l[0],g=(f-u.height)*l[1];(c>0||h>0||p||g)&&(r.push({id:a,type:"position",position:{x:i.position.x-c+p,y:i.position.y-h+g}}),n.get(a)?.forEach((e=>{t.some((t=>t.id===e.id))||r.push({id:e.id,type:"position",position:{x:e.position.x+c,y:e.position.y+h}})}))),(u.width<e.width||u.height<e.height||c||h)&&r.push({id:a,type:"dimensions",setAttributes:!0,dimensions:{width:d+(c?l[0]*c-p:0),height:f+(h?l[1]*h-g:0)}})})),r}function ht(t,e,n,o,r,i){let a=r;const s=o.get(a)||new Map;o.set(a,s.set(n,e)),a=`${r}-${t}`;const u=o.get(a)||new Map;if(o.set(a,u.set(n,e)),i){a=`${r}-${t}-${i}`;const s=o.get(a)||new Map;o.set(a,s.set(n,e))}}var dt={value:()=>{}};function ft(){for(var t,e=0,n=arguments.length,o={};e<n;++e){if(!(t=arguments[e]+"")||t in o||/[\s.]/.test(t))throw new Error("illegal type: "+t);o[t]=[]}return new pt(o)}function pt(t){this._=t}function gt(t,e){for(var n,o=0,r=t.length;o<r;++o)if((n=t[o]).name===e)return n.value}function mt(t,e,n){for(var o=0,r=t.length;o<r;++o)if(t[o].name===e){t[o]=dt,t=t.slice(0,o).concat(t.slice(o+1));break}return null!=n&&t.push({name:e,value:n}),t}pt.prototype=ft.prototype={constructor:pt,on:function(t,e){var n,o,r=this._,i=(o=r,(t+"").trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!o.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))),a=-1,s=i.length;if(!(arguments.length<2)){if(null!=e&&"function"!=typeof e)throw new Error("invalid callback: "+e);for(;++a<s;)if(n=(t=i[a]).type)r[n]=mt(r[n],t.name,e);else if(null==e)for(n in r)r[n]=mt(r[n],t.name,null);return this}for(;++a<s;)if((n=(t=i[a]).type)&&(n=gt(r[n],t.name)))return n},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new pt(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(i=0,n=(o=this._[t]).length;i<n;++i)o[i].value.apply(e,r)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var o=this._[t],r=0,i=o.length;r<i;++r)o[r].value.apply(e,n)}};var yt="http://www.w3.org/1999/xhtml",vt={svg:"http://www.w3.org/2000/svg",xhtml:yt,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function xt(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),vt.hasOwnProperty(e)?{space:vt[e],local:t}:t}function wt(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===yt&&e.documentElement.namespaceURI===yt?e.createElement(t):e.createElementNS(n,t)}}function _t(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function bt(t){var e=xt(t);return(e.local?_t:wt)(e)}function Mt(){}function Pt(t){return null==t?Mt:function(){return this.querySelector(t)}}function Et(){return[]}function St(t){return null==t?Et:function(){return this.querySelectorAll(t)}}function Nt(t){return function(){return null==(e=t.apply(this,arguments))?[]:Array.isArray(e)?e:Array.from(e);var e}}function zt(t){return function(){return this.matches(t)}}function kt(t){return function(e){return e.matches(t)}}var At=Array.prototype.find;function Tt(){return this.firstElementChild}var It=Array.prototype.filter;function $t(){return Array.from(this.children)}function Ct(t){return new Array(t.length)}function Ht(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function Ot(t,e,n,o,r,i){for(var a,s=0,u=e.length,l=i.length;s<l;++s)(a=e[s])?(a.__data__=i[s],o[s]=a):n[s]=new Ht(t,i[s]);for(;s<u;++s)(a=e[s])&&(r[s]=a)}function Bt(t,e,n,o,r,i,a){var s,u,l,c=new Map,h=e.length,d=i.length,f=new Array(h);for(s=0;s<h;++s)(u=e[s])&&(f[s]=l=a.call(u,u.__data__,s,e)+"",c.has(l)?r[s]=u:c.set(l,u));for(s=0;s<d;++s)l=a.call(t,i[s],s,i)+"",(u=c.get(l))?(o[s]=u,u.__data__=i[s],c.delete(l)):n[s]=new Ht(t,i[s]);for(s=0;s<h;++s)(u=e[s])&&c.get(f[s])===u&&(r[s]=u)}function Dt(t){return t.__data__}function Lt(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function Yt(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}function Xt(t){return function(){this.removeAttribute(t)}}function Rt(t){return function(){this.removeAttributeNS(t.space,t.local)}}function Vt(t,e){return function(){this.setAttribute(t,e)}}function qt(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function Zt(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}function Gt(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}function jt(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function Ft(t){return function(){this.style.removeProperty(t)}}function Wt(t,e,n){return function(){this.style.setProperty(t,e,n)}}function Kt(t,e,n){return function(){var o=e.apply(this,arguments);null==o?this.style.removeProperty(t):this.style.setProperty(t,o,n)}}function Ut(t,e){return t.style.getPropertyValue(e)||jt(t).getComputedStyle(t,null).getPropertyValue(e)}function Qt(t){return function(){delete this[t]}}function Jt(t,e){return function(){this[t]=e}}function te(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}function ee(t){return t.trim().split(/^|\s+/)}function ne(t){return t.classList||new oe(t)}function oe(t){this._node=t,this._names=ee(t.getAttribute("class")||"")}function re(t,e){for(var n=ne(t),o=-1,r=e.length;++o<r;)n.add(e[o])}function ie(t,e){for(var n=ne(t),o=-1,r=e.length;++o<r;)n.remove(e[o])}function ae(t){return function(){re(this,t)}}function se(t){return function(){ie(this,t)}}function ue(t,e){return function(){(e.apply(this,arguments)?re:ie)(this,t)}}function le(){this.textContent=""}function ce(t){return function(){this.textContent=t}}function he(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}function de(){this.innerHTML=""}function fe(t){return function(){this.innerHTML=t}}function pe(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}function ge(){this.nextSibling&&this.parentNode.appendChild(this)}function me(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function ye(){return null}function ve(){var t=this.parentNode;t&&t.removeChild(this)}function xe(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function we(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function _e(t){return function(){var e=this.__on;if(e){for(var n,o=0,r=-1,i=e.length;o<i;++o)n=e[o],t.type&&n.type!==t.type||n.name!==t.name?e[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?e.length=r:delete this.__on}}}function be(t,e,n){return function(){var o,r=this.__on,i=function(t){return function(e){t.call(this,e,this.__data__)}}(e);if(r)for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===t.type&&o.name===t.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=e);this.addEventListener(t.type,i,n),o={type:t.type,name:t.name,value:e,listener:i,options:n},r?r.push(o):this.__on=[o]}}function Me(t,e,n){var o=jt(t),r=o.CustomEvent;"function"==typeof r?r=new r(e,n):(r=o.document.createEvent("Event"),n?(r.initEvent(e,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(e,!1,!1)),t.dispatchEvent(r)}function Pe(t,e){return function(){return Me(this,t,e)}}function Ee(t,e){return function(){return Me(this,t,e.apply(this,arguments))}}Ht.prototype={constructor:Ht,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},oe.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var Se=[null];function Ne(t,e){this._groups=t,this._parents=e}function ze(){return new Ne([[document.documentElement]],Se)}function ke(t){return"string"==typeof t?new Ne([[document.querySelector(t)]],[document.documentElement]):new Ne([[t]],Se)}function Ae(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=t.clientX,o.y=t.clientY,[(o=o.matrixTransform(e.getScreenCTM().inverse())).x,o.y]}if(e.getBoundingClientRect){var r=e.getBoundingClientRect();return[t.clientX-r.left-e.clientLeft,t.clientY-r.top-e.clientTop]}}return[t.pageX,t.pageY]}Ne.prototype=ze.prototype={constructor:Ne,select:function(t){"function"!=typeof t&&(t=Pt(t));for(var e=this._groups,n=e.length,o=new Array(n),r=0;r<n;++r)for(var i,a,s=e[r],u=s.length,l=o[r]=new Array(u),c=0;c<u;++c)(i=s[c])&&(a=t.call(i,i.__data__,c,s))&&("__data__"in i&&(a.__data__=i.__data__),l[c]=a);return new Ne(o,this._parents)},selectAll:function(t){t="function"==typeof t?Nt(t):St(t);for(var e=this._groups,n=e.length,o=[],r=[],i=0;i<n;++i)for(var a,s=e[i],u=s.length,l=0;l<u;++l)(a=s[l])&&(o.push(t.call(a,a.__data__,l,s)),r.push(a));return new Ne(o,r)},selectChild:function(t){return this.select(null==t?Tt:function(t){return function(){return At.call(this.children,t)}}("function"==typeof t?t:kt(t)))},selectChildren:function(t){return this.selectAll(null==t?$t:function(t){return function(){return It.call(this.children,t)}}("function"==typeof t?t:kt(t)))},filter:function(t){"function"!=typeof t&&(t=zt(t));for(var e=this._groups,n=e.length,o=new Array(n),r=0;r<n;++r)for(var i,a=e[r],s=a.length,u=o[r]=[],l=0;l<s;++l)(i=a[l])&&t.call(i,i.__data__,l,a)&&u.push(i);return new Ne(o,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,Dt);var n,o=e?Bt:Ot,r=this._parents,i=this._groups;"function"!=typeof t&&(n=t,t=function(){return n});for(var a=i.length,s=new Array(a),u=new Array(a),l=new Array(a),c=0;c<a;++c){var h=r[c],d=i[c],f=d.length,p=Lt(t.call(h,h&&h.__data__,c,r)),g=p.length,m=u[c]=new Array(g),y=s[c]=new Array(g);o(h,d,m,y,l[c]=new Array(f),p,e);for(var v,x,w=0,_=0;w<g;++w)if(v=m[w]){for(w>=_&&(_=w+1);!(x=y[_])&&++_<g;);v._next=x||null}}return(s=new Ne(s,r))._enter=u,s._exit=l,s},enter:function(){return new Ne(this._enter||this._groups.map(Ct),this._parents)},exit:function(){return new Ne(this._exit||this._groups.map(Ct),this._parents)},join:function(t,e,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof t?(o=t(o))&&(o=o.selection()):o=o.append(t+""),null!=e&&(r=e(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,o=e._groups,r=n.length,i=o.length,a=Math.min(r,i),s=new Array(r),u=0;u<a;++u)for(var l,c=n[u],h=o[u],d=c.length,f=s[u]=new Array(d),p=0;p<d;++p)(l=c[p]||h[p])&&(f[p]=l);for(;u<r;++u)s[u]=n[u];return new Ne(s,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var o,r=t[e],i=r.length-1,a=r[i];--i>=0;)(o=r[i])&&(a&&4^o.compareDocumentPosition(a)&&a.parentNode.insertBefore(o,a),a=o);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=Yt);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var a,s=n[i],u=s.length,l=r[i]=new Array(u),c=0;c<u;++c)(a=s[c])&&(l[c]=a);l.sort(e)}return new Ne(r,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var o=t[e],r=0,i=o.length;r<i;++r){var a=o[r];if(a)return a}return null},size:function(){let t=0;for(const e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,o=e.length;n<o;++n)for(var r,i=e[n],a=0,s=i.length;a<s;++a)(r=i[a])&&t.call(r,r.__data__,a,i);return this},attr:function(t,e){var n=xt(t);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==e?n.local?Rt:Xt:"function"==typeof e?n.local?Gt:Zt:n.local?qt:Vt)(n,e))},style:function(t,e,n){return arguments.length>1?this.each((null==e?Ft:"function"==typeof e?Kt:Wt)(t,e,null==n?"":n)):Ut(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?Qt:"function"==typeof e?te:Jt)(t,e)):this.node()[t]},classed:function(t,e){var n=ee(t+"");if(arguments.length<2){for(var o=ne(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof e?ue:e?ae:se)(n,e))},text:function(t){return arguments.length?this.each(null==t?le:("function"==typeof t?he:ce)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?de:("function"==typeof t?pe:fe)(t)):this.node().innerHTML},raise:function(){return this.each(ge)},lower:function(){return this.each(me)},append:function(t){var e="function"==typeof t?t:bt(t);return this.select((function(){return this.appendChild(e.apply(this,arguments))}))},insert:function(t,e){var n="function"==typeof t?t:bt(t),o=null==e?ye:"function"==typeof e?e:Pt(e);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each(ve)},clone:function(t){return this.select(t?we:xe)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var o,r,i=function(t){return t.trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}))}(t+""),a=i.length;if(!(arguments.length<2)){for(s=e?be:_e,o=0;o<a;++o)this.each(s(i[o],e,n));return this}var s=this.node().__on;if(s)for(var u,l=0,c=s.length;l<c;++l)for(o=0,u=s[l];o<a;++o)if((r=i[o]).type===u.type&&r.name===u.name)return u.value},dispatch:function(t,e){return this.each(("function"==typeof e?Ee:Pe)(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var o,r=t[e],i=0,a=r.length;i<a;++i)(o=r[i])&&(yield o)}};const Te={passive:!1},Ie={capture:!0,passive:!1};function $e(t){t.stopImmediatePropagation()}function Ce(t){t.preventDefault(),t.stopImmediatePropagation()}function He(t){var e=t.document.documentElement,n=ke(t).on("dragstart.drag",Ce,Ie);"onselectstart"in e?n.on("selectstart.drag",Ce,Ie):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function Oe(t,e){var n=t.document.documentElement,o=ke(t).on("dragstart.drag",null);e&&(o.on("click.drag",Ce,Ie),setTimeout((function(){o.on("click.drag",null)}),0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var Be=t=>()=>t;function De(t,{sourceEvent:e,subject:n,target:o,identifier:r,active:i,x:a,y:s,dx:u,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:u,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function Le(t){return!t.ctrlKey&&!t.button}function Ye(){return this.parentNode}function Xe(t,e){return null==e?{x:t.x,y:t.y}:e}function Re(){return navigator.maxTouchPoints||"ontouchstart"in this}function Ve(){var t,e,n,o,r=Le,i=Ye,a=Xe,s=Re,u={},l=ft("start","drag","end"),c=0,h=0;function d(t){t.on("mousedown.drag",f).filter(s).on("touchstart.drag",m).on("touchmove.drag",y,Te).on("touchend.drag touchcancel.drag",v).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(a,s){if(!o&&r.call(this,a,s)){var u=x(this,i.call(this,a,s),a,s,"mouse");u&&(ke(a.view).on("mousemove.drag",p,Ie).on("mouseup.drag",g,Ie),He(a.view),$e(a),n=!1,t=a.clientX,e=a.clientY,u("start",a))}}function p(o){if(Ce(o),!n){var r=o.clientX-t,i=o.clientY-e;n=r*r+i*i>h}u.mouse("drag",o)}function g(t){ke(t.view).on("mousemove.drag mouseup.drag",null),Oe(t.view,n),Ce(t),u.mouse("end",t)}function m(t,e){if(r.call(this,t,e)){var n,o,a=t.changedTouches,s=i.call(this,t,e),u=a.length;for(n=0;n<u;++n)(o=x(this,s,t,e,a[n].identifier,a[n]))&&($e(t),o("start",t,a[n]))}}function y(t){var e,n,o=t.changedTouches,r=o.length;for(e=0;e<r;++e)(n=u[o[e].identifier])&&(Ce(t),n("drag",t,o[e]))}function v(t){var e,n,r=t.changedTouches,i=r.length;for(o&&clearTimeout(o),o=setTimeout((function(){o=null}),500),e=0;e<i;++e)(n=u[r[e].identifier])&&($e(t),n("end",t,r[e]))}function x(t,e,n,o,r,i){var s,h,f,p=l.copy(),g=Ae(i||n,e);if(null!=(f=a.call(t,new De("beforestart",{sourceEvent:n,target:d,identifier:r,active:c,x:g[0],y:g[1],dx:0,dy:0,dispatch:p}),o)))return s=f.x-g[0]||0,h=f.y-g[1]||0,function n(i,a,l){var m,y=g;switch(i){case"start":u[r]=n,m=c++;break;case"end":delete u[r],--c;case"drag":g=Ae(l||a,e),m=c}p.call(i,t,new De(i,{sourceEvent:a,subject:f,target:d,identifier:r,active:m,x:g[0]+s,y:g[1]+h,dx:g[0]-y[0],dy:g[1]-y[1],dispatch:p}),o)}}return d.filter=function(t){return arguments.length?(r="function"==typeof t?t:Be(!!t),d):r},d.container=function(t){return arguments.length?(i="function"==typeof t?t:Be(t),d):i},d.subject=function(t){return arguments.length?(a="function"==typeof t?t:Be(t),d):a},d.touchable=function(t){return arguments.length?(s="function"==typeof t?t:Be(!!t),d):s},d.on=function(){var t=l.on.apply(l,arguments);return t===l?d:t},d.clickDistance=function(t){return arguments.length?(h=(t=+t)*t,d):Math.sqrt(h)},d}function qe(t,e){if(!t.parentId)return!1;const n=e.get(t.parentId);return!!n&&(!!n.selected||qe(n,e))}function Ze(t,e,n){let o=t;do{if(o?.matches?.(e))return!0;if(o===n)return!1;o=o?.parentElement}while(o);return!1}function Ge({nodeId:t,dragItems:e,nodeLookup:n,dragging:o=!0}){const r=[];for(const[t,i]of e){const e=n.get(t)?.internals.userNode;e&&r.push({...e,position:i.position,dragging:o})}if(!t)return[r[0],r];const i=n.get(t)?.internals.userNode;return[i?{...i,position:e.get(t)?.position||i.position,dragging:o}:r[0],r]}De.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};const je=250;function Fe(t,e,n,o){let r=[],i=1/0;const a=function(t,e,n){const o=[],r={x:t.x-n,y:t.y-n,width:2*n,height:2*n};for(const t of e.values())z(r,E(t))>0&&o.push(t);return o}(t,n,e+je);for(const n of a){const a=[...n.internals.handleBounds?.source??[],...n.internals.handleBounds?.target??[]];for(const s of a){if(o.nodeId===s.nodeId&&o.type===s.type&&o.id===s.id)continue;const{x:a,y:u}=nt(n,s,s.position,!0),l=Math.sqrt(Math.pow(a-t.x,2)+Math.pow(u-t.y,2));l>e||(l<i?(r=[{...s,x:a,y:u}],i=l):l===i&&r.push({...s,x:a,y:u}))}}if(!r.length)return null;if(r.length>1){const t="source"===o.type?"target":"source";return r.find((e=>e.type===t))??r[0]}return r[0]}function We(t,e,n,o,r,i=!1){const a=o.get(t);if(!a)return null;const s="strict"===r?a.internals.handleBounds?.[e]:[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]],u=(n?s?.find((t=>t.id===n)):s?.[0])??null;return u&&i?{...u,...nt(a,u,u.position,!0)}:u}function Ke(t,e){return t||(e?.classList.contains("target")?"target":e?.classList.contains("source")?"source":null)}const Ue=()=>!0;function Qe(e,{handle:n,connectionMode:o,fromNodeId:r,fromHandleId:i,fromType:a,doc:s,lib:u,flowId:l,isValidConnection:c=Ue,nodeLookup:h}){const d="target"===a,f=n?s.querySelector(`.${u}-flow__handle[data-id="${l}-${n?.nodeId}-${n?.id}-${n?.type}"]`):null,{x:p,y:g}=q(e),m=s.elementFromPoint(p,g),y=m?.classList.contains(`${u}-flow__handle`)?m:f,v={handleDomNode:y,isValid:!1,connection:null,toHandle:null};if(y){const e=Ke(void 0,y),n=y.getAttribute("data-nodeid"),a=y.getAttribute("data-handleid"),s=y.classList.contains("connectable"),u=y.classList.contains("connectableend");if(!n||!e)return v;const l={source:d?n:r,sourceHandle:d?a:i,target:d?r:n,targetHandle:d?i:a};v.connection=l;const f=s&&u&&(o===t.ConnectionMode.Strict?d&&"source"===e||!d&&"target"===e:n!==r||a!==i);v.isValid=f&&c(l),v.toHandle=We(n,e,a,h,o,!0)}return v}const Je={onPointerDown:function(e,{connectionMode:n,connectionRadius:o,handleId:r,nodeId:i,edgeUpdaterType:a,isTarget:s,domNode:u,nodeLookup:l,lib:h,autoPanOnConnect:d,flowId:f,panBy:p,cancelConnection:g,onConnectStart:m,onConnect:y,onConnectEnd:v,isValidConnection:x=Ue,onReconnectEnd:w,updateConnection:b,getTransform:M,getFromHandle:P,autoPanSpeed:E}){const S=X(e.target);let N,z=0;const{x:k,y:A}=q(e),T=S?.elementFromPoint(k,A),C=Ke(a,T),H=u?.getBoundingClientRect();if(!H||!C)return;const O=We(i,C,r,l,n);if(!O)return;let B=q(e,H),D=!1,L=null,Y=!1,R=null;function V(){if(!d||!H)return;const[t,e]=_(B,H,E);p({x:t,y:e}),z=requestAnimationFrame(V)}const Z={...O,nodeId:i,type:C,position:O.position},G=l.get(i),j={inProgress:!0,isValid:null,from:nt(G,Z,t.Position.Left,!0),fromHandle:Z,fromPosition:Z.position,fromNode:G,to:B,toHandle:null,toPosition:c[Z.position],toNode:null};b(j);let F=j;function W(t){if(!P()||!Z)return void K(t);const e=M();B=q(t,H),N=Fe(I(B,e,!1,[1,1]),o,l,Z),D||(V(),D=!0);const a=Qe(t,{handle:N,connectionMode:n,fromNodeId:i,fromHandleId:r,fromType:s?"target":"source",isValidConnection:x,doc:S,lib:h,flowId:f,nodeLookup:l});R=a.handleDomNode,L=a.connection,Y=function(t,e){let n=null;return e?n=!0:t&&!e&&(n=!1),n}(!!N,a.isValid);const u={...F,isValid:Y,to:a.toHandle&&Y?$({x:a.toHandle.x,y:a.toHandle.y},e):B,toHandle:a.toHandle,toPosition:Y&&a.toHandle?a.toHandle.position:c[Z.position],toNode:a.toHandle?l.get(a.toHandle.nodeId):null};Y&&N&&F.toHandle&&u.toHandle&&F.toHandle.type===u.toHandle.type&&F.toHandle.nodeId===u.toHandle.nodeId&&F.toHandle.id===u.toHandle.id&&F.to.x===u.to.x&&F.to.y===u.to.y||(b(u),F=u)}function K(t){(N||R)&&L&&Y&&y?.(L);const{inProgress:e,...n}=F,o={...n,toPosition:F.toHandle?F.toPosition:null};v?.(t,o),a&&w?.(t,o),g(),cancelAnimationFrame(z),D=!1,Y=!1,L=null,R=null,S.removeEventListener("mousemove",W),S.removeEventListener("mouseup",K),S.removeEventListener("touchmove",W),S.removeEventListener("touchend",K)}m?.(e,{nodeId:i,handleId:r,handleType:C}),S.addEventListener("mousemove",W),S.addEventListener("mouseup",K),S.addEventListener("touchmove",W),S.addEventListener("touchend",K)},isValid:Qe};function tn(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function en(t,e){var n=Object.create(t.prototype);for(var o in e)n[o]=e[o];return n}function nn(){}var on=.7,rn=1/on,an="\\s*([+-]?\\d+)\\s*",sn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",un="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ln=/^#([0-9a-f]{3,8})$/,cn=new RegExp(`^rgb\\(${an},${an},${an}\\)$`),hn=new RegExp(`^rgb\\(${un},${un},${un}\\)$`),dn=new RegExp(`^rgba\\(${an},${an},${an},${sn}\\)$`),fn=new RegExp(`^rgba\\(${un},${un},${un},${sn}\\)$`),pn=new RegExp(`^hsl\\(${sn},${un},${un}\\)$`),gn=new RegExp(`^hsla\\(${sn},${un},${un},${sn}\\)$`),mn={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function yn(){return this.rgb().formatHex()}function vn(){return this.rgb().formatRgb()}function xn(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=ln.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?wn(e):3===n?new Mn(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?_n(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?_n(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=cn.exec(t))?new Mn(e[1],e[2],e[3],1):(e=hn.exec(t))?new Mn(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=dn.exec(t))?_n(e[1],e[2],e[3],e[4]):(e=fn.exec(t))?_n(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=pn.exec(t))?kn(e[1],e[2]/100,e[3]/100,1):(e=gn.exec(t))?kn(e[1],e[2]/100,e[3]/100,e[4]):mn.hasOwnProperty(t)?wn(mn[t]):"transparent"===t?new Mn(NaN,NaN,NaN,0):null}function wn(t){return new Mn(t>>16&255,t>>8&255,255&t,1)}function _n(t,e,n,o){return o<=0&&(t=e=n=NaN),new Mn(t,e,n,o)}function bn(t,e,n,o){return 1===arguments.length?((r=t)instanceof nn||(r=xn(r)),r?new Mn((r=r.rgb()).r,r.g,r.b,r.opacity):new Mn):new Mn(t,e,n,null==o?1:o);var r}function Mn(t,e,n,o){this.r=+t,this.g=+e,this.b=+n,this.opacity=+o}function Pn(){return`#${zn(this.r)}${zn(this.g)}${zn(this.b)}`}function En(){const t=Sn(this.opacity);return`${1===t?"rgb(":"rgba("}${Nn(this.r)}, ${Nn(this.g)}, ${Nn(this.b)}${1===t?")":`, ${t})`}`}function Sn(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Nn(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function zn(t){return((t=Nn(t))<16?"0":"")+t.toString(16)}function kn(t,e,n,o){return o<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new Tn(t,e,n,o)}function An(t){if(t instanceof Tn)return new Tn(t.h,t.s,t.l,t.opacity);if(t instanceof nn||(t=xn(t)),!t)return new Tn;if(t instanceof Tn)return t;var e=(t=t.rgb()).r/255,n=t.g/255,o=t.b/255,r=Math.min(e,n,o),i=Math.max(e,n,o),a=NaN,s=i-r,u=(i+r)/2;return s?(a=e===i?(n-o)/s+6*(n<o):n===i?(o-e)/s+2:(e-n)/s+4,s/=u<.5?i+r:2-i-r,a*=60):s=u>0&&u<1?0:a,new Tn(a,s,u,t.opacity)}function Tn(t,e,n,o){this.h=+t,this.s=+e,this.l=+n,this.opacity=+o}function In(t){return(t=(t||0)%360)<0?t+360:t}function $n(t){return Math.max(0,Math.min(1,t||0))}function Cn(t,e,n){return 255*(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)}tn(nn,xn,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:yn,formatHex:yn,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return An(this).formatHsl()},formatRgb:vn,toString:vn}),tn(Mn,bn,en(nn,{brighter(t){return t=null==t?rn:Math.pow(rn,t),new Mn(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?on:Math.pow(on,t),new Mn(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Mn(Nn(this.r),Nn(this.g),Nn(this.b),Sn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Pn,formatHex:Pn,formatHex8:function(){return`#${zn(this.r)}${zn(this.g)}${zn(this.b)}${zn(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:En,toString:En})),tn(Tn,(function(t,e,n,o){return 1===arguments.length?An(t):new Tn(t,e,n,null==o?1:o)}),en(nn,{brighter(t){return t=null==t?rn:Math.pow(rn,t),new Tn(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?on:Math.pow(on,t),new Tn(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*e,r=2*n-o;return new Mn(Cn(t>=240?t-240:t+120,r,o),Cn(t,r,o),Cn(t<120?t+240:t-120,r,o),this.opacity)},clamp(){return new Tn(In(this.h),$n(this.s),$n(this.l),Sn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=Sn(this.opacity);return`${1===t?"hsl(":"hsla("}${In(this.h)}, ${100*$n(this.s)}%, ${100*$n(this.l)}%${1===t?")":`, ${t})`}`}}));var Hn=t=>()=>t;function On(t){return 1==(t=+t)?Bn:function(e,n){return n-e?function(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(o){return Math.pow(t+o*e,n)}}(e,n,t):Hn(isNaN(e)?n:e)}}function Bn(t,e){var n=e-t;return n?function(t,e){return function(n){return t+n*e}}(t,n):Hn(isNaN(t)?e:t)}var Dn=function t(e){var n=On(e);function o(t,e){var o=n((t=bn(t)).r,(e=bn(e)).r),r=n(t.g,e.g),i=n(t.b,e.b),a=Bn(t.opacity,e.opacity);return function(e){return t.r=o(e),t.g=r(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function Ln(t,e){e||(e=[]);var n,o=t?Math.min(e.length,t.length):0,r=e.slice();return function(i){for(n=0;n<o;++n)r[n]=t[n]*(1-i)+e[n]*i;return r}}function Yn(t,e){var n,o=e?e.length:0,r=t?Math.min(o,t.length):0,i=new Array(r),a=new Array(o);for(n=0;n<r;++n)i[n]=jn(t[n],e[n]);for(;n<o;++n)a[n]=e[n];return function(t){for(n=0;n<r;++n)a[n]=i[n](t);return a}}function Xn(t,e){var n=new Date;return t=+t,e=+e,function(o){return n.setTime(t*(1-o)+e*o),n}}function Rn(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}function Vn(t,e){var n,o={},r={};for(n in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)n in t?o[n]=jn(t[n],e[n]):r[n]=e[n];return function(t){for(n in o)r[n]=o[n](t);return r}}var qn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Zn=new RegExp(qn.source,"g");function Gn(t,e){var n,o,r,i=qn.lastIndex=Zn.lastIndex=0,a=-1,s=[],u=[];for(t+="",e+="";(n=qn.exec(t))&&(o=Zn.exec(e));)(r=o.index)>i&&(r=e.slice(i,r),s[a]?s[a]+=r:s[++a]=r),(n=n[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,u.push({i:a,x:Rn(n,o)})),i=Zn.lastIndex;return i<e.length&&(r=e.slice(i),s[a]?s[a]+=r:s[++a]=r),s.length<2?u[0]?function(t){return function(e){return t(e)+""}}(u[0].x):function(t){return function(){return t}}(e):(e=u.length,function(t){for(var n,o=0;o<e;++o)s[(n=u[o]).i]=n.x(t);return s.join("")})}function jn(t,e){var n,o,r=typeof e;return null==e||"boolean"===r?Hn(e):("number"===r?Rn:"string"===r?(n=xn(e))?(e=n,Dn):Gn:e instanceof xn?Dn:e instanceof Date?Xn:(o=e,!ArrayBuffer.isView(o)||o instanceof DataView?Array.isArray(e)?Yn:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?Vn:Rn:Ln))(t,e)}var Fn,Wn=180/Math.PI,Kn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Un(t,e,n,o,r,i){var a,s,u;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(u=t*n+e*o)&&(n-=t*u,o-=e*u),(s=Math.sqrt(n*n+o*o))&&(n/=s,o/=s,u/=s),t*o<e*n&&(t=-t,e=-e,u=-u,a=-a),{translateX:r,translateY:i,rotate:Math.atan2(e,t)*Wn,skewX:Math.atan(u)*Wn,scaleX:a,scaleY:s}}function Qn(t,e,n,o){function r(t){return t.length?t.pop()+" ":""}return function(i,a){var s=[],u=[];return i=t(i),a=t(a),function(t,o,r,i,a,s){if(t!==r||o!==i){var u=a.push("translate(",null,e,null,n);s.push({i:u-4,x:Rn(t,r)},{i:u-2,x:Rn(o,i)})}else(r||i)&&a.push("translate("+r+e+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,s,u),function(t,e,n,i){t!==e?(t-e>180?e+=360:e-t>180&&(t+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:Rn(t,e)})):e&&n.push(r(n)+"rotate("+e+o)}(i.rotate,a.rotate,s,u),function(t,e,n,i){t!==e?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:Rn(t,e)}):e&&n.push(r(n)+"skewX("+e+o)}(i.skewX,a.skewX,s,u),function(t,e,n,o,i,a){if(t!==n||e!==o){var s=i.push(r(i)+"scale(",null,",",null,")");a.push({i:s-4,x:Rn(t,n)},{i:s-2,x:Rn(e,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,s,u),i=a=null,function(t){for(var e,n=-1,o=u.length;++n<o;)s[(e=u[n]).i]=e.x(t);return s.join("")}}}var Jn=Qn((function(t){const e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?Kn:Un(e.a,e.b,e.c,e.d,e.e,e.f)}),"px, ","px)","deg)"),to=Qn((function(t){return null==t?Kn:(Fn||(Fn=document.createElementNS("http://www.w3.org/2000/svg","g")),Fn.setAttribute("transform",t),(t=Fn.transform.baseVal.consolidate())?Un((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):Kn)}),", ",")",")");function eo(t){return((t=Math.exp(t))+1/t)/2}var no,oo,ro=function t(e,n,o){function r(t,r){var i,a,s=t[0],u=t[1],l=t[2],c=r[0],h=r[1],d=r[2],f=c-s,p=h-u,g=f*f+p*p;if(g<1e-12)a=Math.log(d/l)/e,i=function(t){return[s+t*f,u+t*p,l*Math.exp(e*t*a)]};else{var m=Math.sqrt(g),y=(d*d-l*l+o*g)/(2*l*n*m),v=(d*d-l*l-o*g)/(2*d*n*m),x=Math.log(Math.sqrt(y*y+1)-y),w=Math.log(Math.sqrt(v*v+1)-v);a=(w-x)/e,i=function(t){var o,r=t*a,i=eo(x),c=l/(n*m)*(i*(o=e*r+x,((o=Math.exp(2*o))-1)/(o+1))-function(t){return((t=Math.exp(t))-1/t)/2}(x));return[s+c*f,u+c*p,l*i/eo(e*r+x)]}}return i.duration=1e3*a*e/Math.SQRT2,i}return r.rho=function(e){var n=Math.max(.001,+e),o=n*n;return t(n,o,o*o)},r}(Math.SQRT2,2,4),io=0,ao=0,so=0,uo=1e3,lo=0,co=0,ho=0,fo="object"==typeof performance&&performance.now?performance:Date,po="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function go(){return co||(po(mo),co=fo.now()+ho)}function mo(){co=0}function yo(){this._call=this._time=this._next=null}function vo(t,e,n){var o=new yo;return o.restart(t,e,n),o}function xo(){co=(lo=fo.now())+ho,io=ao=0;try{!function(){go(),++io;for(var t,e=no;e;)(t=co-e._time)>=0&&e._call.call(void 0,t),e=e._next;--io}()}finally{io=0,function(){var t,e,n=no,o=1/0;for(;n;)n._call?(o>n._time&&(o=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:no=e);oo=t,_o(o)}(),co=0}}function wo(){var t=fo.now(),e=t-lo;e>uo&&(ho-=e,lo=t)}function _o(t){io||(ao&&(ao=clearTimeout(ao)),t-co>24?(t<1/0&&(ao=setTimeout(xo,t-fo.now()-ho)),so&&(so=clearInterval(so))):(so||(lo=fo.now(),so=setInterval(wo,uo)),io=1,po(xo)))}function bo(t,e,n){var o=new yo;return e=null==e?0:+e,o.restart((n=>{o.stop(),t(n+e)}),e,n),o}yo.prototype=vo.prototype={constructor:yo,restart:function(t,e,n){if("function"!=typeof t)throw new TypeError("callback is not a function");n=(null==n?go():+n)+(null==e?0:+e),this._next||oo===this||(oo?oo._next=this:no=this,oo=this),this._call=t,this._time=n,_o()},stop:function(){this._call&&(this._call=null,this._time=1/0,_o())}};var Mo=ft("start","end","cancel","interrupt"),Po=[],Eo=0,So=1,No=2,zo=3,ko=4,Ao=5,To=6;function Io(t,e,n,o,r,i){var a=t.__transition;if(a){if(n in a)return}else t.__transition={};!function(t,e,n){var o,r=t.__transition;function i(t){n.state=So,n.timer.restart(a,n.delay,n.time),n.delay<=t&&a(t-n.delay)}function a(i){var l,c,h,d;if(n.state!==So)return u();for(l in r)if((d=r[l]).name===n.name){if(d.state===zo)return bo(a);d.state===ko?(d.state=To,d.timer.stop(),d.on.call("interrupt",t,t.__data__,d.index,d.group),delete r[l]):+l<e&&(d.state=To,d.timer.stop(),d.on.call("cancel",t,t.__data__,d.index,d.group),delete r[l])}if(bo((function(){n.state===zo&&(n.state=ko,n.timer.restart(s,n.delay,n.time),s(i))})),n.state=No,n.on.call("start",t,t.__data__,n.index,n.group),n.state===No){for(n.state=zo,o=new Array(h=n.tween.length),l=0,c=-1;l<h;++l)(d=n.tween[l].value.call(t,t.__data__,n.index,n.group))&&(o[++c]=d);o.length=c+1}}function s(e){for(var r=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(u),n.state=Ao,1),i=-1,a=o.length;++i<a;)o[i].call(t,r);n.state===Ao&&(n.on.call("end",t,t.__data__,n.index,n.group),u())}function u(){for(var o in n.state=To,n.timer.stop(),delete r[e],r)return;delete t.__transition}r[e]=n,n.timer=vo(i,0,n.time)}(t,n,{name:e,index:o,group:r,on:Mo,tween:Po,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:Eo})}function $o(t,e){var n=Ho(t,e);if(n.state>Eo)throw new Error("too late; already scheduled");return n}function Co(t,e){var n=Ho(t,e);if(n.state>zo)throw new Error("too late; already running");return n}function Ho(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw new Error("transition not found");return n}function Oo(t,e){var n,o,r,i=t.__transition,a=!0;if(i){for(r in e=null==e?null:e+"",i)(n=i[r]).name===e?(o=n.state>No&&n.state<Ao,n.state=To,n.timer.stop(),n.on.call(o?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete i[r]):a=!1;a&&delete t.__transition}}function Bo(t,e){var n,o;return function(){var r=Co(this,t),i=r.tween;if(i!==n)for(var a=0,s=(o=n=i).length;a<s;++a)if(o[a].name===e){(o=o.slice()).splice(a,1);break}r.tween=o}}function Do(t,e,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=Co(this,t),a=i.tween;if(a!==o){r=(o=a).slice();for(var s={name:e,value:n},u=0,l=r.length;u<l;++u)if(r[u].name===e){r[u]=s;break}u===l&&r.push(s)}i.tween=r}}function Lo(t,e,n){var o=t._id;return t.each((function(){var t=Co(this,o);(t.value||(t.value={}))[e]=n.apply(this,arguments)})),function(t){return Ho(t,o).value[e]}}function Yo(t,e){var n;return("number"==typeof e?Rn:e instanceof xn?Dn:(n=xn(e))?(e=n,Dn):Gn)(t,e)}function Xo(t){return function(){this.removeAttribute(t)}}function Ro(t){return function(){this.removeAttributeNS(t.space,t.local)}}function Vo(t,e,n){var o,r,i=n+"";return function(){var a=this.getAttribute(t);return a===i?null:a===o?r:r=e(o=a,n)}}function qo(t,e,n){var o,r,i=n+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===i?null:a===o?r:r=e(o=a,n)}}function Zo(t,e,n){var o,r,i;return function(){var a,s,u=n(this);if(null!=u)return(a=this.getAttribute(t))===(s=u+"")?null:a===o&&s===r?i:(r=s,i=e(o=a,u));this.removeAttribute(t)}}function Go(t,e,n){var o,r,i;return function(){var a,s,u=n(this);if(null!=u)return(a=this.getAttributeNS(t.space,t.local))===(s=u+"")?null:a===o&&s===r?i:(r=s,i=e(o=a,u));this.removeAttributeNS(t.space,t.local)}}function jo(t,e){var n,o;function r(){var r=e.apply(this,arguments);return r!==o&&(n=(o=r)&&function(t,e){return function(n){this.setAttributeNS(t.space,t.local,e.call(this,n))}}(t,r)),n}return r._value=e,r}function Fo(t,e){var n,o;function r(){var r=e.apply(this,arguments);return r!==o&&(n=(o=r)&&function(t,e){return function(n){this.setAttribute(t,e.call(this,n))}}(t,r)),n}return r._value=e,r}function Wo(t,e){return function(){$o(this,t).delay=+e.apply(this,arguments)}}function Ko(t,e){return e=+e,function(){$o(this,t).delay=e}}function Uo(t,e){return function(){Co(this,t).duration=+e.apply(this,arguments)}}function Qo(t,e){return e=+e,function(){Co(this,t).duration=e}}var Jo=ze.prototype.constructor;function tr(t){return function(){this.style.removeProperty(t)}}var er=0;function nr(t,e,n,o){this._groups=t,this._parents=e,this._name=n,this._id=o}function or(){return++er}var rr=ze.prototype;nr.prototype={constructor:nr,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=Pt(t));for(var o=this._groups,r=o.length,i=new Array(r),a=0;a<r;++a)for(var s,u,l=o[a],c=l.length,h=i[a]=new Array(c),d=0;d<c;++d)(s=l[d])&&(u=t.call(s,s.__data__,d,l))&&("__data__"in s&&(u.__data__=s.__data__),h[d]=u,Io(h[d],e,n,d,h,Ho(s,n)));return new nr(i,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=St(t));for(var o=this._groups,r=o.length,i=[],a=[],s=0;s<r;++s)for(var u,l=o[s],c=l.length,h=0;h<c;++h)if(u=l[h]){for(var d,f=t.call(u,u.__data__,h,l),p=Ho(u,n),g=0,m=f.length;g<m;++g)(d=f[g])&&Io(d,e,n,g,f,p);i.push(f),a.push(u)}return new nr(i,a,e,n)},selectChild:rr.selectChild,selectChildren:rr.selectChildren,filter:function(t){"function"!=typeof t&&(t=zt(t));for(var e=this._groups,n=e.length,o=new Array(n),r=0;r<n;++r)for(var i,a=e[r],s=a.length,u=o[r]=[],l=0;l<s;++l)(i=a[l])&&t.call(i,i.__data__,l,a)&&u.push(i);return new nr(o,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var e=this._groups,n=t._groups,o=e.length,r=n.length,i=Math.min(o,r),a=new Array(o),s=0;s<i;++s)for(var u,l=e[s],c=n[s],h=l.length,d=a[s]=new Array(h),f=0;f<h;++f)(u=l[f]||c[f])&&(d[f]=u);for(;s<o;++s)a[s]=e[s];return new nr(a,this._parents,this._name,this._id)},selection:function(){return new Jo(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=or(),o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],u=s.length,l=0;l<u;++l)if(a=s[l]){var c=Ho(a,e);Io(a,t,n,l,s,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new nr(o,this._parents,t,n)},call:rr.call,nodes:rr.nodes,node:rr.node,size:rr.size,empty:rr.empty,each:rr.each,on:function(t,e){var n=this._id;return arguments.length<2?Ho(this.node(),n).on.on(t):this.each(function(t,e,n){var o,r,i=function(t){return(t+"").trim().split(/^|\s+/).every((function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t}))}(e)?$o:Co;return function(){var a=i(this,t),s=a.on;s!==o&&(r=(o=s).copy()).on(e,n),a.on=r}}(n,t,e))},attr:function(t,e){var n=xt(t),o="transform"===n?to:Yo;return this.attrTween(t,"function"==typeof e?(n.local?Go:Zo)(n,o,Lo(this,"attr."+t,e)):null==e?(n.local?Ro:Xo)(n):(n.local?qo:Vo)(n,o,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw new Error;var o=xt(t);return this.tween(n,(o.local?jo:Fo)(o,e))},style:function(t,e,n){var o="transform"==(t+="")?Jn:Yo;return null==e?this.styleTween(t,function(t,e){var n,o,r;return function(){var i=Ut(this,t),a=(this.style.removeProperty(t),Ut(this,t));return i===a?null:i===n&&a===o?r:r=e(n=i,o=a)}}(t,o)).on("end.style."+t,tr(t)):"function"==typeof e?this.styleTween(t,function(t,e,n){var o,r,i;return function(){var a=Ut(this,t),s=n(this),u=s+"";return null==s&&(this.style.removeProperty(t),u=s=Ut(this,t)),a===u?null:a===o&&u===r?i:(r=u,i=e(o=a,s))}}(t,o,Lo(this,"style."+t,e))).each(function(t,e){var n,o,r,i,a="style."+e,s="end."+a;return function(){var u=Co(this,t),l=u.on,c=null==u.value[a]?i||(i=tr(e)):void 0;l===n&&r===c||(o=(n=l).copy()).on(s,r=c),u.on=o}}(this._id,t)):this.styleTween(t,function(t,e,n){var o,r,i=n+"";return function(){var a=Ut(this,t);return a===i?null:a===o?r:r=e(o=a,n)}}(t,o,e),n).on("end.style."+t,null)},styleTween:function(t,e,n){var o="style."+(t+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==e)return this.tween(o,null);if("function"!=typeof e)throw new Error;return this.tween(o,function(t,e,n){var o,r;function i(){var i=e.apply(this,arguments);return i!==r&&(o=(r=i)&&function(t,e,n){return function(o){this.style.setProperty(t,e.call(this,o),n)}}(t,i,n)),o}return i._value=e,i}(t,e,null==n?"":n))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var e=t(this);this.textContent=null==e?"":e}}(Lo(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw new Error;return this.tween(e,function(t){var e,n;function o(){var o=t.apply(this,arguments);return o!==n&&(e=(n=o)&&function(t){return function(e){this.textContent=t.call(this,e)}}(o)),e}return o._value=t,o}(t))},remove:function(){return this.on("end.remove",function(t){return function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}}(this._id))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var o,r=Ho(this.node(),n).tween,i=0,a=r.length;i<a;++i)if((o=r[i]).name===t)return o.value;return null}return this.each((null==e?Bo:Do)(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?Wo:Ko)(e,t)):Ho(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?Uo:Qo)(e,t)):Ho(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw new Error;return function(){Co(this,t).ease=e}}(e,t)):Ho(this.node(),e).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each(function(t,e){return function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw new Error;Co(this,t).ease=n}}(this._id,t))},end:function(){var t,e,n=this,o=n._id,r=n.size();return new Promise((function(i,a){var s={value:a},u={value:function(){0==--r&&i()}};n.each((function(){var n=Co(this,o),r=n.on;r!==t&&((e=(t=r).copy())._.cancel.push(s),e._.interrupt.push(s),e._.end.push(u)),n.on=e})),0===r&&i()}))},[Symbol.iterator]:rr[Symbol.iterator]};var ir={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function ar(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw new Error(`transition ${e} not found`);return n}ze.prototype.interrupt=function(t){return this.each((function(){Oo(this,t)}))},ze.prototype.transition=function(t){var e,n;t instanceof nr?(e=t._id,t=t._name):(e=or(),(n=ir).time=go(),t=null==t?null:t+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],u=s.length,l=0;l<u;++l)(a=s[l])&&Io(a,t,e,l,s,n||ar(a,e));return new nr(o,this._parents,t,e)};var sr=t=>()=>t;function ur(t,{sourceEvent:e,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function lr(t,e,n){this.k=t,this.x=e,this.y=n}lr.prototype={constructor:lr,scale:function(t){return 1===t?this:new lr(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new lr(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var cr=new lr(1,0,0);function hr(t){for(;!t.__zoom;)if(!(t=t.parentNode))return cr;return t.__zoom}function dr(t){t.stopImmediatePropagation()}function fr(t){t.preventDefault(),t.stopImmediatePropagation()}function pr(t){return!(t.ctrlKey&&"wheel"!==t.type||t.button)}function gr(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function mr(){return this.__zoom||cr}function yr(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function vr(){return navigator.maxTouchPoints||"ontouchstart"in this}function xr(t,e,n){var o=t.invertX(e[0][0])-n[0][0],r=t.invertX(e[1][0])-n[1][0],i=t.invertY(e[0][1])-n[0][1],a=t.invertY(e[1][1])-n[1][1];return t.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function wr(){var t,e,n,o=pr,r=gr,i=xr,a=yr,s=vr,u=[0,1/0],l=[[-1/0,-1/0],[1/0,1/0]],c=250,h=ro,d=ft("start","zoom","end"),f=500,p=150,g=0,m=10;function y(t){t.property("__zoom",mr).on("wheel.zoom",P,{passive:!1}).on("mousedown.zoom",E).on("dblclick.zoom",S).filter(s).on("touchstart.zoom",N).on("touchmove.zoom",z).on("touchend.zoom touchcancel.zoom",k).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function v(t,e){return(e=Math.max(u[0],Math.min(u[1],e)))===t.k?t:new lr(e,t.x,t.y)}function x(t,e,n){var o=e[0]-n[0]*t.k,r=e[1]-n[1]*t.k;return o===t.x&&r===t.y?t:new lr(t.k,o,r)}function w(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function _(t,e,n,o){t.on("start.zoom",(function(){b(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){b(this,arguments).event(o).end()})).tween("zoom",(function(){var t=this,i=arguments,a=b(t,i).event(o),s=r.apply(t,i),u=null==n?w(s):"function"==typeof n?n.apply(t,i):n,l=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),c=t.__zoom,d="function"==typeof e?e.apply(t,i):e,f=h(c.invert(u).concat(l/c.k),d.invert(u).concat(l/d.k));return function(t){if(1===t)t=d;else{var e=f(t),n=l/e[2];t=new lr(n,u[0]-e[0]*n,u[1]-e[1]*n)}a.zoom(null,t)}}))}function b(t,e,n){return!n&&t.__zooming||new M(t,e)}function M(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=r.apply(t,e),this.taps=0}function P(t,...e){if(o.apply(this,arguments)){var n=b(this,e).event(t),r=this.__zoom,s=Math.max(u[0],Math.min(u[1],r.k*Math.pow(2,a.apply(this,arguments)))),c=Ae(t);if(n.wheel)n.mouse[0][0]===c[0]&&n.mouse[0][1]===c[1]||(n.mouse[1]=r.invert(n.mouse[0]=c)),clearTimeout(n.wheel);else{if(r.k===s)return;n.mouse=[c,r.invert(c)],Oo(this),n.start()}fr(t),n.wheel=setTimeout((function(){n.wheel=null,n.end()}),p),n.zoom("mouse",i(x(v(r,s),n.mouse[0],n.mouse[1]),n.extent,l))}}function E(t,...e){if(!n&&o.apply(this,arguments)){var r=t.currentTarget,a=b(this,e,!0).event(t),s=ke(t.view).on("mousemove.zoom",(function(t){if(fr(t),!a.moved){var e=t.clientX-c,n=t.clientY-h;a.moved=e*e+n*n>g}a.event(t).zoom("mouse",i(x(a.that.__zoom,a.mouse[0]=Ae(t,r),a.mouse[1]),a.extent,l))}),!0).on("mouseup.zoom",(function(t){s.on("mousemove.zoom mouseup.zoom",null),Oe(t.view,a.moved),fr(t),a.event(t).end()}),!0),u=Ae(t,r),c=t.clientX,h=t.clientY;He(t.view),dr(t),a.mouse=[u,this.__zoom.invert(u)],Oo(this),a.start()}}function S(t,...e){if(o.apply(this,arguments)){var n=this.__zoom,a=Ae(t.changedTouches?t.changedTouches[0]:t,this),s=n.invert(a),u=n.k*(t.shiftKey?.5:2),h=i(x(v(n,u),a,s),r.apply(this,e),l);fr(t),c>0?ke(this).transition().duration(c).call(_,h,a,t):ke(this).call(y.transform,h,a,t)}}function N(n,...r){if(o.apply(this,arguments)){var i,a,s,u,l=n.touches,c=l.length,h=b(this,r,n.changedTouches.length===c).event(n);for(dr(n),a=0;a<c;++a)u=[u=Ae(s=l[a],this),this.__zoom.invert(u),s.identifier],h.touch0?h.touch1||h.touch0[2]===u[2]||(h.touch1=u,h.taps=0):(h.touch0=u,i=!0,h.taps=1+!!t);t&&(t=clearTimeout(t)),i&&(h.taps<2&&(e=u[0],t=setTimeout((function(){t=null}),f)),Oo(this),h.start())}}function z(t,...e){if(this.__zooming){var n,o,r,a,s=b(this,e).event(t),u=t.changedTouches,c=u.length;for(fr(t),n=0;n<c;++n)r=Ae(o=u[n],this),s.touch0&&s.touch0[2]===o.identifier?s.touch0[0]=r:s.touch1&&s.touch1[2]===o.identifier&&(s.touch1[0]=r);if(o=s.that.__zoom,s.touch1){var h=s.touch0[0],d=s.touch0[1],f=s.touch1[0],p=s.touch1[1],g=(g=f[0]-h[0])*g+(g=f[1]-h[1])*g,m=(m=p[0]-d[0])*m+(m=p[1]-d[1])*m;o=v(o,Math.sqrt(g/m)),r=[(h[0]+f[0])/2,(h[1]+f[1])/2],a=[(d[0]+p[0])/2,(d[1]+p[1])/2]}else{if(!s.touch0)return;r=s.touch0[0],a=s.touch0[1]}s.zoom("touch",i(x(o,r,a),s.extent,l))}}function k(t,...o){if(this.__zooming){var r,i,a=b(this,o).event(t),s=t.changedTouches,u=s.length;for(dr(t),n&&clearTimeout(n),n=setTimeout((function(){n=null}),f),r=0;r<u;++r)i=s[r],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=Ae(i,this),Math.hypot(e[0]-i[0],e[1]-i[1])<m)){var l=ke(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return y.transform=function(t,e,n,o){var r=t.selection?t.selection():t;r.property("__zoom",mr),t!==r?_(t,e,n,o):r.interrupt().each((function(){b(this,arguments).event(o).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()}))},y.scaleBy=function(t,e,n,o){y.scaleTo(t,(function(){return this.__zoom.k*("function"==typeof e?e.apply(this,arguments):e)}),n,o)},y.scaleTo=function(t,e,n,o){y.transform(t,(function(){var t=r.apply(this,arguments),o=this.__zoom,a=null==n?w(t):"function"==typeof n?n.apply(this,arguments):n,s=o.invert(a),u="function"==typeof e?e.apply(this,arguments):e;return i(x(v(o,u),a,s),t,l)}),n,o)},y.translateBy=function(t,e,n,o){y.transform(t,(function(){return i(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),l)}),null,o)},y.translateTo=function(t,e,n,o,a){y.transform(t,(function(){var t=r.apply(this,arguments),a=this.__zoom,s=null==o?w(t):"function"==typeof o?o.apply(this,arguments):o;return i(cr.translate(s[0],s[1]).scale(a.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,l)}),o,a)},M.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=ke(this.that).datum();d.call(t,this.that,new ur(t,{sourceEvent:this.sourceEvent,target:y,type:t,transform:this.that.__zoom,dispatch:d}),e)}},y.wheelDelta=function(t){return arguments.length?(a="function"==typeof t?t:sr(+t),y):a},y.filter=function(t){return arguments.length?(o="function"==typeof t?t:sr(!!t),y):o},y.touchable=function(t){return arguments.length?(s="function"==typeof t?t:sr(!!t),y):s},y.extent=function(t){return arguments.length?(r="function"==typeof t?t:sr([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),y):r},y.scaleExtent=function(t){return arguments.length?(u[0]=+t[0],u[1]=+t[1],y):[u[0],u[1]]},y.translateExtent=function(t){return arguments.length?(l[0][0]=+t[0][0],l[1][0]=+t[1][0],l[0][1]=+t[0][1],l[1][1]=+t[1][1],y):[[l[0][0],l[0][1]],[l[1][0],l[1][1]]]},y.constrain=function(t){return arguments.length?(i=t,y):i},y.duration=function(t){return arguments.length?(c=+t,y):c},y.interpolate=function(t){return arguments.length?(h=t,y):h},y.on=function(){var t=d.on.apply(d,arguments);return t===d?y:t},y.clickDistance=function(t){return arguments.length?(g=(t=+t)*t,y):Math.sqrt(g)},y.tapDistance=function(t){return arguments.length?(m=+t,y):m},y}hr.prototype=lr.prototype;const _r=(t,e)=>t.x!==e.x||t.y!==e.y||t.zoom!==e.k,br=t=>({x:t.x,y:t.y,zoom:t.k}),Mr=({x:t,y:e,zoom:n})=>cr.translate(t,e).scale(n),Pr=(t,e)=>t.target.closest(`.${e}`),Er=(t,e)=>2===e&&Array.isArray(t)&&t.includes(2),Sr=t=>((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2,Nr=(t,e=0,n=Sr,o=(()=>{}))=>{const r="number"==typeof e&&e>0;return r||o(),r?t.transition().duration(e).ease(n).on("end",o):t},zr=t=>{const e=t.ctrlKey&&O()?10:1;return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*e};var kr;t.ResizeControlVariant=void 0,(kr=t.ResizeControlVariant||(t.ResizeControlVariant={})).Line="line",kr.Handle="handle";function Ar(t,e){return Math.max(0,e-t)}function Tr(t,e){return Math.max(0,t-e)}function Ir(t,e,n){return Math.max(0,e-t,t-n)}function $r(t,e){return t?!e:e}const Cr={width:0,height:0,x:0,y:0},Hr={...Cr,pointerX:0,pointerY:0,aspectRatio:1};function Or(t,e,n){const o=e.position.x+t.position.x,r=e.position.y+t.position.y,i=t.measured.width??0,a=t.measured.height??0,s=n[0]*i,u=n[1]*a;return[[o-s,r-u],[o+i-s,r+a-u]]}t.XYDrag=function({onNodeMouseDown:t,getStoreItems:e,onDragStart:n,onDrag:o,onDragStop:r}){let i={x:null,y:null},a=0,s=new Map,u=!1,l={x:0,y:0},c=null,h=!1,d=null,f=!1;return{update:function({noDragClassName:g,handleSelector:y,domNode:v,isSelectable:x,nodeId:w,nodeClickDistance:b=0}){function P({x:t,y:n},r){const{nodeLookup:a,nodeExtent:u,snapGrid:l,snapToGrid:c,nodeOrigin:h,onNodeDrag:d,onSelectionDrag:f,onError:g,updateNodePositions:y}=e();i={x:t,y:n};let v=!1,x={x:0,y:0,x2:0,y2:0};if(s.size>1&&u){const t=p(s);x=M(t)}for(const[e,o]of s){if(!a.has(e))continue;let r={x:t-o.distance.x,y:n-o.distance.y};c&&(r=T(r,l));let i=[[u[0][0],u[0][1]],[u[1][0],u[1][1]]];if(s.size>1&&u&&!o.extent){const{positionAbsolute:t}=o.internals,e=t.x-x.x+u[0][0],n=t.x+o.measured.width-x.x2+u[1][0];i=[[e,t.y-x.y+u[0][1]],[n,t.y+o.measured.height-x.y2+u[1][1]]]}const{position:d,positionAbsolute:f}=m({nodeId:e,nextPosition:r,nodeLookup:a,nodeExtent:i,nodeOrigin:h,onError:g});v=v||o.position.x!==d.x||o.position.y!==d.y,o.position=d,o.internals.positionAbsolute=f}if(v&&(y(s,!0),r&&(o||d||!w&&f))){const[t,e]=Ge({nodeId:w,dragItems:s,nodeLookup:a});o?.(r,s,t,e),d?.(r,t,e),w||f?.(r,e)}}async function E(){if(!c)return;const{transform:t,panBy:n,autoPanSpeed:o,autoPanOnNodeDrag:r}=e();if(!r)return u=!1,void cancelAnimationFrame(a);const[s,h]=_(l,c,o);0===s&&0===h||(i.x=(i.x??0)-s/t[2],i.y=(i.y??0)-h/t[2],await n({x:s,y:h})&&P(i,null)),a=requestAnimationFrame(E)}function S(o){const{nodeLookup:r,multiSelectionActive:a,nodesDraggable:u,transform:l,snapGrid:d,snapToGrid:f,selectNodesOnDrag:p,onNodeDragStart:g,onSelectionDragStart:m,unselectNodesAndEdges:y}=e();h=!0,p&&x||a||!w||r.get(w)?.selected||y(),x&&p&&w&&t?.(w);const v=L(o.sourceEvent,{transform:l,snapGrid:d,snapToGrid:f,containerBounds:c});if(i=v,s=function(t,e,n,o){const r=new Map;for(const[i,a]of t)if((a.selected||a.id===o)&&(!a.parentId||!qe(a,t))&&(a.draggable||e&&void 0===a.draggable)){const e=t.get(i);e&&r.set(i,{id:i,position:e.position||{x:0,y:0},distance:{x:n.x-e.internals.positionAbsolute.x,y:n.y-e.internals.positionAbsolute.y},extent:e.extent,parentId:e.parentId,origin:e.origin,expandParent:e.expandParent,internals:{positionAbsolute:e.internals.positionAbsolute||{x:0,y:0}},measured:{width:e.measured.width??0,height:e.measured.height??0}})}return r}(r,u,v,w),s.size>0&&(n||g||!w&&m)){const[t,e]=Ge({nodeId:w,dragItems:s,nodeLookup:r});n?.(o.sourceEvent,s,t,e),g?.(o.sourceEvent,t,e),w||m?.(o.sourceEvent,e)}}d=ke(v);const N=Ve().clickDistance(b).on("start",(t=>{const{domNode:n,nodeDragThreshold:o,transform:r,snapGrid:a,snapToGrid:s}=e();c=n?.getBoundingClientRect()||null,f=!1,0===o&&S(t);const u=L(t.sourceEvent,{transform:r,snapGrid:a,snapToGrid:s,containerBounds:c});i=u,l=q(t.sourceEvent,c)})).on("drag",(t=>{const{autoPanOnNodeDrag:n,transform:o,snapGrid:r,snapToGrid:a,nodeDragThreshold:d,nodeLookup:p}=e(),g=L(t.sourceEvent,{transform:o,snapGrid:r,snapToGrid:a,containerBounds:c});if(("touchmove"===t.sourceEvent.type&&t.sourceEvent.touches.length>1||w&&!p.has(w))&&(f=!0),!f){if(!u&&n&&h&&(u=!0,E()),!h){const e=g.xSnapped-(i.x??0),n=g.ySnapped-(i.y??0);Math.sqrt(e*e+n*n)>d&&S(t)}(i.x!==g.xSnapped||i.y!==g.ySnapped)&&s&&h&&(l=q(t.sourceEvent,c),P(g,t.sourceEvent))}})).on("end",(t=>{if(h&&!f&&(u=!1,h=!1,cancelAnimationFrame(a),s.size>0)){const{nodeLookup:n,updateNodePositions:o,onNodeDragStop:i,onSelectionDragStop:a}=e();if(o(s,!1),r||i||!w&&a){const[e,o]=Ge({nodeId:w,dragItems:s,nodeLookup:n,dragging:!1});r?.(t.sourceEvent,s,e,o),i?.(t.sourceEvent,e,o),w||a?.(t.sourceEvent,o)}}})).filter((t=>{const e=t.target;return!t.button&&(!g||!Ze(e,`.${g}`,v))&&(!y||Ze(e,y,v))}));d.call(N)},destroy:function(){d?.on(".drag",null)}}},t.XYHandle=Je,t.XYMinimap=function({domNode:t,panZoom:e,getTransform:n,getViewScale:o}){const r=ke(t);return{update:function({translateExtent:t,width:i,height:a,zoomStep:s=10,pannable:u=!0,zoomable:l=!0,inversePan:c=!1}){let h=[0,0];const d=wr().on("start",(t=>{"mousedown"!==t.sourceEvent.type&&"touchstart"!==t.sourceEvent.type||(h=[t.sourceEvent.clientX??t.sourceEvent.touches[0].clientX,t.sourceEvent.clientY??t.sourceEvent.touches[0].clientY])})).on("zoom",u?r=>{const s=n();if("mousemove"!==r.sourceEvent.type&&"touchmove"!==r.sourceEvent.type||!e)return;const u=[r.sourceEvent.clientX??r.sourceEvent.touches[0].clientX,r.sourceEvent.clientY??r.sourceEvent.touches[0].clientY],l=[u[0]-h[0],u[1]-h[1]];h=u;const d=o()*Math.max(s[2],Math.log(s[2]))*(c?-1:1),f={x:s[0]-l[0]*d,y:s[1]-l[1]*d},p=[[0,0],[i,a]];e.setViewportConstrained({x:f.x,y:f.y,zoom:s[2]},p,t)}:null).on("zoom.wheel",l?t=>{const o=n();if("wheel"!==t.sourceEvent.type||!e)return;const r=-t.sourceEvent.deltaY*(1===t.sourceEvent.deltaMode?.05:t.sourceEvent.deltaMode?1:.002)*s,i=o[2]*Math.pow(2,r);e.scaleTo(i)}:null);r.call(d,{})},destroy:function(){r.on("zoom",null)},pointer:Ae}},t.XYPanZoom=function({domNode:e,minZoom:n,maxZoom:o,paneClickDistance:r,translateExtent:i,viewport:a,onPanZoom:s,onPanZoomStart:u,onPanZoomEnd:l,onDraggingChange:c}){const h={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},d=e.getBoundingClientRect(),f=wr().clickDistance(!k(r)||r<0?0:r).scaleExtent([n,o]).translateExtent(i),p=ke(e).call(f);w({x:a.x,y:a.y,zoom:y(a.zoom,n,o)},[[0,0],[d.width,d.height]],i);const g=p.on("wheel.zoom"),m=p.on("dblclick.zoom");function v(t,e){return p?new Promise((n=>{f?.interpolate("linear"===e?.interpolate?jn:ro).transform(Nr(p,e?.duration,e?.ease,(()=>n(!0))),t)})):Promise.resolve(!1)}function x(){f.on("zoom",null)}async function w(t,e,n){const o=Mr(t),r=f?.constrain()(o,e,n);return r&&await v(r),new Promise((t=>t(r)))}return f.wheelDelta(zr),{update:function({noWheelClassName:e,noPanClassName:n,onPaneContextMenu:o,userSelectionActive:r,panOnScroll:i,panOnDrag:a,panOnScrollMode:d,panOnScrollSpeed:y,preventScrolling:v,zoomOnPinch:w,zoomOnScroll:_,zoomOnDoubleClick:b,zoomActivationKeyPressed:M,lib:P,onTransformChange:E}){r&&!h.isZoomingOrPanning&&x();const S=i&&!M&&!r?function({zoomPanValues:e,noWheelClassName:n,d3Selection:o,d3Zoom:r,panOnScrollMode:i,panOnScrollSpeed:a,zoomOnPinch:s,onPanZoomStart:u,onPanZoom:l,onPanZoomEnd:c}){return h=>{if(Pr(h,n))return!1;h.preventDefault(),h.stopImmediatePropagation();const d=o.property("__zoom").k||1;if(h.ctrlKey&&s){const t=Ae(h),e=zr(h),n=d*Math.pow(2,e);return void r.scaleTo(o,n,t,h)}const f=1===h.deltaMode?20:1;let p=i===t.PanOnScrollMode.Vertical?0:h.deltaX*f,g=i===t.PanOnScrollMode.Horizontal?0:h.deltaY*f;!O()&&h.shiftKey&&i!==t.PanOnScrollMode.Vertical&&(p=h.deltaY*f,g=0),r.translateBy(o,-p/d*a,-g/d*a,{internal:!0});const m=br(o.property("__zoom"));clearTimeout(e.panScrollTimeout),e.isPanScrolling||(e.isPanScrolling=!0,u?.(h,m)),e.isPanScrolling&&(l?.(h,m),e.panScrollTimeout=setTimeout((()=>{c?.(h,m),e.isPanScrolling=!1}),150))}}({zoomPanValues:h,noWheelClassName:e,d3Selection:p,d3Zoom:f,panOnScrollMode:d,panOnScrollSpeed:y,zoomOnPinch:w,onPanZoomStart:u,onPanZoom:s,onPanZoomEnd:l}):function({noWheelClassName:t,preventScrolling:e,d3ZoomHandler:n}){return function(o,r){const i="wheel"===o.type,a=!e&&i&&!o.ctrlKey,s=Pr(o,t);if(o.ctrlKey&&i&&s&&o.preventDefault(),a||s)return null;o.preventDefault(),n.call(this,o,r)}}({noWheelClassName:e,preventScrolling:v,d3ZoomHandler:g});if(p.on("wheel.zoom",S,{passive:!1}),!r){const t=function({zoomPanValues:t,onDraggingChange:e,onPanZoomStart:n}){return o=>{if(o.sourceEvent?.internal)return;const r=br(o.transform);t.mouseButton=o.sourceEvent?.button||0,t.isZoomingOrPanning=!0,t.prevViewport=r,"mousedown"===o.sourceEvent?.type&&e(!0),n&&n?.(o.sourceEvent,r)}}({zoomPanValues:h,onDraggingChange:c,onPanZoomStart:u});f.on("start",t);const e=function({zoomPanValues:t,panOnDrag:e,onPaneContextMenu:n,onTransformChange:o,onPanZoom:r}){return i=>{t.usedRightMouseButton=!(!n||!Er(e,t.mouseButton??0)),i.sourceEvent?.sync||o([i.transform.x,i.transform.y,i.transform.k]),r&&!i.sourceEvent?.internal&&r?.(i.sourceEvent,br(i.transform))}}({zoomPanValues:h,panOnDrag:a,onPaneContextMenu:!!o,onPanZoom:s,onTransformChange:E});f.on("zoom",e);const n=function({zoomPanValues:t,panOnDrag:e,panOnScroll:n,onDraggingChange:o,onPanZoomEnd:r,onPaneContextMenu:i}){return a=>{if(!a.sourceEvent?.internal&&(t.isZoomingOrPanning=!1,i&&Er(e,t.mouseButton??0)&&!t.usedRightMouseButton&&a.sourceEvent&&i(a.sourceEvent),t.usedRightMouseButton=!1,o(!1),r&&_r(t.prevViewport,a.transform))){const e=br(a.transform);t.prevViewport=e,clearTimeout(t.timerId),t.timerId=setTimeout((()=>{r?.(a.sourceEvent,e)}),n?150:0)}}}({zoomPanValues:h,panOnDrag:a,panOnScroll:i,onPaneContextMenu:o,onPanZoomEnd:l,onDraggingChange:c});f.on("end",n)}const N=function({zoomActivationKeyPressed:t,zoomOnScroll:e,zoomOnPinch:n,panOnDrag:o,panOnScroll:r,zoomOnDoubleClick:i,userSelectionActive:a,noWheelClassName:s,noPanClassName:u,lib:l}){return c=>{const h=t||e,d=n&&c.ctrlKey;if(1===c.button&&"mousedown"===c.type&&(Pr(c,`${l}-flow__node`)||Pr(c,`${l}-flow__edge`)))return!0;if(!(o||h||r||i||n))return!1;if(a)return!1;if(Pr(c,s)&&"wheel"===c.type)return!1;if(Pr(c,u)&&("wheel"!==c.type||r&&"wheel"===c.type&&!t))return!1;if(!n&&c.ctrlKey&&"wheel"===c.type)return!1;if(!n&&"touchstart"===c.type&&c.touches?.length>1)return c.preventDefault(),!1;if(!h&&!r&&!d&&"wheel"===c.type)return!1;if(!o&&("mousedown"===c.type||"touchstart"===c.type))return!1;if(Array.isArray(o)&&!o.includes(c.button)&&"mousedown"===c.type)return!1;const f=Array.isArray(o)&&o.includes(c.button)||!c.button||c.button<=1;return(!c.ctrlKey||"wheel"===c.type)&&f}}({zoomActivationKeyPressed:M,panOnDrag:a,zoomOnScroll:_,panOnScroll:i,zoomOnDoubleClick:b,zoomOnPinch:w,userSelectionActive:r,noPanClassName:n,noWheelClassName:e,lib:P});f.filter(N),b?p.on("dblclick.zoom",m):p.on("dblclick.zoom",null)},destroy:x,setViewport:async function(t,e){const n=Mr(t);return await v(n,e),new Promise((t=>t(n)))},setViewportConstrained:w,getViewport:function(){const t=p?hr(p.node()):{x:0,y:0,k:1};return{x:t.x,y:t.y,zoom:t.k}},scaleTo:function(t,e){return p?new Promise((n=>{f?.interpolate("linear"===e?.interpolate?jn:ro).scaleTo(Nr(p,e?.duration,e?.ease,(()=>n(!0))),t)})):Promise.resolve(!1)},scaleBy:function(t,e){return p?new Promise((n=>{f?.interpolate("linear"===e?.interpolate?jn:ro).scaleBy(Nr(p,e?.duration,e?.ease,(()=>n(!0))),t)})):Promise.resolve(!1)},setScaleExtent:function(t){f?.scaleExtent(t)},setTranslateExtent:function(t){f?.translateExtent(t)},syncViewport:function(t){if(p){const e=Mr(t),n=p.property("__zoom");n.k===t.zoom&&n.x===t.x&&n.y===t.y||f?.transform(p,e,null,{sync:!0})}},setClickDistance:function(t){const e=!k(t)||t<0?0:t;f?.clickDistance(e)}}},t.XYResizer=function({domNode:t,nodeId:e,getStoreItems:n,onChange:o,onEnd:r}){const i=ke(t);return{update:function({controlPosition:t,boundaries:a,keepAspectRatio:s,resizeDirection:u,onResizeStart:l,onResize:c,onResizeEnd:h,shouldResize:d}){let f={...Cr},p={...Hr};const g=function(t){return{isHorizontal:t.includes("right")||t.includes("left"),isVertical:t.includes("bottom")||t.includes("top"),affectsX:t.includes("left"),affectsY:t.includes("top")}}(t);let m,y,v,x,w=null,_=[];const b=Ve().on("start",(t=>{const{nodeLookup:o,transform:r,snapGrid:i,snapToGrid:a,nodeOrigin:s,paneDomNode:u}=n();if(m=o.get(e),!m)return;w=u?.getBoundingClientRect()??null;const{xSnapped:c,ySnapped:h}=L(t.sourceEvent,{transform:r,snapGrid:i,snapToGrid:a,containerBounds:w});f={width:m.measured.width??0,height:m.measured.height??0,x:m.position.x??0,y:m.position.y??0},p={...f,pointerX:c,pointerY:h,aspectRatio:f.width/f.height},y=void 0,m.parentId&&("parent"===m.extent||m.expandParent)&&(y=o.get(m.parentId),v=y&&"parent"===m.extent?function(t){return[[0,0],[t.measured.width,t.measured.height]]}(y):void 0),_=[],x=void 0;for(const[t,n]of o)if(n.parentId===e&&(_.push({id:t,position:{...n.position},extent:n.extent}),"parent"===n.extent||n.expandParent)){const t=Or(n,m,n.origin??s);x=x?[[Math.min(t[0][0],x[0][0]),Math.min(t[0][1],x[0][1])],[Math.max(t[1][0],x[1][0]),Math.max(t[1][1],x[1][1])]]:t}l?.(t,{...f})})).on("drag",(t=>{const{transform:e,snapGrid:r,snapToGrid:i,nodeOrigin:l}=n(),h=L(t.sourceEvent,{transform:e,snapGrid:r,snapToGrid:i,containerBounds:w}),b=[];if(!m)return;const{x:M,y:P,width:E,height:S}=f,N={},z=m.origin??l,{width:k,height:A,x:T,y:I}=function(t,e,n,o,r,i,a,s){let{affectsX:u,affectsY:l}=e;const{isHorizontal:c,isVertical:h}=e,d=c&&h,{xSnapped:f,ySnapped:p}=n,{minWidth:g,maxWidth:m,minHeight:y,maxHeight:v}=o,{x:x,y:w,width:_,height:b,aspectRatio:M}=t;let P=Math.floor(c?f-t.pointerX:0),E=Math.floor(h?p-t.pointerY:0);const S=_+(u?-P:P),N=b+(l?-E:E),z=-i[0]*_,k=-i[1]*b;let A=Ir(S,g,m),T=Ir(N,y,v);if(a){let t=0,e=0;u&&P<0?t=Ar(x+P+z,a[0][0]):!u&&P>0&&(t=Tr(x+S+z,a[1][0])),l&&E<0?e=Ar(w+E+k,a[0][1]):!l&&E>0&&(e=Tr(w+N+k,a[1][1])),A=Math.max(A,t),T=Math.max(T,e)}if(s){let t=0,e=0;u&&P>0?t=Tr(x+P,s[0][0]):!u&&P<0&&(t=Ar(x+S,s[1][0])),l&&E>0?e=Tr(w+E,s[0][1]):!l&&E<0&&(e=Ar(w+N,s[1][1])),A=Math.max(A,t),T=Math.max(T,e)}if(r){if(c){const t=Ir(S/M,y,v)*M;if(A=Math.max(A,t),a){let t=0;t=!u&&!l||u&&!l&&d?Tr(w+k+S/M,a[1][1])*M:Ar(w+k+(u?P:-P)/M,a[0][1])*M,A=Math.max(A,t)}if(s){let t=0;t=!u&&!l||u&&!l&&d?Ar(w+S/M,s[1][1])*M:Tr(w+(u?P:-P)/M,s[0][1])*M,A=Math.max(A,t)}}if(h){const t=Ir(N*M,g,m)/M;if(T=Math.max(T,t),a){let t=0;t=!u&&!l||l&&!u&&d?Tr(x+N*M+z,a[1][0])/M:Ar(x+(l?E:-E)*M+z,a[0][0])/M,T=Math.max(T,t)}if(s){let t=0;t=!u&&!l||l&&!u&&d?Ar(x+N*M,s[1][0])/M:Tr(x+(l?E:-E)*M,s[0][0])/M,T=Math.max(T,t)}}}E+=E<0?T:-T,P+=P<0?A:-A,r&&(d?S>N*M?E=($r(u,l)?-P:P)/M:P=($r(u,l)?-E:E)*M:c?(E=P/M,l=u):(P=E*M,u=l));const I=u?x+P:x,$=l?w+E:w;return{width:_+(u?-P:P),height:b+(l?-E:E),x:i[0]*P*(u?-1:1)+I,y:i[1]*E*(l?-1:1)+$}}(p,g,h,a,s,z,v,x),$=k!==E,C=A!==S,H=T!==M&&$,O=I!==P&&C;if(!(H||O||$||C))return;if((H||O||1===z[0]||1===z[1])&&(N.x=H?T:f.x,N.y=O?I:f.y,f.x=N.x,f.y=N.y,_.length>0)){const t=T-M,e=I-P;for(const n of _)n.position={x:n.position.x-t+z[0]*(k-E),y:n.position.y-e+z[1]*(A-S)},b.push(n)}if(($||C)&&(N.width=!$||u&&"horizontal"!==u?f.width:k,N.height=!C||u&&"vertical"!==u?f.height:A,f.width=N.width,f.height=N.height),y&&m.expandParent){const t=z[0]*(N.width??0);N.x&&N.x<t&&(f.x=t,p.x=p.x-(N.x-t));const e=z[1]*(N.height??0);N.y&&N.y<e&&(f.y=e,p.y=p.y-(N.y-e))}const B=function({width:t,prevWidth:e,height:n,prevHeight:o,affectsX:r,affectsY:i}){const a=t-e,s=n-o,u=[a>0?1:a<0?-1:0,s>0?1:s<0?-1:0];return a&&r&&(u[0]=-1*u[0]),s&&i&&(u[1]=-1*u[1]),u}({width:f.width,prevWidth:E,height:f.height,prevHeight:S,affectsX:g.affectsX,affectsY:g.affectsY}),D={...f,direction:B},Y=d?.(t,D);!1!==Y&&(c?.(t,D),o(N,b))})).on("end",(t=>{h?.(t,{...f}),r?.({...f})}));i.call(b)},destroy:function(){i.on(".drag",null)}}},t.XY_RESIZER_HANDLE_POSITIONS=["top-left","top-right","bottom-left","bottom-right"],t.XY_RESIZER_LINE_POSITIONS=["top","right","bottom","left"],t.addEdge=(t,n)=>{if(!t.source||!t.target)return e.error006(),n;let o;return o=h(t)?{...t}:{...t,id:K(t)},((t,e)=>e.some((e=>!(e.source!==t.source||e.target!==t.target||e.sourceHandle!==t.sourceHandle&&(e.sourceHandle||t.sourceHandle)||e.targetHandle!==t.targetHandle&&(e.targetHandle||t.targetHandle)))))(o,n)?n:(null===o.sourceHandle&&delete o.sourceHandle,null===o.targetHandle&&delete o.targetHandle,n.concat(o))},t.adoptUserNodes=function(t,e,n,o){const r=st(at,o);let i=t.length>0;const a=new Map(e),s=r?.elevateNodesOnSelect?1e3:0;e.clear(),n.clear();for(const u of t){let t=a.get(u.id);if(r.checkEquality&&u===t?.internals.userNode)e.set(u.id,t);else{const n=f(u,r.nodeOrigin),o=B(u.extent)?u.extent:r.nodeExtent,i=v(n,o,D(u));t={...r.defaults,...u,measured:{width:u.measured?.width,height:u.measured?.height},internals:{positionAbsolute:i,handleBounds:u.measured?t?.internals.handleBounds:void 0,z:lt(u,s),userNode:u}},e.set(u.id,t)}void 0!==t.measured&&void 0!==t.measured.width&&void 0!==t.measured.height||t.hidden||(i=!1),u.parentId&&ut(t,e,n,o)}return i},t.areConnectionMapsEqual=function(t,e){if(!t&&!e)return!0;if(!t||!e||t.size!==e.size)return!1;if(!t.size&&!e.size)return!0;for(const n of t.keys())if(!e.has(n))return!1;return!0},t.areSetsEqual=function(t,e){if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0},t.boxToRect=P,t.calcAutoPan=_,t.calculateNodePosition=m,t.clamp=y,t.clampPosition=v,t.clampPositionToParent=x,t.createMarkerIds=function(t,{id:e,defaultColor:n,defaultMarkerStart:o,defaultMarkerEnd:r}){const i=new Set;return t.reduce(((t,a)=>([a.markerStart||o,a.markerEnd||r].forEach((o=>{if(o&&"object"==typeof o){const r=rt(o,e);i.has(r)||(t.push({id:r,color:o.color||n,...o}),i.add(r))}})),t)),[]).sort(((t,e)=>t.id.localeCompare(e.id)))},t.defaultAriaLabelConfig=o,t.devWarn=A,t.elementSelectionKeys=["Enter"," ","Escape"],t.errorMessages=e,t.evaluateAbsolutePosition=function(t,e={width:0,height:0},n,o,r){const i={...t},a=o.get(n);if(a){const t=a.origin||r;i.x+=a.internals.positionAbsolute.x-(e.width??0)*t[0],i.y+=a.internals.positionAbsolute.y-(e.height??0)*t[1]}return i},t.fitViewport=async function({nodes:t,width:e,height:n,panZoom:o,minZoom:r,maxZoom:i},a){if(0===t.size)return Promise.resolve(!0);const s=function(t,e){const n=new Map,o=e?.nodes?new Set(e.nodes.map((t=>t.id))):null;return t.forEach((t=>{!t.measured.width||!t.measured.height||!e?.includeHiddenNodes&&t.hidden||o&&!o.has(t.id)||n.set(t.id,t)})),n}(t,a),u=p(s),l=H(u,e,n,a?.minZoom??r,a?.maxZoom??i,a?.padding??.1);return await o.setViewport(l,{duration:a?.duration,ease:a?.ease,interpolate:a?.interpolate}),Promise.resolve(!0)},t.getBezierEdgeCenter=G,t.getBezierPath=function({sourceX:e,sourceY:n,sourcePosition:o=t.Position.Bottom,targetX:r,targetY:i,targetPosition:a=t.Position.Top,curvature:s=.25}){const[u,l]=F({pos:o,x1:e,y1:n,x2:r,y2:i,c:s}),[c,h]=F({pos:a,x1:r,y1:i,x2:e,y2:n,c:s}),[d,f,p,g]=G({sourceX:e,sourceY:n,targetX:r,targetY:i,sourceControlX:u,sourceControlY:l,targetControlX:c,targetControlY:h});return[`M${e},${n} C${u},${l} ${c},${h} ${r},${i}`,d,f,p,g]},t.getBoundsOfBoxes=b,t.getBoundsOfRects=N,t.getConnectedEdges=g,t.getConnectionStatus=function(t){return null===t?null:t?"valid":"invalid"},t.getDimensions=Y,t.getEdgeCenter=W,t.getEdgePosition=function(n){const{sourceNode:o,targetNode:r}=n;if(!tt(o)||!tt(r))return null;const i=o.internals.handleBounds||et(o.handles),a=r.internals.handleBounds||et(r.handles),s=ot(i?.source??[],n.sourceHandle),u=ot(n.connectionMode===t.ConnectionMode.Strict?a?.target??[]:(a?.target??[]).concat(a?.source??[]),n.targetHandle);if(!s||!u)return n.onError?.("008",e.error008(s?"target":"source",{id:n.id,sourceHandle:n.sourceHandle,targetHandle:n.targetHandle})),null;const l=s?.position||t.Position.Bottom,c=u?.position||t.Position.Top,h=nt(o,s,l),d=nt(r,u,c);return{sourceX:h.x,sourceY:h.y,targetX:d.x,targetY:d.y,sourcePosition:l,targetPosition:c}},t.getElementsToRemove=async function({nodesToRemove:t=[],edgesToRemove:e=[],nodes:n,edges:o,onBeforeDelete:r}){const i=new Set(t.map((t=>t.id))),a=[];for(const t of n){if(!1===t.deletable)continue;const e=i.has(t.id),n=!e&&t.parentId&&a.find((e=>e.id===t.parentId));(e||n)&&a.push(t)}const s=new Set(e.map((t=>t.id))),u=o.filter((t=>!1!==t.deletable)),l=g(a,u);for(const t of u){s.has(t.id)&&!l.find((e=>e.id===t.id))&&l.push(t)}if(!r)return{edges:l,nodes:a};const c=await r({nodes:a,edges:l});return"boolean"==typeof c?c?{edges:l,nodes:a}:{edges:[],nodes:[]}:c},t.getElevatedEdgeZIndex=function({sourceNode:t,targetNode:e,selected:n=!1,zIndex:o=0,elevateOnSelect:r=!1}){if(!r)return o;const i=n||e.selected||t.selected,a=Math.max(t.internals.z||0,e.internals.z||0,1e3);return o+(i?a:0)},t.getEventPosition=q,t.getHandleBounds=Z,t.getHandlePosition=nt,t.getHostForElement=X,t.getIncomers=(t,e,n)=>{if(!t.id)return[];const o=new Set;return n.forEach((e=>{e.target===t.id&&o.add(e.source)})),e.filter((t=>o.has(t.id)))},t.getInternalNodesBounds=p,t.getMarkerId=rt,t.getNodeDimensions=D,t.getNodePositionWithOrigin=f,t.getNodeToolbarTransform=function(e,n,o,r,i){let a=.5;"start"===i?a=0:"end"===i&&(a=1);let s=[(e.x+e.width*a)*n.zoom+n.x,e.y*n.zoom+n.y-r],u=[-100*a,-100];switch(o){case t.Position.Right:s=[(e.x+e.width)*n.zoom+n.x+r,(e.y+e.height*a)*n.zoom+n.y],u=[0,-100*a];break;case t.Position.Bottom:s[1]=(e.y+e.height)*n.zoom+n.y+r,u[1]=0;break;case t.Position.Left:s=[e.x*n.zoom+n.x-r,(e.y+e.height*a)*n.zoom+n.y],u=[-100,-100*a]}return`translate(${s[0]}px, ${s[1]}px) translate(${u[0]}%, ${u[1]}%)`},t.getNodesBounds=(t,e={nodeOrigin:[0,0]})=>{if(0===t.length)return{x:0,y:0,width:0,height:0};const n=t.reduce(((t,n)=>{const o="string"==typeof n;let r=e.nodeLookup||o?void 0:n;e.nodeLookup&&(r=o?e.nodeLookup.get(n):d(n)?n:e.nodeLookup.get(n.id));const i=r?S(r,e.nodeOrigin):{x:0,y:0,x2:0,y2:0};return b(t,i)}),{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return P(n)},t.getNodesInside=(t,e,[n,o,r]=[0,0,1],i=!1,a=!1)=>{const s={...I(e,[n,o,r]),width:e.width/r,height:e.height/r},u=[];for(const e of t.values()){const{measured:t,selectable:n=!0,hidden:o=!1}=e;if(a&&!n||o)continue;const r=t.width??e.width??e.initialWidth??null,l=t.height??e.height??e.initialHeight??null,c=z(s,E(e)),h=(r??0)*(l??0),d=i&&c>0;(!e.internals.handleBounds||d||c>=h||e.dragging)&&u.push(e)}return u},t.getOutgoers=(t,e,n)=>{if(!t.id)return[];const o=new Set;return n.forEach((e=>{e.source===t.id&&o.add(e.target)})),e.filter((t=>o.has(t.id)))},t.getOverlappingArea=z,t.getPointerPosition=L,t.getSmoothStepPath=function({sourceX:e,sourceY:n,sourcePosition:o=t.Position.Bottom,targetX:r,targetY:i,targetPosition:a=t.Position.Top,borderRadius:s=5,centerX:u,centerY:l,offset:c=20}){const[h,d,f,p,g]=function({source:e,sourcePosition:n=t.Position.Bottom,target:o,targetPosition:r=t.Position.Top,center:i,offset:a}){const s=U[n],u=U[r],l={x:e.x+s.x*a,y:e.y+s.y*a},c={x:o.x+u.x*a,y:o.y+u.y*a},h=Q({source:l,sourcePosition:n,target:c}),d=0!==h.x?"x":"y",f=h[d];let p,g,m=[];const y={x:0,y:0},v={x:0,y:0},[x,w,_,b]=W({sourceX:e.x,sourceY:e.y,targetX:o.x,targetY:o.y});if(s[d]*u[d]==-1){p=i.x??x,g=i.y??w;const t=[{x:p,y:l.y},{x:p,y:c.y}],e=[{x:l.x,y:g},{x:c.x,y:g}];m=s[d]===f?"x"===d?t:e:"x"===d?e:t}else{const t=[{x:l.x,y:c.y}],i=[{x:c.x,y:l.y}];if(m="x"===d?s.x===f?i:t:s.y===f?t:i,n===r){const t=Math.abs(e[d]-o[d]);if(t<=a){const n=Math.min(a-1,a-t);s[d]===f?y[d]=(l[d]>e[d]?-1:1)*n:v[d]=(c[d]>o[d]?-1:1)*n}}if(n!==r){const e="x"===d?"y":"x",n=s[d]===u[e],o=l[e]>c[e],r=l[e]<c[e];(1===s[d]&&(!n&&o||n&&r)||1!==s[d]&&(!n&&r||n&&o))&&(m="x"===d?t:i)}const h={x:l.x+y.x,y:l.y+y.y},x={x:c.x+v.x,y:c.y+v.y};Math.max(Math.abs(h.x-m[0].x),Math.abs(x.x-m[0].x))>=Math.max(Math.abs(h.y-m[0].y),Math.abs(x.y-m[0].y))?(p=(h.x+x.x)/2,g=m[0].y):(p=m[0].x,g=(h.y+x.y)/2)}return[[e,{x:l.x+y.x,y:l.y+y.y},...m,{x:c.x+v.x,y:c.y+v.y},o],p,g,_,b]}({source:{x:e,y:n},sourcePosition:o,target:{x:r,y:i},targetPosition:a,center:{x:u,y:l},offset:c});return[h.reduce(((t,e,n)=>{let o="";return o=n>0&&n<h.length-1?function(t,e,n,o){const r=Math.min(J(t,e)/2,J(e,n)/2,o),{x:i,y:a}=e;if(t.x===i&&i===n.x||t.y===a&&a===n.y)return`L${i} ${a}`;if(t.y===a)return`L ${i+r*(t.x<n.x?-1:1)},${a}Q ${i},${a} ${i},${a+r*(t.y<n.y?1:-1)}`;const s=t.x<n.x?1:-1;return`L ${i},${a+r*(t.y<n.y?-1:1)}Q ${i},${a} ${i+r*s},${a}`}(h[n-1],e,h[n+1],s):`${0===n?"M":"L"}${e.x} ${e.y}`,t+=o}),""),d,f,p,g]},t.getStraightPath=function({sourceX:t,sourceY:e,targetX:n,targetY:o}){const[r,i,a,s]=W({sourceX:t,sourceY:e,targetX:n,targetY:o});return[`M ${t},${e}L ${n},${o}`,r,i,a,s]},t.getViewportForBounds=H,t.handleConnectionChange=function(t,e,n){if(!n)return;const o=[];t.forEach(((t,n)=>{e?.has(n)||o.push(t)})),o.length&&n(o)},t.handleExpandParent=ct,t.infiniteExtent=n,t.initialConnection={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null},t.isCoordinateExtent=B,t.isEdgeBase=h,t.isEdgeVisible=function({sourceNode:t,targetNode:e,width:n,height:o,transform:r}){const i=b(S(t),S(e));i.x===i.x2&&(i.x2+=1),i.y===i.y2&&(i.y2+=1);const a={x:-r[0]/r[2],y:-r[1]/r[2],width:n/r[2],height:o/r[2]};return z(a,P(i))>0},t.isInputDOMNode=function(t){const e=t.composedPath?.()?.[0]||t.target;return 1===e?.nodeType&&(R.includes(e.nodeName)||e.hasAttribute("contenteditable")||!!e.closest(".nokey"))},t.isInternalNodeBase=d,t.isMacOs=O,t.isMouseEvent=V,t.isNodeBase=t=>"id"in t&&"position"in t&&!("source"in t)&&!("target"in t),t.isNumeric=k,t.isRectObject=t=>k(t.width)&&k(t.height)&&k(t.x)&&k(t.y),t.mergeAriaLabelConfig=function(t){return{...o,...t||{}}},t.nodeHasDimensions=function(t){return void 0!==(t.measured?.width??t.width??t.initialWidth)&&void 0!==(t.measured?.height??t.height??t.initialHeight)},t.nodeToBox=S,t.nodeToRect=E,t.oppositePosition=c,t.panBy=async function({delta:t,panZoom:e,transform:n,translateExtent:o,width:r,height:i}){if(!e||!t.x&&!t.y)return Promise.resolve(!1);const a=await e.setViewportConstrained({x:n[0]+t.x,y:n[1]+t.y,zoom:n[2]},[[0,0],[r,i]],o),s=!!a&&(a.x!==n[0]||a.y!==n[1]||a.k!==n[2]);return Promise.resolve(s)},t.pointToRendererPoint=I,t.reconnectEdge=(t,n,o,r={shouldReplaceId:!0})=>{const{id:i,...a}=t;if(!n.source||!n.target)return e.error006(),o;if(!o.find((e=>e.id===t.id)))return e.error007(i),o;const s={...a,id:r.shouldReplaceId?K(n):i,source:n.source,target:n.target,sourceHandle:n.sourceHandle,targetHandle:n.targetHandle};return o.filter((t=>t.id!==i)).concat(s)},t.rectToBox=M,t.rendererPointToPoint=$,t.shallowNodeData=function(t,e){if(null===t||null===e)return!1;const n=Array.isArray(t)?t:[t],o=Array.isArray(e)?e:[e];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++)if(n[t].id!==o[t].id||n[t].type!==o[t].type||!Object.is(n[t].data,o[t].data))return!1;return!0},t.snapPosition=T,t.updateAbsolutePositions=function(t,e,n){const o=st(it,n);for(const n of t.values())if(n.parentId)ut(n,t,e,o);else{const t=f(n,o.nodeOrigin),e=B(n.extent)?n.extent:o.nodeExtent,r=v(t,e,D(n));n.internals.positionAbsolute=r}},t.updateConnectionLookup=function(t,e,n){t.clear(),e.clear();for(const o of n){const{source:n,target:r,sourceHandle:i=null,targetHandle:a=null}=o,s={edgeId:o.id,source:n,target:r,sourceHandle:i,targetHandle:a},u=`${n}-${i}--${r}-${a}`;ht("source",s,`${r}-${a}--${n}-${i}`,t,n,i),ht("target",s,u,t,r,a),e.set(o.id,o)}},t.updateNodeInternals=function(t,e,n,o,r,i){const a=o?.querySelector(".xyflow__viewport");let s=!1;if(!a)return{changes:[],updatedInternals:s};const u=[],l=window.getComputedStyle(a),{m22:c}=new window.DOMMatrixReadOnly(l.transform),h=[];for(const o of t.values()){const t=e.get(o.id);if(!t)continue;if(t.hidden){e.set(t.id,{...t,internals:{...t.internals,handleBounds:void 0}}),s=!0;continue}const a=Y(o.nodeElement),l=t.measured.width!==a.width||t.measured.height!==a.height;if(!(!a.width||!a.height||!l&&t.internals.handleBounds&&!o.force)){const d=o.nodeElement.getBoundingClientRect(),f=B(t.extent)?t.extent:i;let{positionAbsolute:p}=t.internals;t.parentId&&"parent"===t.extent?p=x(p,a,e.get(t.parentId)):f&&(p=v(p,f,a));const g={...t,measured:a,internals:{...t.internals,positionAbsolute:p,handleBounds:{source:Z("source",o.nodeElement,d,c,t.id),target:Z("target",o.nodeElement,d,c,t.id)}}};e.set(t.id,g),t.parentId&&ut(g,e,n,{nodeOrigin:r}),s=!0,l&&(u.push({id:t.id,type:"dimensions",dimensions:a}),t.expandParent&&t.parentId&&h.push({id:t.id,parentId:t.parentId,rect:E(g,r)}))}}if(h.length>0){const t=ct(h,e,n,r);u.push(...t)}return{changes:u,updatedInternals:s}},t.withResolvers=function(){let t,e;return{promise:new Promise(((n,o)=>{t=n,e=o})),resolve:t,reject:e}}}));
