import type { Edge, Node, ReactFlowProps } from '../../types';
/**
 * The `<ReactFlow />` component is the heart of your React Flow application.
 * It renders your nodes and edges and handles user interaction
 *
 * @public
 *
 * @example
 * ```tsx
 *import { ReactFlow } from '@xyflow/react'
 *
 *export default function Flow() {
 *  return (<ReactFlow
 *    nodes={...}
 *    edges={...}
 *    onNodesChange={...}
 *    ...
 *  />);
 *}
 *```
 */
declare const _default: <NodeType extends Node = Node, EdgeType extends Edge = Edge>(props: ReactFlowProps<NodeType, EdgeType> & import("react").RefAttributes<HTMLDivElement>) => import("react").JSX.Element;
export default _default;
//# sourceMappingURL=index.d.ts.map