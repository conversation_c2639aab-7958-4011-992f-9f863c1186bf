{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/additional-components/NodeResizer/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACtD,OAAO,KAAK,EACV,eAAe,EACf,mBAAmB,EACnB,oBAAoB,EACpB,sBAAsB,EACtB,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,WAAW,EACZ,MAAM,gBAAgB,CAAC;AAExB;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG;IAC7B;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,kCAAkC;IAClC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,oCAAoC;IACpC,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,+BAA+B;IAC/B,WAAW,CAAC,EAAE,aAAa,CAAC;IAC5B,kCAAkC;IAClC,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,6BAA6B;IAC7B,SAAS,CAAC,EAAE,aAAa,CAAC;IAC1B;;;OAGG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B;;;OAGG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,mDAAmD;IACnD,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,4CAA4C;IAC5C,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,qCAAqC;IACrC,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB,0CAA0C;IAC1C,WAAW,CAAC,EAAE,WAAW,CAAC;CAC3B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,IAAI,CACnC,gBAAgB,EACd,QAAQ,GACR,OAAO,GACP,UAAU,GACV,WAAW,GACX,UAAU,GACV,WAAW,GACX,iBAAiB,GACjB,cAAc,GACd,WAAW,GACX,eAAe,GACf,UAAU,GACV,aAAa,CAChB,GAAG;IACF;;;;OAIG;IACH,QAAQ,CAAC,EAAE,eAAe,CAAC;IAC3B;;;;OAIG;IACH,OAAO,CAAC,EAAE,oBAAoB,CAAC;IAC/B;;;OAGG;IACH,eAAe,CAAC,EAAE,sBAAsB,CAAC;IACzC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,KAAK,CAAC,EAAE,aAAa,CAAC;IACtB,QAAQ,CAAC,EAAE,SAAS,CAAC;CACtB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,GAAG;IACjF,QAAQ,CAAC,EAAE,mBAAmB,CAAC;CAChC,CAAC"}