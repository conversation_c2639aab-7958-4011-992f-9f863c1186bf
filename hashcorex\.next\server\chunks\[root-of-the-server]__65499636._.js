module.exports = {

"[project]/.next-internal/server/app/api/admin/users/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "adminSettingsDb": (()=>adminSettingsDb),
    "binaryPointsDb": (()=>binaryPointsDb),
    "depositTransactionDb": (()=>depositTransactionDb),
    "miningUnitDb": (()=>miningUnitDb),
    "referralDb": (()=>referralDb),
    "systemLogDb": (()=>systemLogDb),
    "transactionDb": (()=>transactionDb),
    "userDb": (()=>userDb),
    "walletBalanceDb": (()=>walletBalanceDb),
    "withdrawalDb": (()=>withdrawalDb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
const userDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.create({
            data: {
                email: data.email,
                firstName: data.firstName,
                lastName: data.lastName,
                password: data.password,
                referralId: data.referralId || undefined
            }
        });
    },
    async findByEmail (email) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                email
            },
            include: {
                miningUnits: true,
                transactions: true,
                binaryPoints: true
            }
        });
    },
    async findById (id) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id
            },
            include: {
                miningUnits: true,
                transactions: true,
                binaryPoints: true
            }
        });
    },
    async findByReferralId (referralId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                referralId
            }
        });
    },
    async update (id, data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id
            },
            data
        });
    },
    async updateKYCStatus (userId, status) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id: userId
            },
            data: {
                kycStatus: status
            }
        });
    }
};
const miningUnitDb = {
    async create (data) {
        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 12 months from now
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.create({
            data: {
                userId: data.userId,
                thsAmount: data.thsAmount,
                investmentAmount: data.investmentAmount,
                dailyROI: data.dailyROI,
                expiryDate
            }
        });
    },
    async findActiveByUserId (userId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.findMany({
            where: {
                userId,
                status: 'ACTIVE',
                expiryDate: {
                    gt: new Date()
                }
            }
        });
    },
    async updateTotalEarned (unitId, amount) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.update({
            where: {
                id: unitId
            },
            data: {
                totalEarned: {
                    increment: amount
                }
            }
        });
    },
    async expireUnit (unitId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.update({
            where: {
                id: unitId
            },
            data: {
                status: 'EXPIRED'
            }
        });
    }
};
const transactionDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.create({
            data: {
                userId: data.userId,
                type: data.type,
                amount: data.amount,
                description: data.description,
                status: data.status || 'PENDING'
            }
        });
    },
    async findByUserId (userId, filters) {
        const where = {
            userId
        };
        if (filters?.types && filters.types.length > 0) {
            where.type = {
                in: filters.types
            };
        }
        if (filters?.status) {
            where.status = filters.status;
        }
        const include = filters?.includeUser ? {
            user: {
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true
                }
            }
        } : undefined;
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.findMany({
            where,
            include,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 50,
            skip: filters?.offset
        });
    },
    async updateStatus (transactionId, status) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.update({
            where: {
                id: transactionId
            },
            data: {
                status
            }
        });
    }
};
const referralDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.create({
            data: {
                referrerId: data.referrerId,
                referredId: data.referredId,
                placementSide: data.placementSide
            }
        });
    },
    async findByReferrerId (referrerId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
            where: {
                referrerId
            },
            include: {
                referred: {
                    select: {
                        id: true,
                        email: true,
                        createdAt: true
                    }
                }
            }
        });
    }
};
const binaryPointsDb = {
    async upsert (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.upsert({
            where: {
                userId: data.userId
            },
            update: {
                leftPoints: data.leftPoints !== undefined ? {
                    increment: data.leftPoints
                } : undefined,
                rightPoints: data.rightPoints !== undefined ? {
                    increment: data.rightPoints
                } : undefined
            },
            create: {
                userId: data.userId,
                leftPoints: data.leftPoints || 0,
                rightPoints: data.rightPoints || 0
            }
        });
    },
    async findByUserId (userId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.findUnique({
            where: {
                userId
            }
        });
    },
    async resetPoints (userId, leftPoints, rightPoints) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.update({
            where: {
                userId
            },
            data: {
                leftPoints,
                rightPoints,
                flushDate: new Date()
            }
        });
    }
};
const withdrawalDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.create({
            data: {
                userId: data.userId,
                amount: data.amount,
                usdtAddress: data.usdtAddress
            }
        });
    },
    async findPending () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.findMany({
            where: {
                status: 'PENDING'
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        kycStatus: true
                    }
                }
            },
            orderBy: {
                createdAt: 'asc'
            }
        });
    },
    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.update({
            where: {
                id: requestId
            },
            data: {
                status,
                processedBy,
                txid,
                rejectionReason,
                processedAt: new Date()
            }
        });
    }
};
const adminSettingsDb = {
    async get (key) {
        const setting = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.findUnique({
            where: {
                key
            }
        });
        return setting?.value;
    },
    async set (key, value, updatedBy) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.upsert({
            where: {
                key
            },
            update: {
                value
            },
            create: {
                key,
                value
            }
        });
    },
    async getAll () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.findMany();
    }
};
const systemLogDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].systemLog.create({
            data: {
                action: data.action,
                userId: data.userId,
                adminId: data.adminId,
                details: data.details ? JSON.stringify(data.details) : null,
                ipAddress: data.ipAddress,
                userAgent: data.userAgent
            }
        });
    }
};
const walletBalanceDb = {
    async getOrCreate (userId) {
        let walletBalance = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.findUnique({
            where: {
                userId
            }
        });
        if (!walletBalance) {
            walletBalance = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.create({
                data: {
                    userId,
                    availableBalance: 0,
                    pendingBalance: 0,
                    totalDeposits: 0,
                    totalWithdrawals: 0,
                    totalEarnings: 0
                }
            });
        }
        return walletBalance;
    },
    async updateBalance (userId, updates) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                ...updates,
                lastUpdated: new Date()
            }
        });
    },
    async addDeposit (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance + amount,
                totalDeposits: wallet.totalDeposits + amount,
                lastUpdated: new Date()
            }
        });
    },
    async addEarnings (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance + amount,
                totalEarnings: wallet.totalEarnings + amount,
                lastUpdated: new Date()
            }
        });
    },
    async deductWithdrawal (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        if (wallet.availableBalance < amount) {
            throw new Error('Insufficient balance');
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance - amount,
                totalWithdrawals: wallet.totalWithdrawals + amount,
                lastUpdated: new Date()
            }
        });
    },
    async findByUserId (userId) {
        return await this.getOrCreate(userId);
    }
};
const depositTransactionDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.create({
            data: {
                userId: data.userId,
                transactionId: data.transactionId,
                amount: data.amount,
                usdtAmount: data.usdtAmount,
                tronAddress: data.tronAddress,
                senderAddress: data.senderAddress,
                blockNumber: data.blockNumber,
                blockTimestamp: data.blockTimestamp,
                confirmations: data.confirmations || 0,
                status: 'PENDING'
            }
        });
    },
    async findByTransactionId (transactionId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findUnique({
            where: {
                transactionId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async findByUserId (userId, filters) {
        const where = {
            userId
        };
        if (filters?.status) {
            where.status = filters.status;
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 50,
            skip: filters?.offset,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async findAll (filters) {
        const where = {};
        if (filters?.status) {
            where.status = filters.status;
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 100,
            skip: filters?.offset,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async updateStatus (transactionId, status, updates) {
        const updateData = {
            status
        };
        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;
        if (updates?.processedAt) updateData.processedAt = updates.processedAt;
        if (updates?.failureReason) updateData.failureReason = updates.failureReason;
        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.update({
            where: {
                transactionId
            },
            data: updateData
        });
    },
    async markAsCompleted (transactionId) {
        return await this.updateStatus(transactionId, 'COMPLETED', {
            processedAt: new Date()
        });
    },
    async markAsFailed (transactionId, reason) {
        return await this.updateStatus(transactionId, 'FAILED', {
            failureReason: reason,
            processedAt: new Date()
        });
    },
    async getPendingDeposits () {
        return await this.findAll({
            status: 'PENDING'
        });
    },
    async getDepositStats () {
        const stats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.aggregate({
            _count: {
                id: true
            },
            _sum: {
                usdtAmount: true
            },
            where: {
                status: 'COMPLETED'
            }
        });
        const pendingCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.count({
            where: {
                status: 'PENDING'
            }
        });
        return {
            totalDeposits: stats._count.id || 0,
            totalAmount: stats._sum.usdtAmount || 0,
            pendingDeposits: pendingCount
        };
    }
};
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authenticateRequest": (()=>authenticateRequest),
    "createSession": (()=>createSession),
    "generateReferralId": (()=>generateReferralId),
    "generateToken": (()=>generateToken),
    "hashPassword": (()=>hashPassword),
    "isAdmin": (()=>isAdmin),
    "loginUser": (()=>loginUser),
    "registerUser": (()=>registerUser),
    "validateEmail": (()=>validateEmail),
    "validatePassword": (()=>validatePassword),
    "validateSession": (()=>validateSession),
    "verifyPassword": (()=>verifyPassword),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const hashPassword = async (password)=>{
    return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, 12);
};
const verifyPassword = async (password, hashedPassword)=>{
    return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
};
const generateToken = (payload)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN
    });
};
const verifyToken = (token)=>{
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
        return decoded;
    } catch (error) {
        return null;
    }
};
const generateReferralId = ()=>{
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = 'HC'; // HashCoreX prefix
    for(let i = 0; i < 8; i++){
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};
const authenticateRequest = async (request)=>{
    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;
    if (!token) {
        return {
            authenticated: false,
            user: null
        };
    }
    const decoded = verifyToken(token);
    if (!decoded) {
        return {
            authenticated: false,
            user: null
        };
    }
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(decoded.email);
    if (!user || !user.isActive) {
        return {
            authenticated: false,
            user: null
        };
    }
    return {
        authenticated: true,
        user
    };
};
const registerUser = async (data)=>{
    // Check if user already exists
    const existingUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(data.email);
    if (existingUser) {
        throw new Error('User already exists with this email');
    }
    // Validate referral code if provided
    let referrerId;
    if (data.referralCode) {
        const referrer = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByReferralId(data.referralCode);
        if (!referrer) {
            throw new Error('Invalid referral code');
        }
        referrerId = referrer.id;
    }
    // Hash password
    const passwordHash = await hashPassword(data.password);
    // Generate unique referral ID
    let referralId;
    let isUnique = false;
    do {
        referralId = generateReferralId();
        const existing = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByReferralId(referralId);
        isUnique = !existing;
    }while (!isUnique)
    // Create user in PostgreSQL
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].create({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        password: passwordHash,
        referralId
    });
    // Create referral relationship if referrer exists
    if (referrerId) {
        const { placeUserInBinaryTree, placeUserInSpecificSide } = await __turbopack_context__.r("[project]/src/lib/referral.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        if (data.placementSide) {
            // Place user in specific side if requested
            await placeUserInSpecificSide(referrerId, user.id, data.placementSide.toUpperCase());
        } else {
            // Place user in weaker leg automatically
            await placeUserInBinaryTree(referrerId, user.id);
        }
    }
    return {
        id: user.id,
        email: user.email,
        referralId: user.referralId,
        kycStatus: user.kycStatus
    };
};
const loginUser = async (data)=>{
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(data.email);
    if (!user) {
        throw new Error('Invalid email or password');
    }
    if (!user.isActive) {
        throw new Error('Account is deactivated');
    }
    const isValidPassword = await verifyPassword(data.password, user.password);
    if (!isValidPassword) {
        throw new Error('Invalid email or password');
    }
    const token = generateToken({
        userId: user.id,
        email: user.email
    });
    return {
        token,
        user: {
            id: user.id,
            email: user.email,
            referralId: user.referralId,
            kycStatus: user.kycStatus
        }
    };
};
const validatePassword = (password)=>{
    const errors = [];
    if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
        errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        errors.push('Password must contain at least one special character');
    }
    return {
        valid: errors.length === 0,
        errors
    };
};
const validateEmail = (email)=>{
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
const createSession = (userId, email)=>{
    return generateToken({
        userId,
        email
    });
};
const validateSession = (token)=>{
    return verifyToken(token);
};
const isAdmin = async (userId)=>{
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findById(userId);
    return user?.role === 'ADMIN';
};
}}),
"[project]/src/app/api/admin/users/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        const { authenticated, user } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authenticateRequest"])(request);
        if (!authenticated || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Not authenticated'
            }, {
                status: 401
            });
        }
        const userIsAdmin = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isAdmin"])(user.id);
        if (!userIsAdmin) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Admin access required'
            }, {
                status: 403
            });
        }
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '20');
        const search = searchParams.get('search') || '';
        const status = searchParams.get('status') || 'all';
        const skip = (page - 1) * limit;
        // Build where clause
        const where = {};
        if (search) {
            where.OR = [
                {
                    email: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    firstName: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    lastName: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    referralId: {
                        contains: search,
                        mode: 'insensitive'
                    }
                }
            ];
        }
        if (status !== 'all') {
            where.isActive = status === 'active';
        }
        // Get users with pagination
        const [users, totalCount] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findMany({
                where,
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    referralId: true,
                    role: true,
                    isActive: true,
                    kycStatus: true,
                    createdAt: true,
                    _count: {
                        select: {
                            miningUnits: true,
                            transactions: true
                        }
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                },
                skip,
                take: limit
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.count({
                where
            })
        ]);
        const totalPages = Math.ceil(totalCount / limit);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                users,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalCount,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        });
    } catch (error) {
        console.error('Admin users fetch error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch users'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__65499636._.js.map