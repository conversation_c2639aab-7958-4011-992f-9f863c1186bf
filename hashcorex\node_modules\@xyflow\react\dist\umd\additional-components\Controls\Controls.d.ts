import type { ControlProps } from './types';
declare function ControlsComponent({ style, showZoom, showFitView, showInteractive, fitViewOptions, onZoomIn, onZoomOut, onFitView, onInteractiveChange, className, children, position, orientation, 'aria-label': ariaLabel, }: ControlProps): import("react/jsx-runtime").JSX.Element;
declare namespace ControlsComponent {
    var displayName: string;
}
/**
 * The `<Controls />` component renders a small panel that contains convenient
 * buttons to zoom in, zoom out, fit the view, and lock the viewport.
 *
 * @public
 * @example
 *```tsx
 *import { ReactFlow, Controls } from '@xyflow/react'
 *
 *export default function Flow() {
 *  return (
 *    <ReactFlow nodes={[...]} edges={[...]}>
 *      <Controls />
 *    </ReactFlow>
 *  )
 *}
 *```
 *
 * @remarks To extend or customise the controls, you can use the [`<ControlButton />`](/api-reference/components/control-button) component
 *
 */
export declare const Controls: import("react").MemoExoticComponent<typeof ControlsComponent>;
export {};
//# sourceMappingURL=Controls.d.ts.map