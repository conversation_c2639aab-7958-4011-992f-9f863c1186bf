{"version": 3, "file": "component-props.d.ts", "sourceRoot": "", "sources": ["../../src/types/component-props.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,IAAI,eAAe,EAAE,UAAU,EAAE,MAAM,OAAO,CAAC;AACtG,OAAO,KAAK,EACV,cAAc,EACd,kBAAkB,EAClB,SAAS,EACT,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,OAAO,EACP,eAAe,EACf,UAAU,EACV,aAAa,EACb,MAAM,EACN,WAAW,EACX,SAAS,EACT,QAAQ,EACR,UAAU,EACV,UAAU,EACV,aAAa,EACb,OAAO,EACP,SAAS,EACT,QAAQ,EACR,WAAW,EACX,eAAe,EAChB,MAAM,gBAAgB,CAAC;AAExB,OAAO,KAAK,EACV,qBAAqB,EACrB,SAAS,EACT,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,uBAAuB,EACvB,MAAM,EACN,kBAAkB,EAClB,cAAc,EACd,aAAa,EACb,aAAa,EACb,QAAQ,EACR,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,oBAAoB,EACpB,gBAAgB,EAChB,UAAU,EACV,cAAc,EACd,iBAAiB,EAClB,MAAM,GAAG,CAAC;AAEX;;;GAGG;AACH,MAAM,WAAW,cAAc,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,SAAS,IAAI,GAAG,IAAI,CACxF,SAAQ,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,SAAS,CAAC;IACvD;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC;IACnB;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC;IACnB,2DAA2D;IAC3D,YAAY,CAAC,EAAE,QAAQ,EAAE,CAAC;IAC1B,2DAA2D;IAC3D,YAAY,CAAC,EAAE,QAAQ,EAAE,CAAC;IAC1B;;;;;;;;OAQG;IACH,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;IACxC,iEAAiE;IACjE,WAAW,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACzC,wEAAwE;IACxE,iBAAiB,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC/C,uEAAuE;IACvE,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC9C,2EAA2E;IAC3E,eAAe,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC7C,uEAAuE;IACvE,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC9C,uEAAuE;IACvE,iBAAiB,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC/C,sEAAsE;IACtE,eAAe,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;IACvC,6DAA6D;IAC7D,UAAU,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;IAClC,sEAAsE;IACtE,cAAc,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;IACtC,kEAAkE;IAClE,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,KAAK,IAAI,CAAC;IAC/D,wEAAwE;IACxE,iBAAiB,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC/C,wEAAwE;IACxE,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC9C,4EAA4E;IAC5E,eAAe,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC7C,wEAAwE;IACxE,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC9C,yEAAyE;IACzE,iBAAiB,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC/C;;;;OAIG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;IACpC;;OAEG;IACH,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,KAAK,IAAI,CAAC;IAC5F;;;OAGG;IACH,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,KAAK,IAAI,CAAC;IAClG;;;;;;;;;;;;;;;;;OAiBG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxC;;;;;;;;;;;;;;;;;OAiBG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxC,6DAA6D;IAC7D,aAAa,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxC,8DAA8D;IAC9D,aAAa,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxC,qEAAqE;IACrE,QAAQ,CAAC,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACxC,iFAAiF;IACjF,oBAAoB,CAAC,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACtD,wEAAwE;IACxE,eAAe,CAAC,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACjD,iFAAiF;IACjF,mBAAmB,CAAC,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACrD,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;IACpD,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;IAClD;;OAEG;IACH,sBAAsB,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,IAAI,CAAC;IAC7E;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,mFAAmF;IACnF,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC;;;;OAIG;IACH,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,mBAAmB,CAAC,EAAE,cAAc,CAAC;IACrC,iBAAiB,CAAC,EAAE,YAAY,CAAC;IACjC;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACpC,6FAA6F;IAC7F,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,qFAAqF;IACrF,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B;;;OAGG;IACH,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,iGAAiG;IACjG,iBAAiB,CAAC,EAAE,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC9D,uEAAuE;IACvE,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,UAAU,KAAK,IAAI,CAAC;IAC5C,uEAAuE;IACvE,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;IAC/C,6EAA6E;IAC7E,iBAAiB,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,GAAG,UAAU,KAAK,IAAI,CAAC;IAClE,iEAAiE;IACjE,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;IACpD,qEAAqE;IACrE,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;IACnD,iEAAiE;IACjE,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,KAAK,IAAI,CAAC;IACpD;;;OAGG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B;;;OAGG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B;;;OAGG;IACH,cAAc,CAAC,EAAE,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACpD;;;;;;;;;;;;;OAaG;IACH,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB;;;;;;;;;;;;;;OAcG;IACH,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;IACxC,mDAAmD;IACnD,mBAAmB,CAAC,EAAE,aAAa,CAAC;IACpC,uDAAuD;IACvD,uBAAuB,CAAC,EAAE,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAC5D,oEAAoE;IACpE,4BAA4B,CAAC,EAAE,aAAa,CAAC;IAC7C;;;;;OAKG;IACH,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC;;;;;;OAMG;IACH,aAAa,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAC/B;;;;;;;OAOG;IACH,gBAAgB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAClC;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B;;;;OAIG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B;;;;;;OAMG;IACH,oBAAoB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACtC;;;OAGG;IACH,qBAAqB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACvC;;;;;;;OAOG;IACH,qBAAqB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IACvC,8DAA8D;IAC9D,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;;OAGG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB;;;;;OAKG;IACH,yBAAyB,CAAC,EAAE,OAAO,CAAC;IACpC;;;;;OAKG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB;;;OAGG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB;;;;;;;;;OASG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB;;;;;OAKG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B;;;OAGG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B;;;;;;OAMG;IACH,SAAS,CAAC,EAAE,OAAO,GAAG,MAAM,EAAE,CAAC;IAC/B;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;OAGG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB;;;;;;;;;OASG;IACH,eAAe,CAAC,EAAE,QAAQ,CAAC;IAC3B;;OAEG;IACH,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;IAChD;;;;;OAKG;IACH,eAAe,CAAC,EAAE,gBAAgB,CAAC;IACnC;;;OAGG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;;;OAIG;IACH,UAAU,CAAC,EAAE,gBAAgB,CAAC;IAC9B;;;OAGG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B;;;OAGG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB;;;OAGG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;;;OAIG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B;;;;;OAKG;IACH,eAAe,CAAC,EAAE,eAAe,CAAC;IAClC;;;OAGG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B;;;OAGG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;;;;OAKG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B;;;;;OAKG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,+FAA+F;IAC/F,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;;;;;;;;;OAaG;IACH,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC;;;;;;;OAOG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB;;;;;;OAMG;IACH,mBAAmB,CAAC,EAAE,aAAa,CAAC;IACpC;;;;;;OAMG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB;;;OAGG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B;;OAEG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;;OAGG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B;;;;;OAKG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;;OAMG;IACH,iBAAiB,CAAC,EAAE,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAChD;;;;;OAKG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,uCAAuC;IACvC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,wCAAwC;IACxC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;;;OAIG;IACH,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB;;;OAGG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;CAC5C"}