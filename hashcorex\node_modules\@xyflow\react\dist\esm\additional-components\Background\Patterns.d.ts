import { BackgroundVariant } from './types';
type LinePatternProps = {
    dimensions: [number, number];
    variant: BackgroundVariant;
    lineWidth?: number;
    className?: string;
};
export declare function LinePattern({ dimensions, lineWidth, variant, className }: LinePatternProps): import("react/jsx-runtime").JSX.Element;
type DotPatternProps = {
    radius: number;
    className?: string;
};
export declare function DotPattern({ radius, className }: DotPatternProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=Patterns.d.ts.map