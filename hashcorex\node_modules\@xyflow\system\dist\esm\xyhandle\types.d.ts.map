{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../src/xyhandle/types.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,cAAc,EACd,KAAK,UAAU,EACf,KAAK,SAAS,EACd,KAAK,cAAc,EACnB,KAAK,UAAU,EACf,KAAK,KAAK,EACV,KAAK,SAAS,EACd,KAAK,MAAM,EACX,KAAK,YAAY,EACjB,KAAK,gBAAgB,EACrB,KAAK,iBAAiB,EACtB,UAAU,EACV,oBAAoB,EACrB,MAAM,UAAU,CAAC;AAElB,MAAM,MAAM,mBAAmB,GAAG;IAChC,gBAAgB,EAAE,OAAO,CAAC;IAC1B,cAAc,EAAE,cAAc,CAAC;IAC/B,gBAAgB,EAAE,MAAM,CAAC;IACzB,OAAO,EAAE,cAAc,GAAG,IAAI,CAAC;IAC/B,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,OAAO,CAAC;IAClB,UAAU,EAAE,UAAU,CAAC;IACvB,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,eAAe,CAAC,EAAE,UAAU,CAAC;IAC7B,gBAAgB,EAAE,gBAAgB,CAAC;IACnC,KAAK,EAAE,KAAK,CAAC;IACb,gBAAgB,EAAE,MAAM,IAAI,CAAC;IAC7B,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IACtC,cAAc,CAAC,EAAE,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU,EAAE,eAAe,EAAE,oBAAoB,KAAK,IAAI,CAAC;IAC/F,YAAY,EAAE,MAAM,SAAS,CAAC;IAC9B,aAAa,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC;IACnC,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG;IAC1B,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC;IACtD,cAAc,EAAE,cAAc,CAAC;IAC/B,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B,QAAQ,EAAE,UAAU,CAAC;IACrB,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IACtC,GAAG,EAAE,QAAQ,GAAG,UAAU,CAAC;IAC3B,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,UAAU,EAAE,UAAU,CAAC;CACxB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,aAAa,EAAE,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,EAAE,MAAM,EAAE,mBAAmB,KAAK,IAAI,CAAC;IACrF,OAAO,EAAE,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,EAAE,MAAM,EAAE,aAAa,KAAK,MAAM,CAAC;CAC5E,CAAC;AAEF,MAAM,MAAM,MAAM,GAAG;IACnB,aAAa,EAAE,OAAO,GAAG,IAAI,CAAC;IAC9B,OAAO,EAAE,OAAO,CAAC;IACjB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;IAC9B,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;CACzB,CAAC"}