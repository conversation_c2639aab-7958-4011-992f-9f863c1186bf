{"version": 3, "file": "eventhandler.d.ts", "sourceRoot": "", "sources": ["../../src/xypanzoom/eventhandler.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAG3C,OAAO,EACL,eAAe,EACf,KAAK,mBAAmB,EACxB,KAAK,aAAa,EAClB,KAAK,cAAc,EACnB,KAAK,SAAS,EACd,KAAK,gBAAgB,EACrB,KAAK,iBAAiB,EACvB,MAAM,UAAU,CAAC;AAGlB,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAE5C,MAAM,MAAM,iBAAiB,GAAG;IAC9B,aAAa,EAAE,aAAa,CAAC;IAC7B,gBAAgB,EAAE,MAAM,CAAC;IACzB,WAAW,EAAE,mBAAmB,CAAC;IACjC,MAAM,EAAE,cAAc,CAAC;IACvB,eAAe,EAAE,eAAe,CAAC;IACjC,gBAAgB,EAAE,MAAM,CAAC;IACzB,WAAW,EAAE,OAAO,CAAC;IACrB,cAAc,CAAC,EAAE,SAAS,CAAC;IAC3B,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,YAAY,CAAC,EAAE,SAAS,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B,gBAAgB,EAAE,MAAM,CAAC;IACzB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,aAAa,EAAE,aAAa,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B,aAAa,EAAE,aAAa,CAAC;IAC7B,gBAAgB,EAAE,gBAAgB,CAAC;IACnC,cAAc,CAAC,EAAE,SAAS,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG;IAC1B,aAAa,EAAE,aAAa,CAAC;IAC7B,SAAS,EAAE,OAAO,GAAG,MAAM,EAAE,CAAC;IAC9B,iBAAiB,EAAE,OAAO,CAAC;IAC3B,iBAAiB,EAAE,iBAAiB,CAAC;IACrC,SAAS,CAAC,EAAE,SAAS,CAAC;CACvB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,aAAa,EAAE,aAAa,CAAC;IAC7B,SAAS,EAAE,OAAO,GAAG,MAAM,EAAE,CAAC;IAC9B,WAAW,EAAE,OAAO,CAAC;IACrB,gBAAgB,EAAE,CAAC,UAAU,EAAE,OAAO,KAAK,IAAI,CAAC;IAChD,YAAY,CAAC,EAAE,SAAS,CAAC;IACzB,iBAAiB,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,CAAC;CAC1C,CAAC;AAEF,wBAAgB,wBAAwB,CAAC,EACvC,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,MAAM,EACN,eAAe,EACf,gBAAgB,EAChB,WAAW,EACX,cAAc,EACd,SAAS,EACT,YAAY,GACb,EAAE,iBAAiB,WACH,GAAG,uBAmEnB;AAED,wBAAgB,yBAAyB,CAAC,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,EAAE,kBAAkB,UAC1F,OAAO,SAAS,GAAG,KAAK,OAAO,sBAmBvD;AAED,wBAAgB,yBAAyB,CAAC,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,EAAE,kBAAkB,WAChG,WAAW,CAAC,cAAc,EAAE,GAAG,CAAC,UAoBhD;AAED,wBAAgB,oBAAoB,CAAC,EACnC,aAAa,EACb,SAAS,EACT,iBAAiB,EACjB,iBAAiB,EACjB,SAAS,GACV,EAAE,aAAa,WACC,WAAW,CAAC,cAAc,EAAE,GAAG,CAAC,UAahD;AAED,wBAAgB,uBAAuB,CAAC,EACtC,aAAa,EACb,SAAS,EACT,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,GAClB,EAAE,gBAAgB,WACF,WAAW,CAAC,cAAc,EAAE,GAAG,CAAC,UAiChD"}