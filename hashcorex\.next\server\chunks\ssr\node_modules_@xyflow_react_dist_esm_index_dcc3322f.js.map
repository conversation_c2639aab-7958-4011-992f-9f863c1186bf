{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/node_modules/%40xyflow/react/dist/esm/index.js"], "sourcesContent": ["\"use client\"\nimport { jsxs, Fragment, jsx } from 'react/jsx-runtime';\nimport { createContext, useContext, useMemo, forwardRef, useEffect, useRef, useState, useLayoutEffect, useCallback, memo } from 'react';\nimport cc from 'classcat';\nimport { errorMessages, mergeAriaLabelConfig, infiniteExtent, isInputDOMNode, getViewportForBounds, pointToRendererPoint, rendererPointToPoint, isNodeBase, isEdgeBase, getElementsToRemove, isRectObject, nodeToRect, getOverlappingArea, getNodesBounds, withResolvers, evaluateAbsolutePosition, getDimensions, XYPanZoom, PanOnScrollMode, SelectionMode, getEventPosition, getNodesInside, areSetsEqual, XYDrag, snapPosition, calculateNodePosition, Position, ConnectionMode, isMouseEvent, XYHandle, getHostForElement, addEdge, getInternalNodesBounds, isNumeric, nodeHasDimensions, getNodeDimensions, elementSelectionKeys, isEdgeVisible, MarkerType, createMarkerIds, getBezierEdgeCenter, getSmoothStepPath, getStraightPath, getBezierPath, getEdgePosition, getElevatedEdgeZIndex, getMarkerId, getConnectionStatus, ConnectionLineType, updateConnectionLookup, adoptUserNodes, initialConnection, devWarn, defaultAriaLabelConfig, updateNodeInternals, updateAbsolutePositions, handleExpandParent, panBy, fitViewport, isMacOs, areConnectionMapsEqual, handleConnectionChange, shallowNodeData, XYMinimap, getBoundsOfRects, ResizeControlVariant, XYResizer, XY_RESIZER_LINE_POSITIONS, XY_RESIZER_HANDLE_POSITIONS, getNodeToolbarTransform } from '@xyflow/system';\nexport { ConnectionLineType, ConnectionMode, MarkerType, PanOnScrollMode, Position, ResizeControlVariant, SelectionMode, addEdge, getBezierEdgeCenter, getBezierPath, getConnectedEdges, getEdgeCenter, getIncomers, getNodesBounds, getOutgoers, getSmoothStepPath, getStraightPath, getViewportForBounds, reconnectEdge } from '@xyflow/system';\nimport { useStoreWithEqualityFn, createWithEqualityFn } from 'zustand/traditional';\nimport { shallow } from 'zustand/shallow';\nimport { createPortal } from 'react-dom';\n\nconst StoreContext = createContext(null);\nconst Provider$1 = StoreContext.Provider;\n\nconst zustandErrorMessage = errorMessages['error001']();\n/**\n * This hook can be used to subscribe to internal state changes of the React Flow\n * component. The `useStore` hook is re-exported from the [Zustand](https://github.com/pmndrs/zustand)\n * state management library, so you should check out their docs for more details.\n *\n * @public\n * @param selector - A selector function that returns a slice of the flow's internal state.\n * Extracting or transforming just the state you need is a good practice to avoid unnecessary\n * re-renders.\n * @param equalityFn - A function to compare the previous and next value. This is incredibly useful\n * for preventing unnecessary re-renders. Good sensible defaults are using `Object.is` or importing\n * `zustand/shallow`, but you can be as granular as you like.\n * @returns The selected state slice.\n *\n * @example\n * ```ts\n * const nodes = useStore((state) => state.nodes);\n * ```\n *\n * @remarks This hook should only be used if there is no other way to access the internal\n * state. For many of the common use cases, there are dedicated hooks available\n * such as {@link useReactFlow}, {@link useViewport}, etc.\n */\nfunction useStore(selector, equalityFn) {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useStoreWithEqualityFn(store, selector, equalityFn);\n}\n/**\n * In some cases, you might need to access the store directly. This hook returns the store object which can be used on demand to access the state or dispatch actions.\n *\n * @returns The store object.\n * @example\n * ```ts\n * const store = useStoreApi();\n * ```\n *\n * @remarks This hook should only be used if there is no other way to access the internal\n * state. For many of the common use cases, there are dedicated hooks available\n * such as {@link useReactFlow}, {@link useViewport}, etc.\n */\nfunction useStoreApi() {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useMemo(() => ({\n        getState: store.getState,\n        setState: store.setState,\n        subscribe: store.subscribe,\n    }), [store]);\n}\n\nconst style = { display: 'none' };\nconst ariaLiveStyle = {\n    position: 'absolute',\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0px, 0px, 0px, 0px)',\n    clipPath: 'inset(100%)',\n};\nconst ARIA_NODE_DESC_KEY = 'react-flow__node-desc';\nconst ARIA_EDGE_DESC_KEY = 'react-flow__edge-desc';\nconst ARIA_LIVE_MESSAGE = 'react-flow__aria-live';\nconst ariaLiveSelector = (s) => s.ariaLiveMessage;\nconst ariaLabelConfigSelector = (s) => s.ariaLabelConfig;\nfunction AriaLiveMessage({ rfId }) {\n    const ariaLiveMessage = useStore(ariaLiveSelector);\n    return (jsx(\"div\", { id: `${ARIA_LIVE_MESSAGE}-${rfId}`, \"aria-live\": \"assertive\", \"aria-atomic\": \"true\", style: ariaLiveStyle, children: ariaLiveMessage }));\n}\nfunction A11yDescriptions({ rfId, disableKeyboardA11y }) {\n    const ariaLabelConfig = useStore(ariaLabelConfigSelector);\n    return (jsxs(Fragment, { children: [jsx(\"div\", { id: `${ARIA_NODE_DESC_KEY}-${rfId}`, style: style, children: disableKeyboardA11y\n                    ? ariaLabelConfig['node.a11yDescription.default']\n                    : ariaLabelConfig['node.a11yDescription.keyboardDisabled'] }), jsx(\"div\", { id: `${ARIA_EDGE_DESC_KEY}-${rfId}`, style: style, children: ariaLabelConfig['edge.a11yDescription.default'] }), !disableKeyboardA11y && jsx(AriaLiveMessage, { rfId: rfId })] }));\n}\n\nconst selector$n = (s) => (s.userSelectionActive ? 'none' : 'all');\n/**\n * The `<Panel />` component helps you position content above the viewport.\n * It is used internally by the [`<MiniMap />`](/api-reference/components/minimap)\n * and [`<Controls />`](/api-reference/components/controls) components.\n *\n * @public\n *\n * @example\n * ```jsx\n *import { ReactFlow, Background, Panel } from '@xyflow/react';\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[]} fitView>\n *      <Panel position=\"top-left\">top-left</Panel>\n *      <Panel position=\"top-center\">top-center</Panel>\n *      <Panel position=\"top-right\">top-right</Panel>\n *      <Panel position=\"bottom-left\">bottom-left</Panel>\n *      <Panel position=\"bottom-center\">bottom-center</Panel>\n *      <Panel position=\"bottom-right\">bottom-right</Panel>\n *    </ReactFlow>\n *  );\n *}\n *```\n */\nconst Panel = forwardRef(({ position = 'top-left', children, className, style, ...rest }, ref) => {\n    const pointerEvents = useStore(selector$n);\n    const positionClasses = `${position}`.split('-');\n    return (jsx(\"div\", { className: cc(['react-flow__panel', className, ...positionClasses]), style: { ...style, pointerEvents }, ref: ref, ...rest, children: children }));\n});\nPanel.displayName = 'Panel';\n\nfunction Attribution({ proOptions, position = 'bottom-right' }) {\n    if (proOptions?.hideAttribution) {\n        return null;\n    }\n    return (jsx(Panel, { position: position, className: \"react-flow__attribution\", \"data-message\": \"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev\", children: jsx(\"a\", { href: \"https://reactflow.dev\", target: \"_blank\", rel: \"noopener noreferrer\", \"aria-label\": \"React Flow attribution\", children: \"React Flow\" }) }));\n}\n\nconst selector$m = (s) => {\n    const selectedNodes = [];\n    const selectedEdges = [];\n    for (const [, node] of s.nodeLookup) {\n        if (node.selected) {\n            selectedNodes.push(node.internals.userNode);\n        }\n    }\n    for (const [, edge] of s.edgeLookup) {\n        if (edge.selected) {\n            selectedEdges.push(edge);\n        }\n    }\n    return { selectedNodes, selectedEdges };\n};\nconst selectId = (obj) => obj.id;\nfunction areEqual(a, b) {\n    return (shallow(a.selectedNodes.map(selectId), b.selectedNodes.map(selectId)) &&\n        shallow(a.selectedEdges.map(selectId), b.selectedEdges.map(selectId)));\n}\nfunction SelectionListenerInner({ onSelectionChange, }) {\n    const store = useStoreApi();\n    const { selectedNodes, selectedEdges } = useStore(selector$m, areEqual);\n    useEffect(() => {\n        const params = { nodes: selectedNodes, edges: selectedEdges };\n        onSelectionChange?.(params);\n        store.getState().onSelectionChangeHandlers.forEach((fn) => fn(params));\n    }, [selectedNodes, selectedEdges, onSelectionChange]);\n    return null;\n}\nconst changeSelector = (s) => !!s.onSelectionChangeHandlers;\nfunction SelectionListener({ onSelectionChange, }) {\n    const storeHasSelectionChangeHandlers = useStore(changeSelector);\n    if (onSelectionChange || storeHasSelectionChangeHandlers) {\n        return jsx(SelectionListenerInner, { onSelectionChange: onSelectionChange });\n    }\n    return null;\n}\n\nconst defaultNodeOrigin = [0, 0];\nconst defaultViewport = { x: 0, y: 0, zoom: 1 };\n\n/*\n * This component helps us to update the store with the values coming from the user.\n * We distinguish between values we can update directly with `useDirectStoreUpdater` (like `snapGrid`)\n * and values that have a dedicated setter function in the store (like `setNodes`).\n */\n// These fields exist in the global store, and we need to keep them up to date\nconst reactFlowFieldsToTrack = [\n    'nodes',\n    'edges',\n    'defaultNodes',\n    'defaultEdges',\n    'onConnect',\n    'onConnectStart',\n    'onConnectEnd',\n    'onClickConnectStart',\n    'onClickConnectEnd',\n    'nodesDraggable',\n    'autoPanOnNodeFocus',\n    'nodesConnectable',\n    'nodesFocusable',\n    'edgesFocusable',\n    'edgesReconnectable',\n    'elevateNodesOnSelect',\n    'elevateEdgesOnSelect',\n    'minZoom',\n    'maxZoom',\n    'nodeExtent',\n    'onNodesChange',\n    'onEdgesChange',\n    'elementsSelectable',\n    'connectionMode',\n    'snapGrid',\n    'snapToGrid',\n    'translateExtent',\n    'connectOnClick',\n    'defaultEdgeOptions',\n    'fitView',\n    'fitViewOptions',\n    'onNodesDelete',\n    'onEdgesDelete',\n    'onDelete',\n    'onNodeDrag',\n    'onNodeDragStart',\n    'onNodeDragStop',\n    'onSelectionDrag',\n    'onSelectionDragStart',\n    'onSelectionDragStop',\n    'onMoveStart',\n    'onMove',\n    'onMoveEnd',\n    'noPanClassName',\n    'nodeOrigin',\n    'autoPanOnConnect',\n    'autoPanOnNodeDrag',\n    'onError',\n    'connectionRadius',\n    'isValidConnection',\n    'selectNodesOnDrag',\n    'nodeDragThreshold',\n    'onBeforeDelete',\n    'debug',\n    'autoPanSpeed',\n    'paneClickDistance',\n    'ariaLabelConfig',\n];\n// rfId doesn't exist in ReactFlowProps, but it's one of the fields we want to update\nconst fieldsToTrack = [...reactFlowFieldsToTrack, 'rfId'];\nconst selector$l = (s) => ({\n    setNodes: s.setNodes,\n    setEdges: s.setEdges,\n    setMinZoom: s.setMinZoom,\n    setMaxZoom: s.setMaxZoom,\n    setTranslateExtent: s.setTranslateExtent,\n    setNodeExtent: s.setNodeExtent,\n    reset: s.reset,\n    setDefaultNodesAndEdges: s.setDefaultNodesAndEdges,\n    setPaneClickDistance: s.setPaneClickDistance,\n});\nconst initPrevValues = {\n    /*\n     * these are values that are also passed directly to other components\n     * than the StoreUpdater. We can reduce the number of setStore calls\n     * by setting the same values here as prev fields.\n     */\n    translateExtent: infiniteExtent,\n    nodeOrigin: defaultNodeOrigin,\n    minZoom: 0.5,\n    maxZoom: 2,\n    elementsSelectable: true,\n    noPanClassName: 'nopan',\n    rfId: '1',\n    paneClickDistance: 0,\n};\nfunction StoreUpdater(props) {\n    const { setNodes, setEdges, setMinZoom, setMaxZoom, setTranslateExtent, setNodeExtent, reset, setDefaultNodesAndEdges, setPaneClickDistance, } = useStore(selector$l, shallow);\n    const store = useStoreApi();\n    useEffect(() => {\n        setDefaultNodesAndEdges(props.defaultNodes, props.defaultEdges);\n        return () => {\n            // when we reset the store we also need to reset the previous fields\n            previousFields.current = initPrevValues;\n            reset();\n        };\n    }, []);\n    const previousFields = useRef(initPrevValues);\n    useEffect(() => {\n        for (const fieldName of fieldsToTrack) {\n            const fieldValue = props[fieldName];\n            const previousFieldValue = previousFields.current[fieldName];\n            if (fieldValue === previousFieldValue)\n                continue;\n            if (typeof props[fieldName] === 'undefined')\n                continue;\n            // Custom handling with dedicated setters for some fields\n            if (fieldName === 'nodes')\n                setNodes(fieldValue);\n            else if (fieldName === 'edges')\n                setEdges(fieldValue);\n            else if (fieldName === 'minZoom')\n                setMinZoom(fieldValue);\n            else if (fieldName === 'maxZoom')\n                setMaxZoom(fieldValue);\n            else if (fieldName === 'translateExtent')\n                setTranslateExtent(fieldValue);\n            else if (fieldName === 'nodeExtent')\n                setNodeExtent(fieldValue);\n            else if (fieldName === 'paneClickDistance')\n                setPaneClickDistance(fieldValue);\n            // Renamed fields\n            else if (fieldName === 'fitView')\n                store.setState({ fitViewQueued: fieldValue });\n            else if (fieldName === 'fitViewOptions')\n                store.setState({ fitViewOptions: fieldValue });\n            if (fieldName === 'ariaLabelConfig') {\n                store.setState({ ariaLabelConfig: mergeAriaLabelConfig(fieldValue) });\n            }\n            // General case\n            else\n                store.setState({ [fieldName]: fieldValue });\n        }\n        previousFields.current = props;\n    }, \n    // Only re-run the effect if one of the fields we track changes\n    fieldsToTrack.map((fieldName) => props[fieldName]));\n    return null;\n}\n\nfunction getMediaQuery() {\n    if (typeof window === 'undefined' || !window.matchMedia) {\n        return null;\n    }\n    return window.matchMedia('(prefers-color-scheme: dark)');\n}\n/**\n * Hook for receiving the current color mode class 'dark' or 'light'.\n *\n * @internal\n * @param colorMode - The color mode to use ('dark', 'light' or 'system')\n */\nfunction useColorModeClass(colorMode) {\n    const [colorModeClass, setColorModeClass] = useState(colorMode === 'system' ? null : colorMode);\n    useEffect(() => {\n        if (colorMode !== 'system') {\n            setColorModeClass(colorMode);\n            return;\n        }\n        const mediaQuery = getMediaQuery();\n        const updateColorModeClass = () => setColorModeClass(mediaQuery?.matches ? 'dark' : 'light');\n        updateColorModeClass();\n        mediaQuery?.addEventListener('change', updateColorModeClass);\n        return () => {\n            mediaQuery?.removeEventListener('change', updateColorModeClass);\n        };\n    }, [colorMode]);\n    return colorModeClass !== null ? colorModeClass : getMediaQuery()?.matches ? 'dark' : 'light';\n}\n\nconst defaultDoc = typeof document !== 'undefined' ? document : null;\n/**\n * This hook lets you listen for specific key codes and tells you whether they are\n * currently pressed or not.\n *\n * @public\n * @param options - Options\n *\n * @example\n * ```tsx\n *import { useKeyPress } from '@xyflow/react';\n *\n *export default function () {\n *  const spacePressed = useKeyPress('Space');\n *  const cmdAndSPressed = useKeyPress(['Meta+s', 'Strg+s']);\n *\n *  return (\n *    <div>\n *     {spacePressed && <p>Space pressed!</p>}\n *     {cmdAndSPressed && <p>Cmd + S pressed!</p>}\n *    </div>\n *  );\n *}\n *```\n */\nfunction useKeyPress(\n/**\n * The key code (string or array of strings) specifies which key(s) should trigger\n * an action.\n *\n * A **string** can represent:\n * - A **single key**, e.g. `'a'`\n * - A **key combination**, using `'+'` to separate keys, e.g. `'a+d'`\n *\n * An  **array of strings** represents **multiple possible key inputs**. For example, `['a', 'd+s']`\n * means the user can press either the single key `'a'` or the combination of `'d'` and `'s'`.\n * @default null\n */\nkeyCode = null, options = { target: defaultDoc, actInsideInputWithModifier: true }) {\n    const [keyPressed, setKeyPressed] = useState(false);\n    // we need to remember if a modifier key is pressed in order to track it\n    const modifierPressed = useRef(false);\n    // we need to remember the pressed keys in order to support combinations\n    const pressedKeys = useRef(new Set([]));\n    /*\n     * keyCodes = array with single keys [['a']] or key combinations [['a', 's']]\n     * keysToWatch = array with all keys flattened ['a', 'd', 'ShiftLeft']\n     * used to check if we store event.code or event.key. When the code is in the list of keysToWatch\n     * we use the code otherwise the key. Explainer: When you press the left \"command\" key, the code is \"MetaLeft\"\n     * and the key is \"Meta\". We want users to be able to pass keys and codes so we assume that the key is meant when\n     * we can't find it in the list of keysToWatch.\n     */\n    const [keyCodes, keysToWatch] = useMemo(() => {\n        if (keyCode !== null) {\n            const keyCodeArr = Array.isArray(keyCode) ? keyCode : [keyCode];\n            const keys = keyCodeArr\n                .filter((kc) => typeof kc === 'string')\n                /*\n                 * we first replace all '+' with '\\n'  which we will use to split the keys on\n                 * then we replace '\\n\\n' with '\\n+', this way we can also support the combination 'key++'\n                 * in the end we simply split on '\\n' to get the key array\n                 */\n                .map((kc) => kc.replace('+', '\\n').replace('\\n\\n', '\\n+').split('\\n'));\n            const keysFlat = keys.reduce((res, item) => res.concat(...item), []);\n            return [keys, keysFlat];\n        }\n        return [[], []];\n    }, [keyCode]);\n    useEffect(() => {\n        const target = options?.target ?? defaultDoc;\n        const actInsideInputWithModifier = options?.actInsideInputWithModifier ?? true;\n        if (keyCode !== null) {\n            const downHandler = (event) => {\n                modifierPressed.current = event.ctrlKey || event.metaKey || event.shiftKey || event.altKey;\n                const preventAction = (!modifierPressed.current || (modifierPressed.current && !actInsideInputWithModifier)) &&\n                    isInputDOMNode(event);\n                if (preventAction) {\n                    return false;\n                }\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                pressedKeys.current.add(event[keyOrCode]);\n                if (isMatchingKey(keyCodes, pressedKeys.current, false)) {\n                    const target = (event.composedPath?.()?.[0] || event.target);\n                    const isInteractiveElement = target?.nodeName === 'BUTTON' || target?.nodeName === 'A';\n                    if (options.preventDefault !== false && (modifierPressed.current || !isInteractiveElement)) {\n                        event.preventDefault();\n                    }\n                    setKeyPressed(true);\n                }\n            };\n            const upHandler = (event) => {\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                if (isMatchingKey(keyCodes, pressedKeys.current, true)) {\n                    setKeyPressed(false);\n                    pressedKeys.current.clear();\n                }\n                else {\n                    pressedKeys.current.delete(event[keyOrCode]);\n                }\n                // fix for Mac: when cmd key is pressed, keyup is not triggered for any other key, see: https://stackoverflow.com/questions/27380018/when-cmd-key-is-kept-pressed-keyup-is-not-triggered-for-any-other-key\n                if (event.key === 'Meta') {\n                    pressedKeys.current.clear();\n                }\n                modifierPressed.current = false;\n            };\n            const resetHandler = () => {\n                pressedKeys.current.clear();\n                setKeyPressed(false);\n            };\n            target?.addEventListener('keydown', downHandler);\n            target?.addEventListener('keyup', upHandler);\n            window.addEventListener('blur', resetHandler);\n            window.addEventListener('contextmenu', resetHandler);\n            return () => {\n                target?.removeEventListener('keydown', downHandler);\n                target?.removeEventListener('keyup', upHandler);\n                window.removeEventListener('blur', resetHandler);\n                window.removeEventListener('contextmenu', resetHandler);\n            };\n        }\n    }, [keyCode, setKeyPressed]);\n    return keyPressed;\n}\n// utils\nfunction isMatchingKey(keyCodes, pressedKeys, isUp) {\n    return (keyCodes\n        /*\n         * we only want to compare same sizes of keyCode definitions\n         * and pressed keys. When the user specified 'Meta' as a key somewhere\n         * this would also be truthy without this filter when user presses 'Meta' + 'r'\n         */\n        .filter((keys) => isUp || keys.length === pressedKeys.size)\n        /*\n         * since we want to support multiple possibilities only one of the\n         * combinations need to be part of the pressed keys\n         */\n        .some((keys) => keys.every((k) => pressedKeys.has(k))));\n}\nfunction useKeyOrCode(eventCode, keysToWatch) {\n    return keysToWatch.includes(eventCode) ? 'code' : 'key';\n}\n\n/**\n * Hook for getting viewport helper functions.\n *\n * @internal\n * @returns viewport helper functions\n */\nconst useViewportHelper = () => {\n    const store = useStoreApi();\n    return useMemo(() => {\n        return {\n            zoomIn: (options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleBy(1.2, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            zoomOut: (options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleBy(1 / 1.2, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            zoomTo: (zoomLevel, options) => {\n                const { panZoom } = store.getState();\n                return panZoom ? panZoom.scaleTo(zoomLevel, { duration: options?.duration }) : Promise.resolve(false);\n            },\n            getZoom: () => store.getState().transform[2],\n            setViewport: async (viewport, options) => {\n                const { transform: [tX, tY, tZoom], panZoom, } = store.getState();\n                if (!panZoom) {\n                    return Promise.resolve(false);\n                }\n                await panZoom.setViewport({\n                    x: viewport.x ?? tX,\n                    y: viewport.y ?? tY,\n                    zoom: viewport.zoom ?? tZoom,\n                }, options);\n                return Promise.resolve(true);\n            },\n            getViewport: () => {\n                const [x, y, zoom] = store.getState().transform;\n                return { x, y, zoom };\n            },\n            setCenter: async (x, y, options) => {\n                return store.getState().setCenter(x, y, options);\n            },\n            fitBounds: async (bounds, options) => {\n                const { width, height, minZoom, maxZoom, panZoom } = store.getState();\n                const viewport = getViewportForBounds(bounds, width, height, minZoom, maxZoom, options?.padding ?? 0.1);\n                if (!panZoom) {\n                    return Promise.resolve(false);\n                }\n                await panZoom.setViewport(viewport, {\n                    duration: options?.duration,\n                    ease: options?.ease,\n                    interpolate: options?.interpolate,\n                });\n                return Promise.resolve(true);\n            },\n            screenToFlowPosition: (clientPosition, options = {}) => {\n                const { transform, snapGrid, snapToGrid, domNode } = store.getState();\n                if (!domNode) {\n                    return clientPosition;\n                }\n                const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                const correctedPosition = {\n                    x: clientPosition.x - domX,\n                    y: clientPosition.y - domY,\n                };\n                const _snapGrid = options.snapGrid ?? snapGrid;\n                const _snapToGrid = options.snapToGrid ?? snapToGrid;\n                return pointToRendererPoint(correctedPosition, transform, _snapToGrid, _snapGrid);\n            },\n            flowToScreenPosition: (flowPosition) => {\n                const { transform, domNode } = store.getState();\n                if (!domNode) {\n                    return flowPosition;\n                }\n                const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                const rendererPosition = rendererPointToPoint(flowPosition, transform);\n                return {\n                    x: rendererPosition.x + domX,\n                    y: rendererPosition.y + domY,\n                };\n            },\n        };\n    }, []);\n};\n\n/*\n * This function applies changes to nodes or edges that are triggered by React Flow internally.\n * When you drag a node for example, React Flow will send a position change update.\n * This function then applies the changes and returns the updated elements.\n */\nfunction applyChanges(changes, elements) {\n    const updatedElements = [];\n    /*\n     * By storing a map of changes for each element, we can a quick lookup as we\n     * iterate over the elements array!\n     */\n    const changesMap = new Map();\n    const addItemChanges = [];\n    for (const change of changes) {\n        if (change.type === 'add') {\n            addItemChanges.push(change);\n            continue;\n        }\n        else if (change.type === 'remove' || change.type === 'replace') {\n            /*\n             * For a 'remove' change we can safely ignore any other changes queued for\n             * the same element, it's going to be removed anyway!\n             */\n            changesMap.set(change.id, [change]);\n        }\n        else {\n            const elementChanges = changesMap.get(change.id);\n            if (elementChanges) {\n                /*\n                 * If we have some changes queued already, we can do a mutable update of\n                 * that array and save ourselves some copying.\n                 */\n                elementChanges.push(change);\n            }\n            else {\n                changesMap.set(change.id, [change]);\n            }\n        }\n    }\n    for (const element of elements) {\n        const changes = changesMap.get(element.id);\n        /*\n         * When there are no changes for an element we can just push it unmodified,\n         * no need to copy it.\n         */\n        if (!changes) {\n            updatedElements.push(element);\n            continue;\n        }\n        // If we have a 'remove' change queued, it'll be the only change in the array\n        if (changes[0].type === 'remove') {\n            continue;\n        }\n        if (changes[0].type === 'replace') {\n            updatedElements.push({ ...changes[0].item });\n            continue;\n        }\n        /**\n         * For other types of changes, we want to start with a shallow copy of the\n         * object so React knows this element has changed. Sequential changes will\n         * each _mutate_ this object, so there's only ever one copy.\n         */\n        const updatedElement = { ...element };\n        for (const change of changes) {\n            applyChange(change, updatedElement);\n        }\n        updatedElements.push(updatedElement);\n    }\n    /*\n     * we need to wait for all changes to be applied before adding new items\n     * to be able to add them at the correct index\n     */\n    if (addItemChanges.length) {\n        addItemChanges.forEach((change) => {\n            if (change.index !== undefined) {\n                updatedElements.splice(change.index, 0, { ...change.item });\n            }\n            else {\n                updatedElements.push({ ...change.item });\n            }\n        });\n    }\n    return updatedElements;\n}\n// Applies a single change to an element. This is a *mutable* update.\nfunction applyChange(change, element) {\n    switch (change.type) {\n        case 'select': {\n            element.selected = change.selected;\n            break;\n        }\n        case 'position': {\n            if (typeof change.position !== 'undefined') {\n                element.position = change.position;\n            }\n            if (typeof change.dragging !== 'undefined') {\n                element.dragging = change.dragging;\n            }\n            break;\n        }\n        case 'dimensions': {\n            if (typeof change.dimensions !== 'undefined') {\n                element.measured ??= {};\n                element.measured.width = change.dimensions.width;\n                element.measured.height = change.dimensions.height;\n                if (change.setAttributes) {\n                    if (change.setAttributes === true || change.setAttributes === 'width') {\n                        element.width = change.dimensions.width;\n                    }\n                    if (change.setAttributes === true || change.setAttributes === 'height') {\n                        element.height = change.dimensions.height;\n                    }\n                }\n            }\n            if (typeof change.resizing === 'boolean') {\n                element.resizing = change.resizing;\n            }\n            break;\n        }\n    }\n}\n/**\n * Drop in function that applies node changes to an array of nodes.\n * @public\n * @param changes - Array of changes to apply.\n * @param nodes - Array of nodes to apply the changes to.\n * @returns Array of updated nodes.\n * @example\n *```tsx\n *import { useState, useCallback } from 'react';\n *import { ReactFlow, applyNodeChanges, type Node, type Edge, type OnNodesChange } from '@xyflow/react';\n *\n *export default function Flow() {\n *  const [nodes, setNodes] = useState<Node[]>([]);\n *  const [edges, setEdges] = useState<Edge[]>([]);\n *  const onNodesChange: OnNodesChange = useCallback(\n *    (changes) => {\n *      setNodes((oldNodes) => applyNodeChanges(changes, oldNodes));\n *    },\n *    [setNodes],\n *  );\n *\n *  return (\n *    <ReactFlow nodes={nodes} edges={edges} onNodesChange={onNodesChange} />\n *  );\n *}\n *```\n * @remarks Various events on the <ReactFlow /> component can produce an {@link NodeChange}\n * that describes how to update the edges of your flow in some way.\n * If you don't need any custom behaviour, this util can be used to take an array\n * of these changes and apply them to your edges.\n */\nfunction applyNodeChanges(changes, nodes) {\n    return applyChanges(changes, nodes);\n}\n/**\n * Drop in function that applies edge changes to an array of edges.\n * @public\n * @param changes - Array of changes to apply.\n * @param edges - Array of edge to apply the changes to.\n * @returns Array of updated edges.\n * @example\n * ```tsx\n *import { useState, useCallback } from 'react';\n *import { ReactFlow, applyEdgeChanges } from '@xyflow/react';\n *\n *export default function Flow() {\n *  const [nodes, setNodes] = useState([]);\n *  const [edges, setEdges] = useState([]);\n *  const onEdgesChange = useCallback(\n *    (changes) => {\n *      setEdges((oldEdges) => applyEdgeChanges(changes, oldEdges));\n *    },\n *    [setEdges],\n *  );\n *\n *  return (\n *    <ReactFlow nodes={nodes} edges={edges} onEdgesChange={onEdgesChange} />\n *  );\n *}\n *```\n * @remarks Various events on the <ReactFlow /> component can produce an {@link EdgeChange}\n * that describes how to update the edges of your flow in some way.\n * If you don't need any custom behaviour, this util can be used to take an array\n * of these changes and apply them to your edges.\n */\nfunction applyEdgeChanges(changes, edges) {\n    return applyChanges(changes, edges);\n}\nfunction createSelectionChange(id, selected) {\n    return {\n        id,\n        type: 'select',\n        selected,\n    };\n}\nfunction getSelectionChanges(items, selectedIds = new Set(), mutateItem = false) {\n    const changes = [];\n    for (const [id, item] of items) {\n        const willBeSelected = selectedIds.has(id);\n        // we don't want to set all items to selected=false on the first selection\n        if (!(item.selected === undefined && !willBeSelected) && item.selected !== willBeSelected) {\n            if (mutateItem) {\n                /*\n                 * this hack is needed for nodes. When the user dragged a node, it's selected.\n                 * When another node gets dragged, we need to deselect the previous one,\n                 * in order to have only one selected node at a time - the onNodesChange callback comes too late here :/\n                 */\n                item.selected = willBeSelected;\n            }\n            changes.push(createSelectionChange(item.id, willBeSelected));\n        }\n    }\n    return changes;\n}\nfunction getElementsDiffChanges({ items = [], lookup, }) {\n    const changes = [];\n    const itemsLookup = new Map(items.map((item) => [item.id, item]));\n    for (const [index, item] of items.entries()) {\n        const lookupItem = lookup.get(item.id);\n        const storeItem = lookupItem?.internals?.userNode ?? lookupItem;\n        if (storeItem !== undefined && storeItem !== item) {\n            changes.push({ id: item.id, item: item, type: 'replace' });\n        }\n        if (storeItem === undefined) {\n            changes.push({ item: item, type: 'add', index });\n        }\n    }\n    for (const [id] of lookup) {\n        const nextNode = itemsLookup.get(id);\n        if (nextNode === undefined) {\n            changes.push({ id, type: 'remove' });\n        }\n    }\n    return changes;\n}\nfunction elementToRemoveChange(item) {\n    return {\n        id: item.id,\n        type: 'remove',\n    };\n}\n\n/**\n * Test whether an object is usable as an [`Node`](/api-reference/types/node).\n * In TypeScript this is a type guard that will narrow the type of whatever you pass in to\n * [`Node`](/api-reference/types/node) if it returns `true`.\n *\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Node if it returns true\n * @param element - The element to test.\n * @returns Tests whether the provided value can be used as a `Node`. If you're using TypeScript,\n * this function acts as a type guard and will narrow the type of the value to `Node` if it returns\n * `true`.\n *\n * @example\n * ```js\n *import { isNode } from '@xyflow/react';\n *\n *if (isNode(node)) {\n * // ...\n *}\n *```\n */\nconst isNode = (element) => isNodeBase(element);\n/**\n * Test whether an object is usable as an [`Edge`](/api-reference/types/edge).\n * In TypeScript this is a type guard that will narrow the type of whatever you pass in to\n * [`Edge`](/api-reference/types/edge) if it returns `true`.\n *\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Edge if it returns true\n * @param element - The element to test\n * @returns Tests whether the provided value can be used as an `Edge`. If you're using TypeScript,\n * this function acts as a type guard and will narrow the type of the value to `Edge` if it returns\n * `true`.\n *\n * @example\n * ```js\n *import { isEdge } from '@xyflow/react';\n *\n *if (isEdge(edge)) {\n * // ...\n *}\n *```\n */\nconst isEdge = (element) => isEdgeBase(element);\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\nfunction fixedForwardRef(render) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return forwardRef(render);\n}\n\n// we need this hook to prevent a warning when using react-flow in SSR\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\n\n/**\n * This hook returns a queue that can be used to batch updates.\n *\n * @param runQueue - a function that gets called when the queue is flushed\n * @internal\n *\n * @returns a Queue object\n */\nfunction useQueue(runQueue) {\n    /*\n     * Because we're using a ref above, we need some way to let React know when to\n     * actually process the queue. We increment this number any time we mutate the\n     * queue, creating a new state to trigger the layout effect below.\n     * Using a boolean dirty flag here instead would lead to issues related to\n     * automatic batching. (https://github.com/xyflow/xyflow/issues/4779)\n     */\n    const [serial, setSerial] = useState(BigInt(0));\n    /*\n     * A reference of all the batched updates to process before the next render. We\n     * want a reference here so multiple synchronous calls to `setNodes` etc can be\n     * batched together.\n     */\n    const [queue] = useState(() => createQueue(() => setSerial(n => n + BigInt(1))));\n    /*\n     * Layout effects are guaranteed to run before the next render which means we\n     * shouldn't run into any issues with stale state or weird issues that come from\n     * rendering things one frame later than expected (we used to use `setTimeout`).\n     */\n    useIsomorphicLayoutEffect(() => {\n        const queueItems = queue.get();\n        if (queueItems.length) {\n            runQueue(queueItems);\n            queue.reset();\n        }\n    }, [serial]);\n    return queue;\n}\nfunction createQueue(cb) {\n    let queue = [];\n    return {\n        get: () => queue,\n        reset: () => {\n            queue = [];\n        },\n        push: (item) => {\n            queue.push(item);\n            cb();\n        },\n    };\n}\n\nconst BatchContext = createContext(null);\n/**\n * This is a context provider that holds and processes the node and edge update queues\n * that are needed to handle setNodes, addNodes, setEdges and addEdges.\n *\n * @internal\n */\nfunction BatchProvider({ children, }) {\n    const store = useStoreApi();\n    const nodeQueueHandler = useCallback((queueItems) => {\n        const { nodes = [], setNodes, hasDefaultNodes, onNodesChange, nodeLookup, fitViewQueued } = store.getState();\n        /*\n         * This is essentially an `Array.reduce` in imperative clothing. Processing\n         * this queue is a relatively hot path so we'd like to avoid the overhead of\n         * array methods where we can.\n         */\n        let next = nodes;\n        for (const payload of queueItems) {\n            next = typeof payload === 'function' ? payload(next) : payload;\n        }\n        const changes = getElementsDiffChanges({\n            items: next,\n            lookup: nodeLookup,\n        });\n        if (hasDefaultNodes) {\n            setNodes(next);\n        }\n        // We only want to fire onNodesChange if there are changes to the nodes\n        if (changes.length > 0) {\n            onNodesChange?.(changes);\n        }\n        else if (fitViewQueued) {\n            // If there are no changes to the nodes, we still need to call setNodes\n            // to trigger a re-render and fitView.\n            window.requestAnimationFrame(() => {\n                const { fitViewQueued, nodes, setNodes } = store.getState();\n                if (fitViewQueued) {\n                    setNodes(nodes);\n                }\n            });\n        }\n    }, []);\n    const nodeQueue = useQueue(nodeQueueHandler);\n    const edgeQueueHandler = useCallback((queueItems) => {\n        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange, edgeLookup } = store.getState();\n        let next = edges;\n        for (const payload of queueItems) {\n            next = typeof payload === 'function' ? payload(next) : payload;\n        }\n        if (hasDefaultEdges) {\n            setEdges(next);\n        }\n        else if (onEdgesChange) {\n            onEdgesChange(getElementsDiffChanges({\n                items: next,\n                lookup: edgeLookup,\n            }));\n        }\n    }, []);\n    const edgeQueue = useQueue(edgeQueueHandler);\n    const value = useMemo(() => ({ nodeQueue, edgeQueue }), []);\n    return jsx(BatchContext.Provider, { value: value, children: children });\n}\nfunction useBatchContext() {\n    const batchContext = useContext(BatchContext);\n    if (!batchContext) {\n        throw new Error('useBatchContext must be used within a BatchProvider');\n    }\n    return batchContext;\n}\n\nconst selector$k = (s) => !!s.panZoom;\n/**\n * This hook returns a ReactFlowInstance that can be used to update nodes and edges, manipulate the viewport, or query the current state of the flow.\n *\n * @public\n * @example\n * ```jsx\n *import { useCallback, useState } from 'react';\n *import { useReactFlow } from '@xyflow/react';\n *\n *export function NodeCounter() {\n *  const reactFlow = useReactFlow();\n *  const [count, setCount] = useState(0);\n *  const countNodes = useCallback(() => {\n *    setCount(reactFlow.getNodes().length);\n *    // you need to pass it as a dependency if you are using it with useEffect or useCallback\n *    // because at the first render, it's not initialized yet and some functions might not work.\n *  }, [reactFlow]);\n *\n *  return (\n *    <div>\n *      <button onClick={countNodes}>Update count</button>\n *      <p>There are {count} nodes in the flow.</p>\n *    </div>\n *  );\n *}\n *```\n */\nfunction useReactFlow() {\n    const viewportHelper = useViewportHelper();\n    const store = useStoreApi();\n    const batchContext = useBatchContext();\n    const viewportInitialized = useStore(selector$k);\n    const generalHelper = useMemo(() => {\n        const getInternalNode = (id) => store.getState().nodeLookup.get(id);\n        const setNodes = (payload) => {\n            batchContext.nodeQueue.push(payload);\n        };\n        const setEdges = (payload) => {\n            batchContext.edgeQueue.push(payload);\n        };\n        const getNodeRect = (node) => {\n            const { nodeLookup, nodeOrigin } = store.getState();\n            const nodeToUse = isNode(node) ? node : nodeLookup.get(node.id);\n            const position = nodeToUse.parentId\n                ? evaluateAbsolutePosition(nodeToUse.position, nodeToUse.measured, nodeToUse.parentId, nodeLookup, nodeOrigin)\n                : nodeToUse.position;\n            const nodeWithPosition = {\n                ...nodeToUse,\n                position,\n                width: nodeToUse.measured?.width ?? nodeToUse.width,\n                height: nodeToUse.measured?.height ?? nodeToUse.height,\n            };\n            return nodeToRect(nodeWithPosition);\n        };\n        const updateNode = (id, nodeUpdate, options = { replace: false }) => {\n            setNodes((prevNodes) => prevNodes.map((node) => {\n                if (node.id === id) {\n                    const nextNode = typeof nodeUpdate === 'function' ? nodeUpdate(node) : nodeUpdate;\n                    return options.replace && isNode(nextNode) ? nextNode : { ...node, ...nextNode };\n                }\n                return node;\n            }));\n        };\n        const updateEdge = (id, edgeUpdate, options = { replace: false }) => {\n            setEdges((prevEdges) => prevEdges.map((edge) => {\n                if (edge.id === id) {\n                    const nextEdge = typeof edgeUpdate === 'function' ? edgeUpdate(edge) : edgeUpdate;\n                    return options.replace && isEdge(nextEdge) ? nextEdge : { ...edge, ...nextEdge };\n                }\n                return edge;\n            }));\n        };\n        return {\n            getNodes: () => store.getState().nodes.map((n) => ({ ...n })),\n            getNode: (id) => getInternalNode(id)?.internals.userNode,\n            getInternalNode,\n            getEdges: () => {\n                const { edges = [] } = store.getState();\n                return edges.map((e) => ({ ...e }));\n            },\n            getEdge: (id) => store.getState().edgeLookup.get(id),\n            setNodes,\n            setEdges,\n            addNodes: (payload) => {\n                const newNodes = Array.isArray(payload) ? payload : [payload];\n                batchContext.nodeQueue.push((nodes) => [...nodes, ...newNodes]);\n            },\n            addEdges: (payload) => {\n                const newEdges = Array.isArray(payload) ? payload : [payload];\n                batchContext.edgeQueue.push((edges) => [...edges, ...newEdges]);\n            },\n            toObject: () => {\n                const { nodes = [], edges = [], transform } = store.getState();\n                const [x, y, zoom] = transform;\n                return {\n                    nodes: nodes.map((n) => ({ ...n })),\n                    edges: edges.map((e) => ({ ...e })),\n                    viewport: {\n                        x,\n                        y,\n                        zoom,\n                    },\n                };\n            },\n            deleteElements: async ({ nodes: nodesToRemove = [], edges: edgesToRemove = [] }) => {\n                const { nodes, edges, onNodesDelete, onEdgesDelete, triggerNodeChanges, triggerEdgeChanges, onDelete, onBeforeDelete, } = store.getState();\n                const { nodes: matchingNodes, edges: matchingEdges } = await getElementsToRemove({\n                    nodesToRemove,\n                    edgesToRemove,\n                    nodes,\n                    edges,\n                    onBeforeDelete,\n                });\n                const hasMatchingEdges = matchingEdges.length > 0;\n                const hasMatchingNodes = matchingNodes.length > 0;\n                if (hasMatchingEdges) {\n                    const edgeChanges = matchingEdges.map(elementToRemoveChange);\n                    onEdgesDelete?.(matchingEdges);\n                    triggerEdgeChanges(edgeChanges);\n                }\n                if (hasMatchingNodes) {\n                    const nodeChanges = matchingNodes.map(elementToRemoveChange);\n                    onNodesDelete?.(matchingNodes);\n                    triggerNodeChanges(nodeChanges);\n                }\n                if (hasMatchingNodes || hasMatchingEdges) {\n                    onDelete?.({ nodes: matchingNodes, edges: matchingEdges });\n                }\n                return { deletedNodes: matchingNodes, deletedEdges: matchingEdges };\n            },\n            getIntersectingNodes: (nodeOrRect, partially = true, nodes) => {\n                const isRect = isRectObject(nodeOrRect);\n                const nodeRect = isRect ? nodeOrRect : getNodeRect(nodeOrRect);\n                const hasNodesOption = nodes !== undefined;\n                if (!nodeRect) {\n                    return [];\n                }\n                return (nodes || store.getState().nodes).filter((n) => {\n                    const internalNode = store.getState().nodeLookup.get(n.id);\n                    if (internalNode && !isRect && (n.id === nodeOrRect.id || !internalNode.internals.positionAbsolute)) {\n                        return false;\n                    }\n                    const currNodeRect = nodeToRect(hasNodesOption ? n : internalNode);\n                    const overlappingArea = getOverlappingArea(currNodeRect, nodeRect);\n                    const partiallyVisible = partially && overlappingArea > 0;\n                    return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n                });\n            },\n            isNodeIntersecting: (nodeOrRect, area, partially = true) => {\n                const isRect = isRectObject(nodeOrRect);\n                const nodeRect = isRect ? nodeOrRect : getNodeRect(nodeOrRect);\n                if (!nodeRect) {\n                    return false;\n                }\n                const overlappingArea = getOverlappingArea(nodeRect, area);\n                const partiallyVisible = partially && overlappingArea > 0;\n                return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n            },\n            updateNode,\n            updateNodeData: (id, dataUpdate, options = { replace: false }) => {\n                updateNode(id, (node) => {\n                    const nextData = typeof dataUpdate === 'function' ? dataUpdate(node) : dataUpdate;\n                    return options.replace ? { ...node, data: nextData } : { ...node, data: { ...node.data, ...nextData } };\n                }, options);\n            },\n            updateEdge,\n            updateEdgeData: (id, dataUpdate, options = { replace: false }) => {\n                updateEdge(id, (edge) => {\n                    const nextData = typeof dataUpdate === 'function' ? dataUpdate(edge) : dataUpdate;\n                    return options.replace ? { ...edge, data: nextData } : { ...edge, data: { ...edge.data, ...nextData } };\n                }, options);\n            },\n            getNodesBounds: (nodes) => {\n                const { nodeLookup, nodeOrigin } = store.getState();\n                return getNodesBounds(nodes, { nodeLookup, nodeOrigin });\n            },\n            getHandleConnections: ({ type, id, nodeId }) => Array.from(store\n                .getState()\n                .connectionLookup.get(`${nodeId}-${type}${id ? `-${id}` : ''}`)\n                ?.values() ?? []),\n            getNodeConnections: ({ type, handleId, nodeId }) => Array.from(store\n                .getState()\n                .connectionLookup.get(`${nodeId}${type ? (handleId ? `-${type}-${handleId}` : `-${type}`) : ''}`)\n                ?.values() ?? []),\n            fitView: async (options) => {\n                // We either create a new Promise or reuse the existing one\n                // Even if fitView is called multiple times in a row, we only end up with a single Promise\n                const fitViewResolver = store.getState().fitViewResolver ?? withResolvers();\n                // We schedule a fitView by setting fitViewQueued and triggering a setNodes\n                store.setState({ fitViewQueued: true, fitViewOptions: options, fitViewResolver });\n                batchContext.nodeQueue.push((nodes) => [...nodes]);\n                return fitViewResolver.promise;\n            },\n        };\n    }, []);\n    return useMemo(() => {\n        return {\n            ...generalHelper,\n            ...viewportHelper,\n            viewportInitialized,\n        };\n    }, [viewportInitialized]);\n}\n\nconst selected = (item) => item.selected;\nconst win$1 = typeof window !== 'undefined' ? window : undefined;\n/**\n * Hook for handling global key events.\n *\n * @internal\n */\nfunction useGlobalKeyHandler({ deleteKeyCode, multiSelectionKeyCode, }) {\n    const store = useStoreApi();\n    const { deleteElements } = useReactFlow();\n    const deleteKeyPressed = useKeyPress(deleteKeyCode, { actInsideInputWithModifier: false });\n    const multiSelectionKeyPressed = useKeyPress(multiSelectionKeyCode, { target: win$1 });\n    useEffect(() => {\n        if (deleteKeyPressed) {\n            const { edges, nodes } = store.getState();\n            deleteElements({ nodes: nodes.filter(selected), edges: edges.filter(selected) });\n            store.setState({ nodesSelectionActive: false });\n        }\n    }, [deleteKeyPressed]);\n    useEffect(() => {\n        store.setState({ multiSelectionActive: multiSelectionKeyPressed });\n    }, [multiSelectionKeyPressed]);\n}\n\n/**\n * Hook for handling resize events.\n *\n * @internal\n */\nfunction useResizeHandler(domNode) {\n    const store = useStoreApi();\n    useEffect(() => {\n        const updateDimensions = () => {\n            if (!domNode.current) {\n                return false;\n            }\n            const size = getDimensions(domNode.current);\n            if (size.height === 0 || size.width === 0) {\n                store.getState().onError?.('004', errorMessages['error004']());\n            }\n            store.setState({ width: size.width || 500, height: size.height || 500 });\n        };\n        if (domNode.current) {\n            updateDimensions();\n            window.addEventListener('resize', updateDimensions);\n            const resizeObserver = new ResizeObserver(() => updateDimensions());\n            resizeObserver.observe(domNode.current);\n            return () => {\n                window.removeEventListener('resize', updateDimensions);\n                if (resizeObserver && domNode.current) {\n                    resizeObserver.unobserve(domNode.current);\n                }\n            };\n        }\n    }, []);\n}\n\nconst containerStyle = {\n    position: 'absolute',\n    width: '100%',\n    height: '100%',\n    top: 0,\n    left: 0,\n};\n\nconst selector$j = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    lib: s.lib,\n});\nfunction ZoomPane({ onPaneContextMenu, zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, defaultViewport, translateExtent, minZoom, maxZoom, zoomActivationKeyCode, preventScrolling = true, children, noWheelClassName, noPanClassName, onViewportChange, isControlledViewport, paneClickDistance, }) {\n    const store = useStoreApi();\n    const zoomPane = useRef(null);\n    const { userSelectionActive, lib } = useStore(selector$j, shallow);\n    const zoomActivationKeyPressed = useKeyPress(zoomActivationKeyCode);\n    const panZoom = useRef();\n    useResizeHandler(zoomPane);\n    const onTransformChange = useCallback((transform) => {\n        onViewportChange?.({ x: transform[0], y: transform[1], zoom: transform[2] });\n        if (!isControlledViewport) {\n            store.setState({ transform });\n        }\n    }, [onViewportChange, isControlledViewport]);\n    useEffect(() => {\n        if (zoomPane.current) {\n            panZoom.current = XYPanZoom({\n                domNode: zoomPane.current,\n                minZoom,\n                maxZoom,\n                translateExtent,\n                viewport: defaultViewport,\n                paneClickDistance,\n                onDraggingChange: (paneDragging) => store.setState({ paneDragging }),\n                onPanZoomStart: (event, vp) => {\n                    const { onViewportChangeStart, onMoveStart } = store.getState();\n                    onMoveStart?.(event, vp);\n                    onViewportChangeStart?.(vp);\n                },\n                onPanZoom: (event, vp) => {\n                    const { onViewportChange, onMove } = store.getState();\n                    onMove?.(event, vp);\n                    onViewportChange?.(vp);\n                },\n                onPanZoomEnd: (event, vp) => {\n                    const { onViewportChangeEnd, onMoveEnd } = store.getState();\n                    onMoveEnd?.(event, vp);\n                    onViewportChangeEnd?.(vp);\n                },\n            });\n            const { x, y, zoom } = panZoom.current.getViewport();\n            store.setState({\n                panZoom: panZoom.current,\n                transform: [x, y, zoom],\n                domNode: zoomPane.current.closest('.react-flow'),\n            });\n            return () => {\n                panZoom.current?.destroy();\n            };\n        }\n    }, []);\n    useEffect(() => {\n        panZoom.current?.update({\n            onPaneContextMenu,\n            zoomOnScroll,\n            zoomOnPinch,\n            panOnScroll,\n            panOnScrollSpeed,\n            panOnScrollMode,\n            zoomOnDoubleClick,\n            panOnDrag,\n            zoomActivationKeyPressed,\n            preventScrolling,\n            noPanClassName,\n            userSelectionActive,\n            noWheelClassName,\n            lib,\n            onTransformChange,\n        });\n    }, [\n        onPaneContextMenu,\n        zoomOnScroll,\n        zoomOnPinch,\n        panOnScroll,\n        panOnScrollSpeed,\n        panOnScrollMode,\n        zoomOnDoubleClick,\n        panOnDrag,\n        zoomActivationKeyPressed,\n        preventScrolling,\n        noPanClassName,\n        userSelectionActive,\n        noWheelClassName,\n        lib,\n        onTransformChange,\n    ]);\n    return (jsx(\"div\", { className: \"react-flow__renderer\", ref: zoomPane, style: containerStyle, children: children }));\n}\n\nconst selector$i = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    userSelectionRect: s.userSelectionRect,\n});\nfunction UserSelection() {\n    const { userSelectionActive, userSelectionRect } = useStore(selector$i, shallow);\n    const isActive = userSelectionActive && userSelectionRect;\n    if (!isActive) {\n        return null;\n    }\n    return (jsx(\"div\", { className: \"react-flow__selection react-flow__container\", style: {\n            width: userSelectionRect.width,\n            height: userSelectionRect.height,\n            transform: `translate(${userSelectionRect.x}px, ${userSelectionRect.y}px)`,\n        } }));\n}\n\nconst wrapHandler = (handler, containerRef) => {\n    return (event) => {\n        if (event.target !== containerRef.current) {\n            return;\n        }\n        handler?.(event);\n    };\n};\nconst selector$h = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    elementsSelectable: s.elementsSelectable,\n    connectionInProgress: s.connection.inProgress,\n    dragging: s.paneDragging,\n});\nfunction Pane({ isSelecting, selectionKeyPressed, selectionMode = SelectionMode.Full, panOnDrag, selectionOnDrag, onSelectionStart, onSelectionEnd, onPaneClick, onPaneContextMenu, onPaneScroll, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, children, }) {\n    const store = useStoreApi();\n    const { userSelectionActive, elementsSelectable, dragging, connectionInProgress } = useStore(selector$h, shallow);\n    const hasActiveSelection = elementsSelectable && (isSelecting || userSelectionActive);\n    const container = useRef(null);\n    const containerBounds = useRef();\n    const selectedNodeIds = useRef(new Set());\n    const selectedEdgeIds = useRef(new Set());\n    // Used to prevent click events when the user lets go of the selectionKey during a selection\n    const selectionInProgress = useRef(false);\n    const selectionStarted = useRef(false);\n    const onClick = (event) => {\n        // We prevent click events when the user let go of the selectionKey during a selection\n        // We also prevent click events when a connection is in progress\n        if (selectionInProgress.current || connectionInProgress) {\n            selectionInProgress.current = false;\n            return;\n        }\n        onPaneClick?.(event);\n        store.getState().resetSelectedElements();\n        store.setState({ nodesSelectionActive: false });\n    };\n    const onContextMenu = (event) => {\n        if (Array.isArray(panOnDrag) && panOnDrag?.includes(2)) {\n            event.preventDefault();\n            return;\n        }\n        onPaneContextMenu?.(event);\n    };\n    const onWheel = onPaneScroll ? (event) => onPaneScroll(event) : undefined;\n    const onPointerDown = (event) => {\n        const { resetSelectedElements, domNode } = store.getState();\n        containerBounds.current = domNode?.getBoundingClientRect();\n        if (!elementsSelectable ||\n            !isSelecting ||\n            event.button !== 0 ||\n            event.target !== container.current ||\n            !containerBounds.current) {\n            return;\n        }\n        event.target?.setPointerCapture?.(event.pointerId);\n        selectionStarted.current = true;\n        selectionInProgress.current = false;\n        const { x, y } = getEventPosition(event.nativeEvent, containerBounds.current);\n        resetSelectedElements();\n        store.setState({\n            userSelectionRect: {\n                width: 0,\n                height: 0,\n                startX: x,\n                startY: y,\n                x,\n                y,\n            },\n        });\n        onSelectionStart?.(event);\n    };\n    const onPointerMove = (event) => {\n        const { userSelectionRect, transform, nodeLookup, edgeLookup, connectionLookup, triggerNodeChanges, triggerEdgeChanges, defaultEdgeOptions, } = store.getState();\n        if (!containerBounds.current || !userSelectionRect) {\n            return;\n        }\n        selectionInProgress.current = true;\n        const { x: mouseX, y: mouseY } = getEventPosition(event.nativeEvent, containerBounds.current);\n        const { startX, startY } = userSelectionRect;\n        const nextUserSelectRect = {\n            startX,\n            startY,\n            x: mouseX < startX ? mouseX : startX,\n            y: mouseY < startY ? mouseY : startY,\n            width: Math.abs(mouseX - startX),\n            height: Math.abs(mouseY - startY),\n        };\n        const prevSelectedNodeIds = selectedNodeIds.current;\n        const prevSelectedEdgeIds = selectedEdgeIds.current;\n        selectedNodeIds.current = new Set(getNodesInside(nodeLookup, nextUserSelectRect, transform, selectionMode === SelectionMode.Partial, true).map((node) => node.id));\n        selectedEdgeIds.current = new Set();\n        const edgesSelectable = defaultEdgeOptions?.selectable ?? true;\n        // We look for all edges connected to the selected nodes\n        for (const nodeId of selectedNodeIds.current) {\n            const connections = connectionLookup.get(nodeId);\n            if (!connections)\n                continue;\n            for (const { edgeId } of connections.values()) {\n                const edge = edgeLookup.get(edgeId);\n                if (edge && (edge.selectable ?? edgesSelectable)) {\n                    selectedEdgeIds.current.add(edgeId);\n                }\n            }\n        }\n        if (!areSetsEqual(prevSelectedNodeIds, selectedNodeIds.current)) {\n            const changes = getSelectionChanges(nodeLookup, selectedNodeIds.current, true);\n            triggerNodeChanges(changes);\n        }\n        if (!areSetsEqual(prevSelectedEdgeIds, selectedEdgeIds.current)) {\n            const changes = getSelectionChanges(edgeLookup, selectedEdgeIds.current);\n            triggerEdgeChanges(changes);\n        }\n        store.setState({\n            userSelectionRect: nextUserSelectRect,\n            userSelectionActive: true,\n            nodesSelectionActive: false,\n        });\n    };\n    const onPointerUp = (event) => {\n        if (event.button !== 0 || !selectionStarted.current) {\n            return;\n        }\n        event.target?.releasePointerCapture?.(event.pointerId);\n        const { userSelectionRect } = store.getState();\n        /*\n         * We only want to trigger click functions when in selection mode if\n         * the user did not move the mouse.\n         */\n        if (!userSelectionActive && userSelectionRect && event.target === container.current) {\n            onClick?.(event);\n        }\n        store.setState({\n            userSelectionActive: false,\n            userSelectionRect: null,\n            nodesSelectionActive: selectedNodeIds.current.size > 0,\n        });\n        onSelectionEnd?.(event);\n        /*\n         * If the user kept holding the selectionKey during the selection,\n         * we need to reset the selectionInProgress, so the next click event is not prevented\n         */\n        if (selectionKeyPressed || selectionOnDrag) {\n            selectionInProgress.current = false;\n        }\n        selectionStarted.current = false;\n    };\n    const draggable = panOnDrag === true || (Array.isArray(panOnDrag) && panOnDrag.includes(0));\n    return (jsxs(\"div\", { className: cc(['react-flow__pane', { draggable, dragging, selection: isSelecting }]), onClick: hasActiveSelection ? undefined : wrapHandler(onClick, container), onContextMenu: wrapHandler(onContextMenu, container), onWheel: wrapHandler(onWheel, container), onPointerEnter: hasActiveSelection ? undefined : onPaneMouseEnter, onPointerDown: hasActiveSelection ? onPointerDown : onPaneMouseMove, onPointerMove: hasActiveSelection ? onPointerMove : onPaneMouseMove, onPointerUp: hasActiveSelection ? onPointerUp : undefined, onPointerLeave: onPaneMouseLeave, ref: container, style: containerStyle, children: [children, jsx(UserSelection, {})] }));\n}\n\n/*\n * this handler is called by\n * 1. the click handler when node is not draggable or selectNodesOnDrag = false\n * or\n * 2. the on drag start handler when node is draggable and selectNodesOnDrag = true\n */\nfunction handleNodeClick({ id, store, unselect = false, nodeRef, }) {\n    const { addSelectedNodes, unselectNodesAndEdges, multiSelectionActive, nodeLookup, onError } = store.getState();\n    const node = nodeLookup.get(id);\n    if (!node) {\n        onError?.('012', errorMessages['error012'](id));\n        return;\n    }\n    store.setState({ nodesSelectionActive: false });\n    if (!node.selected) {\n        addSelectedNodes([id]);\n    }\n    else if (unselect || (node.selected && multiSelectionActive)) {\n        unselectNodesAndEdges({ nodes: [node], edges: [] });\n        requestAnimationFrame(() => nodeRef?.current?.blur());\n    }\n}\n\n/**\n * Hook for calling XYDrag helper from @xyflow/system.\n *\n * @internal\n */\nfunction useDrag({ nodeRef, disabled = false, noDragClassName, handleSelector, nodeId, isSelectable, nodeClickDistance, }) {\n    const store = useStoreApi();\n    const [dragging, setDragging] = useState(false);\n    const xyDrag = useRef();\n    useEffect(() => {\n        xyDrag.current = XYDrag({\n            getStoreItems: () => store.getState(),\n            onNodeMouseDown: (id) => {\n                handleNodeClick({\n                    id,\n                    store,\n                    nodeRef,\n                });\n            },\n            onDragStart: () => {\n                setDragging(true);\n            },\n            onDragStop: () => {\n                setDragging(false);\n            },\n        });\n    }, []);\n    useEffect(() => {\n        if (disabled) {\n            xyDrag.current?.destroy();\n        }\n        else if (nodeRef.current) {\n            xyDrag.current?.update({\n                noDragClassName,\n                handleSelector,\n                domNode: nodeRef.current,\n                isSelectable,\n                nodeId,\n                nodeClickDistance,\n            });\n            return () => {\n                xyDrag.current?.destroy();\n            };\n        }\n    }, [noDragClassName, handleSelector, disabled, isSelectable, nodeRef, nodeId]);\n    return dragging;\n}\n\nconst selectedAndDraggable = (nodesDraggable) => (n) => n.selected && (n.draggable || (nodesDraggable && typeof n.draggable === 'undefined'));\n/**\n * Hook for updating node positions by passing a direction and factor\n *\n * @internal\n * @returns function for updating node positions\n */\nfunction useMoveSelectedNodes() {\n    const store = useStoreApi();\n    const moveSelectedNodes = useCallback((params) => {\n        const { nodeExtent, snapToGrid, snapGrid, nodesDraggable, onError, updateNodePositions, nodeLookup, nodeOrigin } = store.getState();\n        const nodeUpdates = new Map();\n        const isSelected = selectedAndDraggable(nodesDraggable);\n        /*\n         * by default a node moves 5px on each key press\n         * if snap grid is enabled, we use that for the velocity\n         */\n        const xVelo = snapToGrid ? snapGrid[0] : 5;\n        const yVelo = snapToGrid ? snapGrid[1] : 5;\n        const xDiff = params.direction.x * xVelo * params.factor;\n        const yDiff = params.direction.y * yVelo * params.factor;\n        for (const [, node] of nodeLookup) {\n            if (!isSelected(node)) {\n                continue;\n            }\n            let nextPosition = {\n                x: node.internals.positionAbsolute.x + xDiff,\n                y: node.internals.positionAbsolute.y + yDiff,\n            };\n            if (snapToGrid) {\n                nextPosition = snapPosition(nextPosition, snapGrid);\n            }\n            const { position, positionAbsolute } = calculateNodePosition({\n                nodeId: node.id,\n                nextPosition,\n                nodeLookup,\n                nodeExtent,\n                nodeOrigin,\n                onError,\n            });\n            node.position = position;\n            node.internals.positionAbsolute = positionAbsolute;\n            nodeUpdates.set(node.id, node);\n        }\n        updateNodePositions(nodeUpdates);\n    }, []);\n    return moveSelectedNodes;\n}\n\nconst NodeIdContext = createContext(null);\nconst Provider = NodeIdContext.Provider;\nNodeIdContext.Consumer;\n/**\n * You can use this hook to get the id of the node it is used inside. It is useful\n * if you need the node's id deeper in the render tree but don't want to manually\n * drill down the id as a prop.\n *\n * @public\n * @returns The id for a node in the flow.\n *\n * @example\n *```jsx\n *import { useNodeId } from '@xyflow/react';\n *\n *export default function CustomNode() {\n *  return (\n *    <div>\n *      <span>This node has an id of </span>\n *      <NodeIdDisplay />\n *    </div>\n *  );\n *}\n *\n *function NodeIdDisplay() {\n *  const nodeId = useNodeId();\n *\n *  return <span>{nodeId}</span>;\n *}\n *```\n */\nconst useNodeId = () => {\n    const nodeId = useContext(NodeIdContext);\n    return nodeId;\n};\n\nconst selector$g = (s) => ({\n    connectOnClick: s.connectOnClick,\n    noPanClassName: s.noPanClassName,\n    rfId: s.rfId,\n});\nconst connectingSelector = (nodeId, handleId, type) => (state) => {\n    const { connectionClickStartHandle: clickHandle, connectionMode, connection } = state;\n    const { fromHandle, toHandle, isValid } = connection;\n    const connectingTo = toHandle?.nodeId === nodeId && toHandle?.id === handleId && toHandle?.type === type;\n    return {\n        connectingFrom: fromHandle?.nodeId === nodeId && fromHandle?.id === handleId && fromHandle?.type === type,\n        connectingTo,\n        clickConnecting: clickHandle?.nodeId === nodeId && clickHandle?.id === handleId && clickHandle?.type === type,\n        isPossibleEndHandle: connectionMode === ConnectionMode.Strict\n            ? fromHandle?.type !== type\n            : nodeId !== fromHandle?.nodeId || handleId !== fromHandle?.id,\n        connectionInProcess: !!fromHandle,\n        clickConnectionInProcess: !!clickHandle,\n        valid: connectingTo && isValid,\n    };\n};\nfunction HandleComponent({ type = 'source', position = Position.Top, isValidConnection, isConnectable = true, isConnectableStart = true, isConnectableEnd = true, id, onConnect, children, className, onMouseDown, onTouchStart, ...rest }, ref) {\n    const handleId = id || null;\n    const isTarget = type === 'target';\n    const store = useStoreApi();\n    const nodeId = useNodeId();\n    const { connectOnClick, noPanClassName, rfId } = useStore(selector$g, shallow);\n    const { connectingFrom, connectingTo, clickConnecting, isPossibleEndHandle, connectionInProcess, clickConnectionInProcess, valid, } = useStore(connectingSelector(nodeId, handleId, type), shallow);\n    if (!nodeId) {\n        store.getState().onError?.('010', errorMessages['error010']());\n    }\n    const onConnectExtended = (params) => {\n        const { defaultEdgeOptions, onConnect: onConnectAction, hasDefaultEdges } = store.getState();\n        const edgeParams = {\n            ...defaultEdgeOptions,\n            ...params,\n        };\n        if (hasDefaultEdges) {\n            const { edges, setEdges } = store.getState();\n            setEdges(addEdge(edgeParams, edges));\n        }\n        onConnectAction?.(edgeParams);\n        onConnect?.(edgeParams);\n    };\n    const onPointerDown = (event) => {\n        if (!nodeId) {\n            return;\n        }\n        const isMouseTriggered = isMouseEvent(event.nativeEvent);\n        if (isConnectableStart &&\n            ((isMouseTriggered && event.button === 0) || !isMouseTriggered)) {\n            const currentStore = store.getState();\n            XYHandle.onPointerDown(event.nativeEvent, {\n                autoPanOnConnect: currentStore.autoPanOnConnect,\n                connectionMode: currentStore.connectionMode,\n                connectionRadius: currentStore.connectionRadius,\n                domNode: currentStore.domNode,\n                nodeLookup: currentStore.nodeLookup,\n                lib: currentStore.lib,\n                isTarget,\n                handleId,\n                nodeId,\n                flowId: currentStore.rfId,\n                panBy: currentStore.panBy,\n                cancelConnection: currentStore.cancelConnection,\n                onConnectStart: currentStore.onConnectStart,\n                onConnectEnd: currentStore.onConnectEnd,\n                updateConnection: currentStore.updateConnection,\n                onConnect: onConnectExtended,\n                isValidConnection: isValidConnection || currentStore.isValidConnection,\n                getTransform: () => store.getState().transform,\n                getFromHandle: () => store.getState().connection.fromHandle,\n                autoPanSpeed: currentStore.autoPanSpeed,\n            });\n        }\n        if (isMouseTriggered) {\n            onMouseDown?.(event);\n        }\n        else {\n            onTouchStart?.(event);\n        }\n    };\n    const onClick = (event) => {\n        const { onClickConnectStart, onClickConnectEnd, connectionClickStartHandle, connectionMode, isValidConnection: isValidConnectionStore, lib, rfId: flowId, nodeLookup, connection: connectionState, } = store.getState();\n        if (!nodeId || (!connectionClickStartHandle && !isConnectableStart)) {\n            return;\n        }\n        if (!connectionClickStartHandle) {\n            onClickConnectStart?.(event.nativeEvent, { nodeId, handleId, handleType: type });\n            store.setState({ connectionClickStartHandle: { nodeId, type, id: handleId } });\n            return;\n        }\n        const doc = getHostForElement(event.target);\n        const isValidConnectionHandler = isValidConnection || isValidConnectionStore;\n        const { connection, isValid } = XYHandle.isValid(event.nativeEvent, {\n            handle: {\n                nodeId,\n                id: handleId,\n                type,\n            },\n            connectionMode,\n            fromNodeId: connectionClickStartHandle.nodeId,\n            fromHandleId: connectionClickStartHandle.id || null,\n            fromType: connectionClickStartHandle.type,\n            isValidConnection: isValidConnectionHandler,\n            flowId,\n            doc,\n            lib,\n            nodeLookup,\n        });\n        if (isValid && connection) {\n            onConnectExtended(connection);\n        }\n        const connectionClone = structuredClone(connectionState);\n        delete connectionClone.inProgress;\n        connectionClone.toPosition = connectionClone.toHandle ? connectionClone.toHandle.position : null;\n        onClickConnectEnd?.(event, connectionClone);\n        store.setState({ connectionClickStartHandle: null });\n    };\n    return (jsx(\"div\", { \"data-handleid\": handleId, \"data-nodeid\": nodeId, \"data-handlepos\": position, \"data-id\": `${rfId}-${nodeId}-${handleId}-${type}`, className: cc([\n            'react-flow__handle',\n            `react-flow__handle-${position}`,\n            'nodrag',\n            noPanClassName,\n            className,\n            {\n                source: !isTarget,\n                target: isTarget,\n                connectable: isConnectable,\n                connectablestart: isConnectableStart,\n                connectableend: isConnectableEnd,\n                clickconnecting: clickConnecting,\n                connectingfrom: connectingFrom,\n                connectingto: connectingTo,\n                valid,\n                /*\n                 * shows where you can start a connection from\n                 * and where you can end it while connecting\n                 */\n                connectionindicator: isConnectable &&\n                    (!connectionInProcess || isPossibleEndHandle) &&\n                    (connectionInProcess || clickConnectionInProcess ? isConnectableEnd : isConnectableStart),\n            },\n        ]), onMouseDown: onPointerDown, onTouchStart: onPointerDown, onClick: connectOnClick ? onClick : undefined, ref: ref, ...rest, children: children }));\n}\n/**\n * The `<Handle />` component is used in your [custom nodes](/learn/customization/custom-nodes)\n * to define connection points.\n *\n *@public\n *\n *@example\n *\n *```jsx\n *import { Handle, Position } from '@xyflow/react';\n *\n *export function CustomNode({ data }) {\n *  return (\n *    <>\n *      <div style={{ padding: '10px 20px' }}>\n *        {data.label}\n *      </div>\n *\n *      <Handle type=\"target\" position={Position.Left} />\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *```\n */\nconst Handle = memo(fixedForwardRef(HandleComponent));\n\nfunction InputNode({ data, isConnectable, sourcePosition = Position.Bottom }) {\n    return (jsxs(Fragment, { children: [data?.label, jsx(Handle, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })] }));\n}\n\nfunction DefaultNode({ data, isConnectable, targetPosition = Position.Top, sourcePosition = Position.Bottom, }) {\n    return (jsxs(Fragment, { children: [jsx(Handle, { type: \"target\", position: targetPosition, isConnectable: isConnectable }), data?.label, jsx(Handle, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })] }));\n}\n\nfunction GroupNode() {\n    return null;\n}\n\nfunction OutputNode({ data, isConnectable, targetPosition = Position.Top }) {\n    return (jsxs(Fragment, { children: [jsx(Handle, { type: \"target\", position: targetPosition, isConnectable: isConnectable }), data?.label] }));\n}\n\nconst arrowKeyDiffs = {\n    ArrowUp: { x: 0, y: -1 },\n    ArrowDown: { x: 0, y: 1 },\n    ArrowLeft: { x: -1, y: 0 },\n    ArrowRight: { x: 1, y: 0 },\n};\nconst builtinNodeTypes = {\n    input: InputNode,\n    default: DefaultNode,\n    output: OutputNode,\n    group: GroupNode,\n};\nfunction getNodeInlineStyleDimensions(node) {\n    if (node.internals.handleBounds === undefined) {\n        return {\n            width: node.width ?? node.initialWidth ?? node.style?.width,\n            height: node.height ?? node.initialHeight ?? node.style?.height,\n        };\n    }\n    return {\n        width: node.width ?? node.style?.width,\n        height: node.height ?? node.style?.height,\n    };\n}\n\nconst selector$f = (s) => {\n    const { width, height, x, y } = getInternalNodesBounds(s.nodeLookup, {\n        filter: (node) => !!node.selected,\n    });\n    return {\n        width: isNumeric(width) ? width : null,\n        height: isNumeric(height) ? height : null,\n        userSelectionActive: s.userSelectionActive,\n        transformString: `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]}) translate(${x}px,${y}px)`,\n    };\n};\nfunction NodesSelection({ onSelectionContextMenu, noPanClassName, disableKeyboardA11y, }) {\n    const store = useStoreApi();\n    const { width, height, transformString, userSelectionActive } = useStore(selector$f, shallow);\n    const moveSelectedNodes = useMoveSelectedNodes();\n    const nodeRef = useRef(null);\n    useEffect(() => {\n        if (!disableKeyboardA11y) {\n            nodeRef.current?.focus({\n                preventScroll: true,\n            });\n        }\n    }, [disableKeyboardA11y]);\n    useDrag({\n        nodeRef,\n    });\n    if (userSelectionActive || !width || !height) {\n        return null;\n    }\n    const onContextMenu = onSelectionContextMenu\n        ? (event) => {\n            const selectedNodes = store.getState().nodes.filter((n) => n.selected);\n            onSelectionContextMenu(event, selectedNodes);\n        }\n        : undefined;\n    const onKeyDown = (event) => {\n        if (Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n            event.preventDefault();\n            moveSelectedNodes({\n                direction: arrowKeyDiffs[event.key],\n                factor: event.shiftKey ? 4 : 1,\n            });\n        }\n    };\n    return (jsx(\"div\", { className: cc(['react-flow__nodesselection', 'react-flow__container', noPanClassName]), style: {\n            transform: transformString,\n        }, children: jsx(\"div\", { ref: nodeRef, className: \"react-flow__nodesselection-rect\", onContextMenu: onContextMenu, tabIndex: disableKeyboardA11y ? undefined : -1, onKeyDown: disableKeyboardA11y ? undefined : onKeyDown, style: {\n                width,\n                height,\n            } }) }));\n}\n\nconst win = typeof window !== 'undefined' ? window : undefined;\nconst selector$e = (s) => {\n    return { nodesSelectionActive: s.nodesSelectionActive, userSelectionActive: s.userSelectionActive };\n};\nfunction FlowRendererComponent({ children, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneContextMenu, onPaneScroll, paneClickDistance, deleteKeyCode, selectionKeyCode, selectionOnDrag, selectionMode, onSelectionStart, onSelectionEnd, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, elementsSelectable, zoomOnScroll, zoomOnPinch, panOnScroll: _panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag: _panOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, onSelectionContextMenu, noWheelClassName, noPanClassName, disableKeyboardA11y, onViewportChange, isControlledViewport, }) {\n    const { nodesSelectionActive, userSelectionActive } = useStore(selector$e);\n    const selectionKeyPressed = useKeyPress(selectionKeyCode, { target: win });\n    const panActivationKeyPressed = useKeyPress(panActivationKeyCode, { target: win });\n    const panOnDrag = panActivationKeyPressed || _panOnDrag;\n    const panOnScroll = panActivationKeyPressed || _panOnScroll;\n    const _selectionOnDrag = selectionOnDrag && panOnDrag !== true;\n    const isSelecting = selectionKeyPressed || userSelectionActive || _selectionOnDrag;\n    useGlobalKeyHandler({ deleteKeyCode, multiSelectionKeyCode });\n    return (jsx(ZoomPane, { onPaneContextMenu: onPaneContextMenu, elementsSelectable: elementsSelectable, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, zoomOnDoubleClick: zoomOnDoubleClick, panOnDrag: !selectionKeyPressed && panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, zoomActivationKeyCode: zoomActivationKeyCode, preventScrolling: preventScrolling, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, onViewportChange: onViewportChange, isControlledViewport: isControlledViewport, paneClickDistance: paneClickDistance, children: jsxs(Pane, { onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, panOnDrag: panOnDrag, isSelecting: !!isSelecting, selectionMode: selectionMode, selectionKeyPressed: selectionKeyPressed, selectionOnDrag: _selectionOnDrag, children: [children, nodesSelectionActive && (jsx(NodesSelection, { onSelectionContextMenu: onSelectionContextMenu, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y }))] }) }));\n}\nFlowRendererComponent.displayName = 'FlowRenderer';\nconst FlowRenderer = memo(FlowRendererComponent);\n\nconst selector$d = (onlyRenderVisible) => (s) => {\n    return onlyRenderVisible\n        ? getNodesInside(s.nodeLookup, { x: 0, y: 0, width: s.width, height: s.height }, s.transform, true).map((node) => node.id)\n        : Array.from(s.nodeLookup.keys());\n};\n/**\n * Hook for getting the visible node ids from the store.\n *\n * @internal\n * @param onlyRenderVisible\n * @returns array with visible node ids\n */\nfunction useVisibleNodeIds(onlyRenderVisible) {\n    const nodeIds = useStore(useCallback(selector$d(onlyRenderVisible), [onlyRenderVisible]), shallow);\n    return nodeIds;\n}\n\nconst selector$c = (s) => s.updateNodeInternals;\nfunction useResizeObserver() {\n    const updateNodeInternals = useStore(selector$c);\n    const [resizeObserver] = useState(() => {\n        if (typeof ResizeObserver === 'undefined') {\n            return null;\n        }\n        return new ResizeObserver((entries) => {\n            const updates = new Map();\n            entries.forEach((entry) => {\n                const id = entry.target.getAttribute('data-id');\n                updates.set(id, {\n                    id,\n                    nodeElement: entry.target,\n                    force: true,\n                });\n            });\n            updateNodeInternals(updates);\n        });\n    });\n    useEffect(() => {\n        return () => {\n            resizeObserver?.disconnect();\n        };\n    }, [resizeObserver]);\n    return resizeObserver;\n}\n\n/**\n * Hook to handle the resize observation + internal updates for the passed node.\n *\n * @internal\n * @returns nodeRef - reference to the node element\n */\nfunction useNodeObserver({ node, nodeType, hasDimensions, resizeObserver, }) {\n    const store = useStoreApi();\n    const nodeRef = useRef(null);\n    const observedNode = useRef(null);\n    const prevSourcePosition = useRef(node.sourcePosition);\n    const prevTargetPosition = useRef(node.targetPosition);\n    const prevType = useRef(nodeType);\n    const isInitialized = hasDimensions && !!node.internals.handleBounds;\n    useEffect(() => {\n        if (nodeRef.current && !node.hidden && (!isInitialized || observedNode.current !== nodeRef.current)) {\n            if (observedNode.current) {\n                resizeObserver?.unobserve(observedNode.current);\n            }\n            resizeObserver?.observe(nodeRef.current);\n            observedNode.current = nodeRef.current;\n        }\n    }, [isInitialized, node.hidden]);\n    useEffect(() => {\n        return () => {\n            if (observedNode.current) {\n                resizeObserver?.unobserve(observedNode.current);\n                observedNode.current = null;\n            }\n        };\n    }, []);\n    useEffect(() => {\n        if (nodeRef.current) {\n            /*\n             * when the user programmatically changes the source or handle position, we need to update the internals\n             * to make sure the edges are updated correctly\n             */\n            const typeChanged = prevType.current !== nodeType;\n            const sourcePosChanged = prevSourcePosition.current !== node.sourcePosition;\n            const targetPosChanged = prevTargetPosition.current !== node.targetPosition;\n            if (typeChanged || sourcePosChanged || targetPosChanged) {\n                prevType.current = nodeType;\n                prevSourcePosition.current = node.sourcePosition;\n                prevTargetPosition.current = node.targetPosition;\n                store\n                    .getState()\n                    .updateNodeInternals(new Map([[node.id, { id: node.id, nodeElement: nodeRef.current, force: true }]]));\n            }\n        }\n    }, [node.id, nodeType, node.sourcePosition, node.targetPosition]);\n    return nodeRef;\n}\n\nfunction NodeWrapper({ id, onClick, onMouseEnter, onMouseMove, onMouseLeave, onContextMenu, onDoubleClick, nodesDraggable, elementsSelectable, nodesConnectable, nodesFocusable, resizeObserver, noDragClassName, noPanClassName, disableKeyboardA11y, rfId, nodeTypes, nodeClickDistance, onError, }) {\n    const { node, internals, isParent } = useStore((s) => {\n        const node = s.nodeLookup.get(id);\n        const isParent = s.parentLookup.has(id);\n        return {\n            node,\n            internals: node.internals,\n            isParent,\n        };\n    }, shallow);\n    let nodeType = node.type || 'default';\n    let NodeComponent = nodeTypes?.[nodeType] || builtinNodeTypes[nodeType];\n    if (NodeComponent === undefined) {\n        onError?.('003', errorMessages['error003'](nodeType));\n        nodeType = 'default';\n        NodeComponent = builtinNodeTypes.default;\n    }\n    const isDraggable = !!(node.draggable || (nodesDraggable && typeof node.draggable === 'undefined'));\n    const isSelectable = !!(node.selectable || (elementsSelectable && typeof node.selectable === 'undefined'));\n    const isConnectable = !!(node.connectable || (nodesConnectable && typeof node.connectable === 'undefined'));\n    const isFocusable = !!(node.focusable || (nodesFocusable && typeof node.focusable === 'undefined'));\n    const store = useStoreApi();\n    const hasDimensions = nodeHasDimensions(node);\n    const nodeRef = useNodeObserver({ node, nodeType, hasDimensions, resizeObserver });\n    const dragging = useDrag({\n        nodeRef,\n        disabled: node.hidden || !isDraggable,\n        noDragClassName,\n        handleSelector: node.dragHandle,\n        nodeId: id,\n        isSelectable,\n        nodeClickDistance,\n    });\n    const moveSelectedNodes = useMoveSelectedNodes();\n    if (node.hidden) {\n        return null;\n    }\n    const nodeDimensions = getNodeDimensions(node);\n    const inlineDimensions = getNodeInlineStyleDimensions(node);\n    const hasPointerEvents = isSelectable || isDraggable || onClick || onMouseEnter || onMouseMove || onMouseLeave;\n    const onMouseEnterHandler = onMouseEnter\n        ? (event) => onMouseEnter(event, { ...internals.userNode })\n        : undefined;\n    const onMouseMoveHandler = onMouseMove\n        ? (event) => onMouseMove(event, { ...internals.userNode })\n        : undefined;\n    const onMouseLeaveHandler = onMouseLeave\n        ? (event) => onMouseLeave(event, { ...internals.userNode })\n        : undefined;\n    const onContextMenuHandler = onContextMenu\n        ? (event) => onContextMenu(event, { ...internals.userNode })\n        : undefined;\n    const onDoubleClickHandler = onDoubleClick\n        ? (event) => onDoubleClick(event, { ...internals.userNode })\n        : undefined;\n    const onSelectNodeHandler = (event) => {\n        const { selectNodesOnDrag, nodeDragThreshold } = store.getState();\n        if (isSelectable && (!selectNodesOnDrag || !isDraggable || nodeDragThreshold > 0)) {\n            /*\n             * this handler gets called by XYDrag on drag start when selectNodesOnDrag=true\n             * here we only need to call it when selectNodesOnDrag=false\n             */\n            handleNodeClick({\n                id,\n                store,\n                nodeRef,\n            });\n        }\n        if (onClick) {\n            onClick(event, { ...internals.userNode });\n        }\n    };\n    const onKeyDown = (event) => {\n        if (isInputDOMNode(event.nativeEvent) || disableKeyboardA11y) {\n            return;\n        }\n        if (elementSelectionKeys.includes(event.key) && isSelectable) {\n            const unselect = event.key === 'Escape';\n            handleNodeClick({\n                id,\n                store,\n                unselect,\n                nodeRef,\n            });\n        }\n        else if (isDraggable && node.selected && Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n            // prevent default scrolling behavior on arrow key press when node is moved\n            event.preventDefault();\n            const { ariaLabelConfig } = store.getState();\n            store.setState({\n                ariaLiveMessage: ariaLabelConfig['node.a11yDescription.ariaLiveMessage']({\n                    direction: event.key.replace('Arrow', '').toLowerCase(),\n                    x: ~~internals.positionAbsolute.x,\n                    y: ~~internals.positionAbsolute.y,\n                }),\n            });\n            moveSelectedNodes({\n                direction: arrowKeyDiffs[event.key],\n                factor: event.shiftKey ? 4 : 1,\n            });\n        }\n    };\n    const onFocus = () => {\n        if (disableKeyboardA11y || !nodeRef.current?.matches(':focus-visible')) {\n            return;\n        }\n        const { transform, width, height, autoPanOnNodeFocus, setCenter } = store.getState();\n        if (!autoPanOnNodeFocus) {\n            return;\n        }\n        const withinViewport = getNodesInside(new Map([[id, node]]), { x: 0, y: 0, width, height }, transform, true).length > 0;\n        if (!withinViewport) {\n            setCenter(node.position.x + nodeDimensions.width / 2, node.position.y + nodeDimensions.height / 2, {\n                zoom: transform[2],\n            });\n        }\n    };\n    return (jsx(\"div\", { className: cc([\n            'react-flow__node',\n            `react-flow__node-${nodeType}`,\n            {\n                // this is overwritable by passing `nopan` as a class name\n                [noPanClassName]: isDraggable,\n            },\n            node.className,\n            {\n                selected: node.selected,\n                selectable: isSelectable,\n                parent: isParent,\n                draggable: isDraggable,\n                dragging,\n            },\n        ]), ref: nodeRef, style: {\n            zIndex: internals.z,\n            transform: `translate(${internals.positionAbsolute.x}px,${internals.positionAbsolute.y}px)`,\n            pointerEvents: hasPointerEvents ? 'all' : 'none',\n            visibility: hasDimensions ? 'visible' : 'hidden',\n            ...node.style,\n            ...inlineDimensions,\n        }, \"data-id\": id, \"data-testid\": `rf__node-${id}`, onMouseEnter: onMouseEnterHandler, onMouseMove: onMouseMoveHandler, onMouseLeave: onMouseLeaveHandler, onContextMenu: onContextMenuHandler, onClick: onSelectNodeHandler, onDoubleClick: onDoubleClickHandler, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, onFocus: isFocusable ? onFocus : undefined, role: node.ariaRole ?? (isFocusable ? 'group' : undefined), \"aria-roledescription\": \"node\", \"aria-describedby\": disableKeyboardA11y ? undefined : `${ARIA_NODE_DESC_KEY}-${rfId}`, \"aria-label\": node.ariaLabel, ...node.domAttributes, children: jsx(Provider, { value: id, children: jsx(NodeComponent, { id: id, data: node.data, type: nodeType, positionAbsoluteX: internals.positionAbsolute.x, positionAbsoluteY: internals.positionAbsolute.y, selected: node.selected ?? false, selectable: isSelectable, draggable: isDraggable, deletable: node.deletable ?? true, isConnectable: isConnectable, sourcePosition: node.sourcePosition, targetPosition: node.targetPosition, dragging: dragging, dragHandle: node.dragHandle, zIndex: internals.z, parentId: node.parentId, ...nodeDimensions }) }) }));\n}\n\nconst selector$b = (s) => ({\n    nodesDraggable: s.nodesDraggable,\n    nodesConnectable: s.nodesConnectable,\n    nodesFocusable: s.nodesFocusable,\n    elementsSelectable: s.elementsSelectable,\n    onError: s.onError,\n});\nfunction NodeRendererComponent(props) {\n    const { nodesDraggable, nodesConnectable, nodesFocusable, elementsSelectable, onError } = useStore(selector$b, shallow);\n    const nodeIds = useVisibleNodeIds(props.onlyRenderVisibleElements);\n    const resizeObserver = useResizeObserver();\n    return (jsx(\"div\", { className: \"react-flow__nodes\", style: containerStyle, children: nodeIds.map((nodeId) => {\n            return (\n            /*\n             * The split of responsibilities between NodeRenderer and\n             * NodeComponentWrapper may appear weird. However, it’s designed to\n             * minimize the cost of updates when individual nodes change.\n             *\n             * For example, when you’re dragging a single node, that node gets\n             * updated multiple times per second. If `NodeRenderer` were to update\n             * every time, it would have to re-run the `nodes.map()` loop every\n             * time. This gets pricey with hundreds of nodes, especially if every\n             * loop cycle does more than just rendering a JSX element!\n             *\n             * As a result of this choice, we took the following implementation\n             * decisions:\n             * - NodeRenderer subscribes *only* to node IDs – and therefore\n             *   rerender *only* when visible nodes are added or removed.\n             * - NodeRenderer performs all operations the result of which can be\n             *   shared between nodes (such as creating the `ResizeObserver`\n             *   instance, or subscribing to `selector`). This means extra prop\n             *   drilling into `NodeComponentWrapper`, but it means we need to run\n             *   these operations only once – instead of once per node.\n             * - Any operations that you’d normally write inside `nodes.map` are\n             *   moved into `NodeComponentWrapper`. This ensures they are\n             *   memorized – so if `NodeRenderer` *has* to rerender, it only\n             *   needs to regenerate the list of nodes, nothing else.\n             */\n            jsx(NodeWrapper, { id: nodeId, nodeTypes: props.nodeTypes, nodeExtent: props.nodeExtent, onClick: props.onNodeClick, onMouseEnter: props.onNodeMouseEnter, onMouseMove: props.onNodeMouseMove, onMouseLeave: props.onNodeMouseLeave, onContextMenu: props.onNodeContextMenu, onDoubleClick: props.onNodeDoubleClick, noDragClassName: props.noDragClassName, noPanClassName: props.noPanClassName, rfId: props.rfId, disableKeyboardA11y: props.disableKeyboardA11y, resizeObserver: resizeObserver, nodesDraggable: nodesDraggable, nodesConnectable: nodesConnectable, nodesFocusable: nodesFocusable, elementsSelectable: elementsSelectable, nodeClickDistance: props.nodeClickDistance, onError: onError }, nodeId));\n        }) }));\n}\nNodeRendererComponent.displayName = 'NodeRenderer';\nconst NodeRenderer = memo(NodeRendererComponent);\n\n/**\n * Hook for getting the visible edge ids from the store.\n *\n * @internal\n * @param onlyRenderVisible\n * @returns array with visible edge ids\n */\nfunction useVisibleEdgeIds(onlyRenderVisible) {\n    const edgeIds = useStore(useCallback((s) => {\n        if (!onlyRenderVisible) {\n            return s.edges.map((edge) => edge.id);\n        }\n        const visibleEdgeIds = [];\n        if (s.width && s.height) {\n            for (const edge of s.edges) {\n                const sourceNode = s.nodeLookup.get(edge.source);\n                const targetNode = s.nodeLookup.get(edge.target);\n                if (sourceNode &&\n                    targetNode &&\n                    isEdgeVisible({\n                        sourceNode,\n                        targetNode,\n                        width: s.width,\n                        height: s.height,\n                        transform: s.transform,\n                    })) {\n                    visibleEdgeIds.push(edge.id);\n                }\n            }\n        }\n        return visibleEdgeIds;\n    }, [onlyRenderVisible]), shallow);\n    return edgeIds;\n}\n\nconst ArrowSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (jsx(\"polyline\", { style: {\n            stroke: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", fill: \"none\", points: \"-5,-4 0,0 -5,4\" }));\n};\nconst ArrowClosedSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (jsx(\"polyline\", { style: {\n            stroke: color,\n            fill: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", points: \"-5,-4 0,0 -5,4 -5,-4\" }));\n};\nconst MarkerSymbols = {\n    [MarkerType.Arrow]: ArrowSymbol,\n    [MarkerType.ArrowClosed]: ArrowClosedSymbol,\n};\nfunction useMarkerSymbol(type) {\n    const store = useStoreApi();\n    const symbol = useMemo(() => {\n        const symbolExists = Object.prototype.hasOwnProperty.call(MarkerSymbols, type);\n        if (!symbolExists) {\n            store.getState().onError?.('009', errorMessages['error009'](type));\n            return null;\n        }\n        return MarkerSymbols[type];\n    }, [type]);\n    return symbol;\n}\n\nconst Marker = ({ id, type, color, width = 12.5, height = 12.5, markerUnits = 'strokeWidth', strokeWidth, orient = 'auto-start-reverse', }) => {\n    const Symbol = useMarkerSymbol(type);\n    if (!Symbol) {\n        return null;\n    }\n    return (jsx(\"marker\", { className: \"react-flow__arrowhead\", id: id, markerWidth: `${width}`, markerHeight: `${height}`, viewBox: \"-10 -10 20 20\", markerUnits: markerUnits, orient: orient, refX: \"0\", refY: \"0\", children: jsx(Symbol, { color: color, strokeWidth: strokeWidth }) }));\n};\n/*\n * when you have multiple flows on a page and you hide the first one, the other ones have no markers anymore\n * when they do have markers with the same ids. To prevent this the user can pass a unique id to the react flow wrapper\n * that we can then use for creating our unique marker ids\n */\nconst MarkerDefinitions = ({ defaultColor, rfId }) => {\n    const edges = useStore((s) => s.edges);\n    const defaultEdgeOptions = useStore((s) => s.defaultEdgeOptions);\n    const markers = useMemo(() => {\n        const markers = createMarkerIds(edges, {\n            id: rfId,\n            defaultColor,\n            defaultMarkerStart: defaultEdgeOptions?.markerStart,\n            defaultMarkerEnd: defaultEdgeOptions?.markerEnd,\n        });\n        return markers;\n    }, [edges, defaultEdgeOptions, rfId, defaultColor]);\n    if (!markers.length) {\n        return null;\n    }\n    return (jsx(\"svg\", { className: \"react-flow__marker\", \"aria-hidden\": \"true\", children: jsx(\"defs\", { children: markers.map((marker) => (jsx(Marker, { id: marker.id, type: marker.type, color: marker.color, width: marker.width, height: marker.height, markerUnits: marker.markerUnits, strokeWidth: marker.strokeWidth, orient: marker.orient }, marker.id))) }) }));\n};\nMarkerDefinitions.displayName = 'MarkerDefinitions';\nvar MarkerDefinitions$1 = memo(MarkerDefinitions);\n\nfunction EdgeTextComponent({ x, y, label, labelStyle, labelShowBg = true, labelBgStyle, labelBgPadding = [2, 4], labelBgBorderRadius = 2, children, className, ...rest }) {\n    const [edgeTextBbox, setEdgeTextBbox] = useState({ x: 1, y: 0, width: 0, height: 0 });\n    const edgeTextClasses = cc(['react-flow__edge-textwrapper', className]);\n    const edgeTextRef = useRef(null);\n    useEffect(() => {\n        if (edgeTextRef.current) {\n            const textBbox = edgeTextRef.current.getBBox();\n            setEdgeTextBbox({\n                x: textBbox.x,\n                y: textBbox.y,\n                width: textBbox.width,\n                height: textBbox.height,\n            });\n        }\n    }, [label]);\n    if (!label) {\n        return null;\n    }\n    return (jsxs(\"g\", { transform: `translate(${x - edgeTextBbox.width / 2} ${y - edgeTextBbox.height / 2})`, className: edgeTextClasses, visibility: edgeTextBbox.width ? 'visible' : 'hidden', ...rest, children: [labelShowBg && (jsx(\"rect\", { width: edgeTextBbox.width + 2 * labelBgPadding[0], x: -labelBgPadding[0], y: -labelBgPadding[1], height: edgeTextBbox.height + 2 * labelBgPadding[1], className: \"react-flow__edge-textbg\", style: labelBgStyle, rx: labelBgBorderRadius, ry: labelBgBorderRadius })), jsx(\"text\", { className: \"react-flow__edge-text\", y: edgeTextBbox.height / 2, dy: \"0.3em\", ref: edgeTextRef, style: labelStyle, children: label }), children] }));\n}\nEdgeTextComponent.displayName = 'EdgeText';\n/**\n * You can use the `<EdgeText />` component as a helper component to display text\n * within your custom edges.\n *\n * @public\n *\n * @example\n * ```jsx\n * import { EdgeText } from '@xyflow/react';\n *\n * export function CustomEdgeLabel({ label }) {\n *   return (\n *     <EdgeText\n *       x={100}\n *       y={100}\n *       label={label}\n *       labelStyle={{ fill: 'white' }}\n *       labelShowBg\n *       labelBgStyle={{ fill: 'red' }}\n *       labelBgPadding={[2, 4]}\n *       labelBgBorderRadius={2}\n *     />\n *   );\n * }\n *```\n */\nconst EdgeText = memo(EdgeTextComponent);\n\n/**\n * The `<BaseEdge />` component gets used internally for all the edges. It can be\n * used inside a custom edge and handles the invisible helper edge and the edge label\n * for you.\n *\n * @public\n * @example\n * ```jsx\n *import { BaseEdge } from '@xyflow/react';\n *\n *export function CustomEdge({ sourceX, sourceY, targetX, targetY, ...props }) {\n *  const [edgePath] = getStraightPath({\n *    sourceX,\n *    sourceY,\n *    targetX,\n *    targetY,\n *  });\n *\n *  return <BaseEdge path={edgePath} {...props} />;\n *}\n *```\n *\n * @remarks If you want to use an edge marker with the [`<BaseEdge />`](/api-reference/components/base-edge) component,\n * you can pass the `markerStart` or `markerEnd` props passed to your custom edge\n * through to the [`<BaseEdge />`](/api-reference/components/base-edge) component.\n * You can see all the props passed to a custom edge by looking at the [`EdgeProps`](/api-reference/types/edge-props) type.\n */\nfunction BaseEdge({ path, labelX, labelY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, interactionWidth = 20, ...props }) {\n    return (jsxs(Fragment, { children: [jsx(\"path\", { ...props, d: path, fill: \"none\", className: cc(['react-flow__edge-path', props.className]) }), interactionWidth && (jsx(\"path\", { d: path, fill: \"none\", strokeOpacity: 0, strokeWidth: interactionWidth, className: \"react-flow__edge-interaction\" })), label && isNumeric(labelX) && isNumeric(labelY) ? (jsx(EdgeText, { x: labelX, y: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius })) : null] }));\n}\n\nfunction getControl({ pos, x1, y1, x2, y2 }) {\n    if (pos === Position.Left || pos === Position.Right) {\n        return [0.5 * (x1 + x2), y1];\n    }\n    return [x1, 0.5 * (y1 + y2)];\n}\n/**\n * The `getSimpleBezierPath` util returns everything you need to render a simple\n * bezier edge between two nodes.\n * @public\n * @returns\n * - `path`: the path to use in an SVG `<path>` element.\n * - `labelX`: the `x` position you can use to render a label for this edge.\n * - `labelY`: the `y` position you can use to render a label for this edge.\n * - `offsetX`: the absolute difference between the source `x` position and the `x` position of the\n * middle of this path.\n * - `offsetY`: the absolute difference between the source `y` position and the `y` position of the\n * middle of this path.\n */\nfunction getSimpleBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, }) {\n    const [sourceControlX, sourceControlY] = getControl({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n    });\n    const [targetControlX, targetControlY] = getControl({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\nfunction createSimpleBezierEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n        const [path, labelX, labelY] = getSimpleBezierPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\nconst SimpleBezierEdge = createSimpleBezierEdge({ isInternal: false });\nconst SimpleBezierEdgeInternal = createSimpleBezierEdge({ isInternal: true });\nSimpleBezierEdge.displayName = 'SimpleBezierEdge';\nSimpleBezierEdgeInternal.displayName = 'SimpleBezierEdgeInternal';\n\nfunction createSmoothStepEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, sourcePosition = Position.Bottom, targetPosition = Position.Top, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n        const [path, labelX, labelY] = getSmoothStepPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n            borderRadius: pathOptions?.borderRadius,\n            offset: pathOptions?.offset,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\n/**\n * Component that can be used inside a custom edge to render a smooth step edge.\n *\n * @public\n * @example\n *\n * ```tsx\n * import { SmoothStepEdge } from '@xyflow/react';\n *\n * function CustomEdge({ sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition }) {\n *   return (\n *     <SmoothStepEdge\n *       sourceX={sourceX}\n *       sourceY={sourceY}\n *       targetX={targetX}\n *       targetY={targetY}\n *       sourcePosition={sourcePosition}\n *       targetPosition={targetPosition}\n *     />\n *   );\n * }\n * ```\n */\nconst SmoothStepEdge = createSmoothStepEdge({ isInternal: false });\n/**\n * @internal\n */\nconst SmoothStepEdgeInternal = createSmoothStepEdge({ isInternal: true });\nSmoothStepEdge.displayName = 'SmoothStepEdge';\nSmoothStepEdgeInternal.displayName = 'SmoothStepEdgeInternal';\n\nfunction createStepEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, ...props }) => {\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(SmoothStepEdge, { ...props, id: _id, pathOptions: useMemo(() => ({ borderRadius: 0, offset: props.pathOptions?.offset }), [props.pathOptions?.offset]) }));\n    });\n}\n/**\n * Component that can be used inside a custom edge to render a step edge.\n *\n * @public\n * @example\n *\n * ```tsx\n * import { StepEdge } from '@xyflow/react';\n *\n * function CustomEdge({ sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition }) {\n *   return (\n *     <StepEdge\n *       sourceX={sourceX}\n *       sourceY={sourceY}\n *       targetX={targetX}\n *       targetY={targetY}\n *       sourcePosition={sourcePosition}\n *       targetPosition={targetPosition}\n *     />\n *   );\n * }\n * ```\n */\nconst StepEdge = createStepEdge({ isInternal: false });\n/**\n * @internal\n */\nconst StepEdgeInternal = createStepEdge({ isInternal: true });\nStepEdge.displayName = 'StepEdge';\nStepEdgeInternal.displayName = 'StepEdgeInternal';\n\nfunction createStraightEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n        const [path, labelX, labelY] = getStraightPath({ sourceX, sourceY, targetX, targetY });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\n/**\n * Component that can be used inside a custom edge to render a straight line.\n *\n * @public\n * @example\n *\n * ```tsx\n * import { StraightEdge } from '@xyflow/react';\n *\n * function CustomEdge({ sourceX, sourceY, targetX, targetY }) {\n *   return (\n *     <StraightEdge\n *       sourceX={sourceX}\n *       sourceY={sourceY}\n *       targetX={targetX}\n *       targetY={targetY}\n *     />\n *   );\n * }\n * ```\n */\nconst StraightEdge = createStraightEdge({ isInternal: false });\n/**\n * @internal\n */\nconst StraightEdgeInternal = createStraightEdge({ isInternal: true });\nStraightEdge.displayName = 'StraightEdge';\nStraightEdgeInternal.displayName = 'StraightEdgeInternal';\n\nfunction createBezierEdge(params) {\n    // eslint-disable-next-line react/display-name\n    return memo(({ id, sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n        const [path, labelX, labelY] = getBezierPath({\n            sourceX,\n            sourceY,\n            sourcePosition,\n            targetX,\n            targetY,\n            targetPosition,\n            curvature: pathOptions?.curvature,\n        });\n        const _id = params.isInternal ? undefined : id;\n        return (jsx(BaseEdge, { id: _id, path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n    });\n}\n/**\n * Component that can be used inside a custom edge to render a bezier curve.\n *\n * @public\n * @example\n *\n * ```tsx\n * import { BezierEdge } from '@xyflow/react';\n *\n * function CustomEdge({ sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition }) {\n *   return (\n *     <BezierEdge\n *       sourceX={sourceX}\n *       sourceY={sourceY}\n *       targetX={targetX}\n *       targetY={targetY}\n *       sourcePosition={sourcePosition}\n *       targetPosition={targetPosition}\n *     />\n *   );\n * }\n * ```\n */\nconst BezierEdge = createBezierEdge({ isInternal: false });\n/**\n * @internal\n */\nconst BezierEdgeInternal = createBezierEdge({ isInternal: true });\nBezierEdge.displayName = 'BezierEdge';\nBezierEdgeInternal.displayName = 'BezierEdgeInternal';\n\nconst builtinEdgeTypes = {\n    default: BezierEdgeInternal,\n    straight: StraightEdgeInternal,\n    step: StepEdgeInternal,\n    smoothstep: SmoothStepEdgeInternal,\n    simplebezier: SimpleBezierEdgeInternal,\n};\nconst nullPosition = {\n    sourceX: null,\n    sourceY: null,\n    targetX: null,\n    targetY: null,\n    sourcePosition: null,\n    targetPosition: null,\n};\n\nconst shiftX = (x, shift, position) => {\n    if (position === Position.Left)\n        return x - shift;\n    if (position === Position.Right)\n        return x + shift;\n    return x;\n};\nconst shiftY = (y, shift, position) => {\n    if (position === Position.Top)\n        return y - shift;\n    if (position === Position.Bottom)\n        return y + shift;\n    return y;\n};\nconst EdgeUpdaterClassName = 'react-flow__edgeupdater';\n/**\n * @internal\n */\nfunction EdgeAnchor({ position, centerX, centerY, radius = 10, onMouseDown, onMouseEnter, onMouseOut, type, }) {\n    return (jsx(\"circle\", { onMouseDown: onMouseDown, onMouseEnter: onMouseEnter, onMouseOut: onMouseOut, className: cc([EdgeUpdaterClassName, `${EdgeUpdaterClassName}-${type}`]), cx: shiftX(centerX, radius, position), cy: shiftY(centerY, radius, position), r: radius, stroke: \"transparent\", fill: \"transparent\" }));\n}\n\nfunction EdgeUpdateAnchors({ isReconnectable, reconnectRadius, edge, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, onReconnect, onReconnectStart, onReconnectEnd, setReconnecting, setUpdateHover, }) {\n    const store = useStoreApi();\n    const handleEdgeUpdater = (event, oppositeHandle) => {\n        // avoid triggering edge updater if mouse btn is not left\n        if (event.button !== 0) {\n            return;\n        }\n        const { autoPanOnConnect, domNode, isValidConnection, connectionMode, connectionRadius, lib, onConnectStart, onConnectEnd, cancelConnection, nodeLookup, rfId: flowId, panBy, updateConnection, } = store.getState();\n        const isTarget = oppositeHandle.type === 'target';\n        setReconnecting(true);\n        onReconnectStart?.(event, edge, oppositeHandle.type);\n        const _onReconnectEnd = (evt, connectionState) => {\n            setReconnecting(false);\n            onReconnectEnd?.(evt, edge, oppositeHandle.type, connectionState);\n        };\n        const onConnectEdge = (connection) => onReconnect?.(edge, connection);\n        XYHandle.onPointerDown(event.nativeEvent, {\n            autoPanOnConnect,\n            connectionMode,\n            connectionRadius,\n            domNode,\n            handleId: oppositeHandle.id,\n            nodeId: oppositeHandle.nodeId,\n            nodeLookup,\n            isTarget,\n            edgeUpdaterType: oppositeHandle.type,\n            lib,\n            flowId,\n            cancelConnection,\n            panBy,\n            isValidConnection,\n            onConnect: onConnectEdge,\n            onConnectStart,\n            onConnectEnd,\n            onReconnectEnd: _onReconnectEnd,\n            updateConnection,\n            getTransform: () => store.getState().transform,\n            getFromHandle: () => store.getState().connection.fromHandle,\n        });\n    };\n    const onReconnectSourceMouseDown = (event) => handleEdgeUpdater(event, { nodeId: edge.target, id: edge.targetHandle ?? null, type: 'target' });\n    const onReconnectTargetMouseDown = (event) => handleEdgeUpdater(event, { nodeId: edge.source, id: edge.sourceHandle ?? null, type: 'source' });\n    const onReconnectMouseEnter = () => setUpdateHover(true);\n    const onReconnectMouseOut = () => setUpdateHover(false);\n    return (jsxs(Fragment, { children: [(isReconnectable === true || isReconnectable === 'source') && (jsx(EdgeAnchor, { position: sourcePosition, centerX: sourceX, centerY: sourceY, radius: reconnectRadius, onMouseDown: onReconnectSourceMouseDown, onMouseEnter: onReconnectMouseEnter, onMouseOut: onReconnectMouseOut, type: \"source\" })), (isReconnectable === true || isReconnectable === 'target') && (jsx(EdgeAnchor, { position: targetPosition, centerX: targetX, centerY: targetY, radius: reconnectRadius, onMouseDown: onReconnectTargetMouseDown, onMouseEnter: onReconnectMouseEnter, onMouseOut: onReconnectMouseOut, type: \"target\" }))] }));\n}\n\nfunction EdgeWrapper({ id, edgesFocusable, edgesReconnectable, elementsSelectable, onClick, onDoubleClick, onContextMenu, onMouseEnter, onMouseMove, onMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, rfId, edgeTypes, noPanClassName, onError, disableKeyboardA11y, }) {\n    let edge = useStore((s) => s.edgeLookup.get(id));\n    const defaultEdgeOptions = useStore((s) => s.defaultEdgeOptions);\n    edge = defaultEdgeOptions ? { ...defaultEdgeOptions, ...edge } : edge;\n    let edgeType = edge.type || 'default';\n    let EdgeComponent = edgeTypes?.[edgeType] || builtinEdgeTypes[edgeType];\n    if (EdgeComponent === undefined) {\n        onError?.('011', errorMessages['error011'](edgeType));\n        edgeType = 'default';\n        EdgeComponent = builtinEdgeTypes.default;\n    }\n    const isFocusable = !!(edge.focusable || (edgesFocusable && typeof edge.focusable === 'undefined'));\n    const isReconnectable = typeof onReconnect !== 'undefined' &&\n        (edge.reconnectable || (edgesReconnectable && typeof edge.reconnectable === 'undefined'));\n    const isSelectable = !!(edge.selectable || (elementsSelectable && typeof edge.selectable === 'undefined'));\n    const edgeRef = useRef(null);\n    const [updateHover, setUpdateHover] = useState(false);\n    const [reconnecting, setReconnecting] = useState(false);\n    const store = useStoreApi();\n    const { zIndex, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition } = useStore(useCallback((store) => {\n        const sourceNode = store.nodeLookup.get(edge.source);\n        const targetNode = store.nodeLookup.get(edge.target);\n        if (!sourceNode || !targetNode) {\n            return {\n                zIndex: edge.zIndex,\n                ...nullPosition,\n            };\n        }\n        const edgePosition = getEdgePosition({\n            id,\n            sourceNode,\n            targetNode,\n            sourceHandle: edge.sourceHandle || null,\n            targetHandle: edge.targetHandle || null,\n            connectionMode: store.connectionMode,\n            onError,\n        });\n        const zIndex = getElevatedEdgeZIndex({\n            selected: edge.selected,\n            zIndex: edge.zIndex,\n            sourceNode,\n            targetNode,\n            elevateOnSelect: store.elevateEdgesOnSelect,\n        });\n        return {\n            zIndex,\n            ...(edgePosition || nullPosition),\n        };\n    }, [edge.source, edge.target, edge.sourceHandle, edge.targetHandle, edge.selected, edge.zIndex]), shallow);\n    const markerStartUrl = useMemo(() => (edge.markerStart ? `url('#${getMarkerId(edge.markerStart, rfId)}')` : undefined), [edge.markerStart, rfId]);\n    const markerEndUrl = useMemo(() => (edge.markerEnd ? `url('#${getMarkerId(edge.markerEnd, rfId)}')` : undefined), [edge.markerEnd, rfId]);\n    if (edge.hidden || sourceX === null || sourceY === null || targetX === null || targetY === null) {\n        return null;\n    }\n    const onEdgeClick = (event) => {\n        const { addSelectedEdges, unselectNodesAndEdges, multiSelectionActive } = store.getState();\n        if (isSelectable) {\n            store.setState({ nodesSelectionActive: false });\n            if (edge.selected && multiSelectionActive) {\n                unselectNodesAndEdges({ nodes: [], edges: [edge] });\n                edgeRef.current?.blur();\n            }\n            else {\n                addSelectedEdges([id]);\n            }\n        }\n        if (onClick) {\n            onClick(event, edge);\n        }\n    };\n    const onEdgeDoubleClick = onDoubleClick\n        ? (event) => {\n            onDoubleClick(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeContextMenu = onContextMenu\n        ? (event) => {\n            onContextMenu(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseEnter = onMouseEnter\n        ? (event) => {\n            onMouseEnter(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseMove = onMouseMove\n        ? (event) => {\n            onMouseMove(event, { ...edge });\n        }\n        : undefined;\n    const onEdgeMouseLeave = onMouseLeave\n        ? (event) => {\n            onMouseLeave(event, { ...edge });\n        }\n        : undefined;\n    const onKeyDown = (event) => {\n        if (!disableKeyboardA11y && elementSelectionKeys.includes(event.key) && isSelectable) {\n            const { unselectNodesAndEdges, addSelectedEdges } = store.getState();\n            const unselect = event.key === 'Escape';\n            if (unselect) {\n                edgeRef.current?.blur();\n                unselectNodesAndEdges({ edges: [edge] });\n            }\n            else {\n                addSelectedEdges([id]);\n            }\n        }\n    };\n    return (jsx(\"svg\", { style: { zIndex }, children: jsxs(\"g\", { className: cc([\n                'react-flow__edge',\n                `react-flow__edge-${edgeType}`,\n                edge.className,\n                noPanClassName,\n                {\n                    selected: edge.selected,\n                    animated: edge.animated,\n                    inactive: !isSelectable && !onClick,\n                    updating: updateHover,\n                    selectable: isSelectable,\n                },\n            ]), onClick: onEdgeClick, onDoubleClick: onEdgeDoubleClick, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, role: edge.ariaRole ?? (isFocusable ? 'group' : 'img'), \"aria-roledescription\": \"edge\", \"data-id\": id, \"data-testid\": `rf__edge-${id}`, \"aria-label\": edge.ariaLabel === null ? undefined : edge.ariaLabel || `Edge from ${edge.source} to ${edge.target}`, \"aria-describedby\": isFocusable ? `${ARIA_EDGE_DESC_KEY}-${rfId}` : undefined, ref: edgeRef, ...edge.domAttributes, children: [!reconnecting && (jsx(EdgeComponent, { id: id, source: edge.source, target: edge.target, type: edge.type, selected: edge.selected, animated: edge.animated, selectable: isSelectable, deletable: edge.deletable ?? true, label: edge.label, labelStyle: edge.labelStyle, labelShowBg: edge.labelShowBg, labelBgStyle: edge.labelBgStyle, labelBgPadding: edge.labelBgPadding, labelBgBorderRadius: edge.labelBgBorderRadius, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, data: edge.data, style: edge.style, sourceHandleId: edge.sourceHandle, targetHandleId: edge.targetHandle, markerStart: markerStartUrl, markerEnd: markerEndUrl, pathOptions: 'pathOptions' in edge ? edge.pathOptions : undefined, interactionWidth: edge.interactionWidth })), isReconnectable && (jsx(EdgeUpdateAnchors, { edge: edge, isReconnectable: isReconnectable, reconnectRadius: reconnectRadius, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, setUpdateHover: setUpdateHover, setReconnecting: setReconnecting }))] }) }));\n}\n\nconst selector$a = (s) => ({\n    edgesFocusable: s.edgesFocusable,\n    edgesReconnectable: s.edgesReconnectable,\n    elementsSelectable: s.elementsSelectable,\n    connectionMode: s.connectionMode,\n    onError: s.onError,\n});\nfunction EdgeRendererComponent({ defaultMarkerColor, onlyRenderVisibleElements, rfId, edgeTypes, noPanClassName, onReconnect, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeClick, reconnectRadius, onEdgeDoubleClick, onReconnectStart, onReconnectEnd, disableKeyboardA11y, }) {\n    const { edgesFocusable, edgesReconnectable, elementsSelectable, onError } = useStore(selector$a, shallow);\n    const edgeIds = useVisibleEdgeIds(onlyRenderVisibleElements);\n    return (jsxs(\"div\", { className: \"react-flow__edges\", children: [jsx(MarkerDefinitions$1, { defaultColor: defaultMarkerColor, rfId: rfId }), edgeIds.map((id) => {\n                return (jsx(EdgeWrapper, { id: id, edgesFocusable: edgesFocusable, edgesReconnectable: edgesReconnectable, elementsSelectable: elementsSelectable, noPanClassName: noPanClassName, onReconnect: onReconnect, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onClick: onEdgeClick, reconnectRadius: reconnectRadius, onDoubleClick: onEdgeDoubleClick, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, rfId: rfId, onError: onError, edgeTypes: edgeTypes, disableKeyboardA11y: disableKeyboardA11y }, id));\n            })] }));\n}\nEdgeRendererComponent.displayName = 'EdgeRenderer';\nconst EdgeRenderer = memo(EdgeRendererComponent);\n\nconst selector$9 = (s) => `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`;\nfunction Viewport({ children }) {\n    const transform = useStore(selector$9);\n    return (jsx(\"div\", { className: \"react-flow__viewport xyflow__viewport react-flow__container\", style: { transform }, children: children }));\n}\n\n/**\n * Hook for calling onInit handler.\n *\n * @internal\n */\nfunction useOnInitHandler(onInit) {\n    const rfInstance = useReactFlow();\n    const isInitialized = useRef(false);\n    useEffect(() => {\n        if (!isInitialized.current && rfInstance.viewportInitialized && onInit) {\n            setTimeout(() => onInit(rfInstance), 1);\n            isInitialized.current = true;\n        }\n    }, [onInit, rfInstance.viewportInitialized]);\n}\n\nconst selector$8 = (state) => state.panZoom?.syncViewport;\n/**\n * Hook for syncing the viewport with the panzoom instance.\n *\n * @internal\n * @param viewport\n */\nfunction useViewportSync(viewport) {\n    const syncViewport = useStore(selector$8);\n    const store = useStoreApi();\n    useEffect(() => {\n        if (viewport) {\n            syncViewport?.(viewport);\n            store.setState({ transform: [viewport.x, viewport.y, viewport.zoom] });\n        }\n    }, [viewport, syncViewport]);\n    return null;\n}\n\nfunction storeSelector$1(s) {\n    return s.connection.inProgress\n        ? { ...s.connection, to: pointToRendererPoint(s.connection.to, s.transform) }\n        : { ...s.connection };\n}\nfunction getSelector(connectionSelector) {\n    if (connectionSelector) {\n        const combinedSelector = (s) => {\n            const connection = storeSelector$1(s);\n            return connectionSelector(connection);\n        };\n        return combinedSelector;\n    }\n    return storeSelector$1;\n}\n/**\n * The `useConnection` hook returns the current connection when there is an active\n * connection interaction. If no connection interaction is active, it returns null\n * for every property. A typical use case for this hook is to colorize handles\n * based on a certain condition (e.g. if the connection is valid or not).\n *\n * @public\n * @param connectionSelector - An optional selector function used to extract a slice of the\n * `ConnectionState` data. Using a selector can prevent component re-renders where data you don't\n * otherwise care about might change. If a selector is not provided, the entire `ConnectionState`\n * object is returned unchanged.\n * @example\n *\n * ```tsx\n *import { useConnection } from '@xyflow/react';\n *\n *function App() {\n *  const connection = useConnection();\n *\n *  return (\n *    <div> {connection ? `Someone is trying to make a connection from ${connection.fromNode} to this one.` : 'There are currently no incoming connections!'}\n *\n *   </div>\n *   );\n * }\n * ```\n *\n * @returns ConnectionState\n */\nfunction useConnection(connectionSelector) {\n    const combinedSelector = getSelector(connectionSelector);\n    return useStore(combinedSelector, shallow);\n}\n\nconst selector$7 = (s) => ({\n    nodesConnectable: s.nodesConnectable,\n    isValid: s.connection.isValid,\n    inProgress: s.connection.inProgress,\n    width: s.width,\n    height: s.height,\n});\nfunction ConnectionLineWrapper({ containerStyle, style, type, component, }) {\n    const { nodesConnectable, width, height, isValid, inProgress } = useStore(selector$7, shallow);\n    const renderConnection = !!(width && nodesConnectable && inProgress);\n    if (!renderConnection) {\n        return null;\n    }\n    return (jsx(\"svg\", { style: containerStyle, width: width, height: height, className: \"react-flow__connectionline react-flow__container\", children: jsx(\"g\", { className: cc(['react-flow__connection', getConnectionStatus(isValid)]), children: jsx(ConnectionLine, { style: style, type: type, CustomComponent: component, isValid: isValid }) }) }));\n}\nconst ConnectionLine = ({ style, type = ConnectionLineType.Bezier, CustomComponent, isValid, }) => {\n    const { inProgress, from, fromNode, fromHandle, fromPosition, to, toNode, toHandle, toPosition } = useConnection();\n    if (!inProgress) {\n        return;\n    }\n    if (CustomComponent) {\n        return (jsx(CustomComponent, { connectionLineType: type, connectionLineStyle: style, fromNode: fromNode, fromHandle: fromHandle, fromX: from.x, fromY: from.y, toX: to.x, toY: to.y, fromPosition: fromPosition, toPosition: toPosition, connectionStatus: getConnectionStatus(isValid), toNode: toNode, toHandle: toHandle }));\n    }\n    let path = '';\n    const pathParams = {\n        sourceX: from.x,\n        sourceY: from.y,\n        sourcePosition: fromPosition,\n        targetX: to.x,\n        targetY: to.y,\n        targetPosition: toPosition,\n    };\n    switch (type) {\n        case ConnectionLineType.Bezier:\n            [path] = getBezierPath(pathParams);\n            break;\n        case ConnectionLineType.SimpleBezier:\n            [path] = getSimpleBezierPath(pathParams);\n            break;\n        case ConnectionLineType.Step:\n            [path] = getSmoothStepPath({\n                ...pathParams,\n                borderRadius: 0,\n            });\n            break;\n        case ConnectionLineType.SmoothStep:\n            [path] = getSmoothStepPath(pathParams);\n            break;\n        default:\n            [path] = getStraightPath(pathParams);\n    }\n    return jsx(\"path\", { d: path, fill: \"none\", className: \"react-flow__connection-path\", style: style });\n};\nConnectionLine.displayName = 'ConnectionLine';\n\nconst emptyTypes = {};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useNodeOrEdgeTypesWarning(nodeOrEdgeTypes = emptyTypes) {\n    const typesRef = useRef(nodeOrEdgeTypes);\n    const store = useStoreApi();\n    useEffect(() => {\n        if (process.env.NODE_ENV === 'development') {\n            const usedKeys = new Set([...Object.keys(typesRef.current), ...Object.keys(nodeOrEdgeTypes)]);\n            for (const key of usedKeys) {\n                if (typesRef.current[key] !== nodeOrEdgeTypes[key]) {\n                    store.getState().onError?.('002', errorMessages['error002']());\n                    break;\n                }\n            }\n            typesRef.current = nodeOrEdgeTypes;\n        }\n    }, [nodeOrEdgeTypes]);\n}\n\nfunction useStylesLoadedWarning() {\n    const store = useStoreApi();\n    const checked = useRef(false);\n    useEffect(() => {\n        if (process.env.NODE_ENV === 'development') {\n            if (!checked.current) {\n                const pane = document.querySelector('.react-flow__pane');\n                if (pane && !(window.getComputedStyle(pane).zIndex === '1')) {\n                    store.getState().onError?.('013', errorMessages['error013']('react'));\n                }\n                checked.current = true;\n            }\n        }\n    }, []);\n}\n\nfunction GraphViewComponent({ nodeTypes, edgeTypes, onInit, onNodeClick, onEdgeClick, onNodeDoubleClick, onEdgeDoubleClick, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionLineType, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, selectionKeyCode, selectionOnDrag, selectionMode, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, deleteKeyCode, onlyRenderVisibleElements, elementsSelectable, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, defaultMarkerColor, zoomOnScroll, zoomOnPinch, panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, paneClickDistance, nodeClickDistance, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, noDragClassName, noWheelClassName, noPanClassName, disableKeyboardA11y, nodeExtent, rfId, viewport, onViewportChange, }) {\n    useNodeOrEdgeTypesWarning(nodeTypes);\n    useNodeOrEdgeTypesWarning(edgeTypes);\n    useStylesLoadedWarning();\n    useOnInitHandler(onInit);\n    useViewportSync(viewport);\n    return (jsx(FlowRenderer, { onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, paneClickDistance: paneClickDistance, deleteKeyCode: deleteKeyCode, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, elementsSelectable: elementsSelectable, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, onSelectionContextMenu: onSelectionContextMenu, preventScrolling: preventScrolling, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y, onViewportChange: onViewportChange, isControlledViewport: !!viewport, children: jsxs(Viewport, { children: [jsx(EdgeRenderer, { edgeTypes: edgeTypes, onEdgeClick: onEdgeClick, onEdgeDoubleClick: onEdgeDoubleClick, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, onlyRenderVisibleElements: onlyRenderVisibleElements, onEdgeContextMenu: onEdgeContextMenu, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, reconnectRadius: reconnectRadius, defaultMarkerColor: defaultMarkerColor, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y, rfId: rfId }), jsx(ConnectionLineWrapper, { style: connectionLineStyle, type: connectionLineType, component: connectionLineComponent, containerStyle: connectionLineContainerStyle }), jsx(\"div\", { className: \"react-flow__edgelabel-renderer\" }), jsx(NodeRenderer, { nodeTypes: nodeTypes, onNodeClick: onNodeClick, onNodeDoubleClick: onNodeDoubleClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, nodeClickDistance: nodeClickDistance, onlyRenderVisibleElements: onlyRenderVisibleElements, noPanClassName: noPanClassName, noDragClassName: noDragClassName, disableKeyboardA11y: disableKeyboardA11y, nodeExtent: nodeExtent, rfId: rfId }), jsx(\"div\", { className: \"react-flow__viewport-portal\" })] }) }));\n}\nGraphViewComponent.displayName = 'GraphView';\nconst GraphView = memo(GraphViewComponent);\n\nconst getInitialState = ({ nodes, edges, defaultNodes, defaultEdges, width, height, fitView, fitViewOptions, minZoom = 0.5, maxZoom = 2, nodeOrigin, nodeExtent, } = {}) => {\n    const nodeLookup = new Map();\n    const parentLookup = new Map();\n    const connectionLookup = new Map();\n    const edgeLookup = new Map();\n    const storeEdges = defaultEdges ?? edges ?? [];\n    const storeNodes = defaultNodes ?? nodes ?? [];\n    const storeNodeOrigin = nodeOrigin ?? [0, 0];\n    const storeNodeExtent = nodeExtent ?? infiniteExtent;\n    updateConnectionLookup(connectionLookup, edgeLookup, storeEdges);\n    const nodesInitialized = adoptUserNodes(storeNodes, nodeLookup, parentLookup, {\n        nodeOrigin: storeNodeOrigin,\n        nodeExtent: storeNodeExtent,\n        elevateNodesOnSelect: false,\n    });\n    let transform = [0, 0, 1];\n    if (fitView && width && height) {\n        const bounds = getInternalNodesBounds(nodeLookup, {\n            filter: (node) => !!((node.width || node.initialWidth) && (node.height || node.initialHeight)),\n        });\n        const { x, y, zoom } = getViewportForBounds(bounds, width, height, minZoom, maxZoom, fitViewOptions?.padding ?? 0.1);\n        transform = [x, y, zoom];\n    }\n    return {\n        rfId: '1',\n        width: 0,\n        height: 0,\n        transform,\n        nodes: storeNodes,\n        nodesInitialized,\n        nodeLookup,\n        parentLookup,\n        edges: storeEdges,\n        edgeLookup,\n        connectionLookup,\n        onNodesChange: null,\n        onEdgesChange: null,\n        hasDefaultNodes: defaultNodes !== undefined,\n        hasDefaultEdges: defaultEdges !== undefined,\n        panZoom: null,\n        minZoom,\n        maxZoom,\n        translateExtent: infiniteExtent,\n        nodeExtent: storeNodeExtent,\n        nodesSelectionActive: false,\n        userSelectionActive: false,\n        userSelectionRect: null,\n        connectionMode: ConnectionMode.Strict,\n        domNode: null,\n        paneDragging: false,\n        noPanClassName: 'nopan',\n        nodeOrigin: storeNodeOrigin,\n        nodeDragThreshold: 1,\n        snapGrid: [15, 15],\n        snapToGrid: false,\n        nodesDraggable: true,\n        nodesConnectable: true,\n        nodesFocusable: true,\n        edgesFocusable: true,\n        edgesReconnectable: true,\n        elementsSelectable: true,\n        elevateNodesOnSelect: true,\n        elevateEdgesOnSelect: false,\n        selectNodesOnDrag: true,\n        multiSelectionActive: false,\n        fitViewQueued: fitView ?? false,\n        fitViewOptions,\n        fitViewResolver: null,\n        connection: { ...initialConnection },\n        connectionClickStartHandle: null,\n        connectOnClick: true,\n        ariaLiveMessage: '',\n        autoPanOnConnect: true,\n        autoPanOnNodeDrag: true,\n        autoPanOnNodeFocus: true,\n        autoPanSpeed: 15,\n        connectionRadius: 20,\n        onError: devWarn,\n        isValidConnection: undefined,\n        onSelectionChangeHandlers: [],\n        lib: 'react',\n        debug: false,\n        ariaLabelConfig: defaultAriaLabelConfig,\n    };\n};\n\nconst createStore = ({ nodes, edges, defaultNodes, defaultEdges, width, height, fitView, fitViewOptions, minZoom, maxZoom, nodeOrigin, nodeExtent, }) => createWithEqualityFn((set, get) => {\n    async function resolveFitView() {\n        const { nodeLookup, panZoom, fitViewOptions, fitViewResolver, width, height, minZoom, maxZoom } = get();\n        if (!panZoom) {\n            return;\n        }\n        await fitViewport({\n            nodes: nodeLookup,\n            width,\n            height,\n            panZoom,\n            minZoom,\n            maxZoom,\n        }, fitViewOptions);\n        fitViewResolver?.resolve(true);\n        /**\n         * wait for the fitViewport to resolve before deleting the resolver,\n         * we want to reuse the old resolver if the user calls fitView again in the mean time\n         */\n        set({ fitViewResolver: null });\n    }\n    return {\n        ...getInitialState({\n            nodes,\n            edges,\n            width,\n            height,\n            fitView,\n            fitViewOptions,\n            minZoom,\n            maxZoom,\n            nodeOrigin,\n            nodeExtent,\n            defaultNodes,\n            defaultEdges,\n        }),\n        setNodes: (nodes) => {\n            const { nodeLookup, parentLookup, nodeOrigin, elevateNodesOnSelect, fitViewQueued } = get();\n            /*\n             * setNodes() is called exclusively in response to user actions:\n             * - either when the `<ReactFlow nodes>` prop is updated in the controlled ReactFlow setup,\n             * - or when the user calls something like `reactFlowInstance.setNodes()` in an uncontrolled ReactFlow setup.\n             *\n             * When this happens, we take the note objects passed by the user and extend them with fields\n             * relevant for internal React Flow operations.\n             */\n            const nodesInitialized = adoptUserNodes(nodes, nodeLookup, parentLookup, {\n                nodeOrigin,\n                nodeExtent,\n                elevateNodesOnSelect,\n                checkEquality: true,\n            });\n            if (fitViewQueued && nodesInitialized) {\n                resolveFitView();\n                set({ nodes, nodesInitialized, fitViewQueued: false, fitViewOptions: undefined });\n            }\n            else {\n                set({ nodes, nodesInitialized });\n            }\n        },\n        setEdges: (edges) => {\n            const { connectionLookup, edgeLookup } = get();\n            updateConnectionLookup(connectionLookup, edgeLookup, edges);\n            set({ edges });\n        },\n        setDefaultNodesAndEdges: (nodes, edges) => {\n            if (nodes) {\n                const { setNodes } = get();\n                setNodes(nodes);\n                set({ hasDefaultNodes: true });\n            }\n            if (edges) {\n                const { setEdges } = get();\n                setEdges(edges);\n                set({ hasDefaultEdges: true });\n            }\n        },\n        /*\n         * Every node gets registerd at a ResizeObserver. Whenever a node\n         * changes its dimensions, this function is called to measure the\n         * new dimensions and update the nodes.\n         */\n        updateNodeInternals: (updates) => {\n            const { triggerNodeChanges, nodeLookup, parentLookup, domNode, nodeOrigin, nodeExtent, debug, fitViewQueued } = get();\n            const { changes, updatedInternals } = updateNodeInternals(updates, nodeLookup, parentLookup, domNode, nodeOrigin, nodeExtent);\n            if (!updatedInternals) {\n                return;\n            }\n            updateAbsolutePositions(nodeLookup, parentLookup, { nodeOrigin, nodeExtent });\n            if (fitViewQueued) {\n                resolveFitView();\n                set({ fitViewQueued: false, fitViewOptions: undefined });\n            }\n            else {\n                // we always want to trigger useStore calls whenever updateNodeInternals is called\n                set({});\n            }\n            if (changes?.length > 0) {\n                if (debug) {\n                    console.log('React Flow: trigger node changes', changes);\n                }\n                triggerNodeChanges?.(changes);\n            }\n        },\n        updateNodePositions: (nodeDragItems, dragging = false) => {\n            const parentExpandChildren = [];\n            const changes = [];\n            const { nodeLookup, triggerNodeChanges } = get();\n            for (const [id, dragItem] of nodeDragItems) {\n                // we are using the nodelookup to be sure to use the current expandParent and parentId value\n                const node = nodeLookup.get(id);\n                const expandParent = !!(node?.expandParent && node?.parentId && dragItem?.position);\n                const change = {\n                    id,\n                    type: 'position',\n                    position: expandParent\n                        ? {\n                            x: Math.max(0, dragItem.position.x),\n                            y: Math.max(0, dragItem.position.y),\n                        }\n                        : dragItem.position,\n                    dragging,\n                };\n                if (expandParent && node.parentId) {\n                    parentExpandChildren.push({\n                        id,\n                        parentId: node.parentId,\n                        rect: {\n                            ...dragItem.internals.positionAbsolute,\n                            width: dragItem.measured.width ?? 0,\n                            height: dragItem.measured.height ?? 0,\n                        },\n                    });\n                }\n                changes.push(change);\n            }\n            if (parentExpandChildren.length > 0) {\n                const { parentLookup, nodeOrigin } = get();\n                const parentExpandChanges = handleExpandParent(parentExpandChildren, nodeLookup, parentLookup, nodeOrigin);\n                changes.push(...parentExpandChanges);\n            }\n            triggerNodeChanges(changes);\n        },\n        triggerNodeChanges: (changes) => {\n            const { onNodesChange, setNodes, nodes, hasDefaultNodes, debug } = get();\n            if (changes?.length) {\n                if (hasDefaultNodes) {\n                    const updatedNodes = applyNodeChanges(changes, nodes);\n                    setNodes(updatedNodes);\n                }\n                if (debug) {\n                    console.log('React Flow: trigger node changes', changes);\n                }\n                onNodesChange?.(changes);\n            }\n        },\n        triggerEdgeChanges: (changes) => {\n            const { onEdgesChange, setEdges, edges, hasDefaultEdges, debug } = get();\n            if (changes?.length) {\n                if (hasDefaultEdges) {\n                    const updatedEdges = applyEdgeChanges(changes, edges);\n                    setEdges(updatedEdges);\n                }\n                if (debug) {\n                    console.log('React Flow: trigger edge changes', changes);\n                }\n                onEdgesChange?.(changes);\n            }\n        },\n        addSelectedNodes: (selectedNodeIds) => {\n            const { multiSelectionActive, edgeLookup, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n            if (multiSelectionActive) {\n                const nodeChanges = selectedNodeIds.map((nodeId) => createSelectionChange(nodeId, true));\n                triggerNodeChanges(nodeChanges);\n                return;\n            }\n            triggerNodeChanges(getSelectionChanges(nodeLookup, new Set([...selectedNodeIds]), true));\n            triggerEdgeChanges(getSelectionChanges(edgeLookup));\n        },\n        addSelectedEdges: (selectedEdgeIds) => {\n            const { multiSelectionActive, edgeLookup, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n            if (multiSelectionActive) {\n                const changedEdges = selectedEdgeIds.map((edgeId) => createSelectionChange(edgeId, true));\n                triggerEdgeChanges(changedEdges);\n                return;\n            }\n            triggerEdgeChanges(getSelectionChanges(edgeLookup, new Set([...selectedEdgeIds])));\n            triggerNodeChanges(getSelectionChanges(nodeLookup, new Set(), true));\n        },\n        unselectNodesAndEdges: ({ nodes, edges } = {}) => {\n            const { edges: storeEdges, nodes: storeNodes, nodeLookup, triggerNodeChanges, triggerEdgeChanges } = get();\n            const nodesToUnselect = nodes ? nodes : storeNodes;\n            const edgesToUnselect = edges ? edges : storeEdges;\n            const nodeChanges = nodesToUnselect.map((n) => {\n                const internalNode = nodeLookup.get(n.id);\n                if (internalNode) {\n                    /*\n                     * we need to unselect the internal node that was selected previously before we\n                     * send the change to the user to prevent it to be selected while dragging the new node\n                     */\n                    internalNode.selected = false;\n                }\n                return createSelectionChange(n.id, false);\n            });\n            const edgeChanges = edgesToUnselect.map((edge) => createSelectionChange(edge.id, false));\n            triggerNodeChanges(nodeChanges);\n            triggerEdgeChanges(edgeChanges);\n        },\n        setMinZoom: (minZoom) => {\n            const { panZoom, maxZoom } = get();\n            panZoom?.setScaleExtent([minZoom, maxZoom]);\n            set({ minZoom });\n        },\n        setMaxZoom: (maxZoom) => {\n            const { panZoom, minZoom } = get();\n            panZoom?.setScaleExtent([minZoom, maxZoom]);\n            set({ maxZoom });\n        },\n        setTranslateExtent: (translateExtent) => {\n            get().panZoom?.setTranslateExtent(translateExtent);\n            set({ translateExtent });\n        },\n        setPaneClickDistance: (clickDistance) => {\n            get().panZoom?.setClickDistance(clickDistance);\n        },\n        resetSelectedElements: () => {\n            const { edges, nodes, triggerNodeChanges, triggerEdgeChanges, elementsSelectable } = get();\n            if (!elementsSelectable) {\n                return;\n            }\n            const nodeChanges = nodes.reduce((res, node) => (node.selected ? [...res, createSelectionChange(node.id, false)] : res), []);\n            const edgeChanges = edges.reduce((res, edge) => (edge.selected ? [...res, createSelectionChange(edge.id, false)] : res), []);\n            triggerNodeChanges(nodeChanges);\n            triggerEdgeChanges(edgeChanges);\n        },\n        setNodeExtent: (nextNodeExtent) => {\n            const { nodes, nodeLookup, parentLookup, nodeOrigin, elevateNodesOnSelect, nodeExtent } = get();\n            if (nextNodeExtent[0][0] === nodeExtent[0][0] &&\n                nextNodeExtent[0][1] === nodeExtent[0][1] &&\n                nextNodeExtent[1][0] === nodeExtent[1][0] &&\n                nextNodeExtent[1][1] === nodeExtent[1][1]) {\n                return;\n            }\n            adoptUserNodes(nodes, nodeLookup, parentLookup, {\n                nodeOrigin,\n                nodeExtent: nextNodeExtent,\n                elevateNodesOnSelect,\n                checkEquality: false,\n            });\n            set({ nodeExtent: nextNodeExtent });\n        },\n        panBy: (delta) => {\n            const { transform, width, height, panZoom, translateExtent } = get();\n            return panBy({ delta, panZoom, transform, translateExtent, width, height });\n        },\n        setCenter: async (x, y, options) => {\n            const { width, height, maxZoom, panZoom } = get();\n            if (!panZoom) {\n                return Promise.resolve(false);\n            }\n            const nextZoom = typeof options?.zoom !== 'undefined' ? options.zoom : maxZoom;\n            await panZoom.setViewport({\n                x: width / 2 - x * nextZoom,\n                y: height / 2 - y * nextZoom,\n                zoom: nextZoom,\n            }, { duration: options?.duration, ease: options?.ease, interpolate: options?.interpolate });\n            return Promise.resolve(true);\n        },\n        cancelConnection: () => {\n            set({\n                connection: { ...initialConnection },\n            });\n        },\n        updateConnection: (connection) => {\n            set({ connection });\n        },\n        reset: () => set({ ...getInitialState() }),\n    };\n}, Object.is);\n\n/**\n * The `<ReactFlowProvider />` component is a [context provider](https://react.dev/learn/passing-data-deeply-with-context#)\n * that makes it possible to access a flow's internal state outside of the\n * [`<ReactFlow />`](/api-reference/react-flow) component. Many of the hooks we\n * provide rely on this component to work.\n * @public\n *\n * @example\n * ```tsx\n *import { ReactFlow, ReactFlowProvider, useNodes } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlowProvider>\n *      <ReactFlow nodes={...} edges={...} />\n *      <Sidebar />\n *    </ReactFlowProvider>\n *  );\n *}\n *\n *function Sidebar() {\n *  // This hook will only work if the component it's used in is a child of a\n *  // <ReactFlowProvider />.\n *  const nodes = useNodes()\n *\n *  return <aside>do something with nodes</aside>;\n *}\n *```\n *\n * @remarks If you're using a router and want your flow's state to persist across routes,\n * it's vital that you place the `<ReactFlowProvider />` component _outside_ of\n * your router. If you have multiple flows on the same page you will need to use a separate\n * `<ReactFlowProvider />` for each flow.\n */\nfunction ReactFlowProvider({ initialNodes: nodes, initialEdges: edges, defaultNodes, defaultEdges, initialWidth: width, initialHeight: height, initialMinZoom: minZoom, initialMaxZoom: maxZoom, initialFitViewOptions: fitViewOptions, fitView, nodeOrigin, nodeExtent, children, }) {\n    const [store] = useState(() => createStore({\n        nodes,\n        edges,\n        defaultNodes,\n        defaultEdges,\n        width,\n        height,\n        fitView,\n        minZoom,\n        maxZoom,\n        fitViewOptions,\n        nodeOrigin,\n        nodeExtent,\n    }));\n    return (jsx(Provider$1, { value: store, children: jsx(BatchProvider, { children: children }) }));\n}\n\nfunction Wrapper({ children, nodes, edges, defaultNodes, defaultEdges, width, height, fitView, fitViewOptions, minZoom, maxZoom, nodeOrigin, nodeExtent, }) {\n    const isWrapped = useContext(StoreContext);\n    if (isWrapped) {\n        /*\n         * we need to wrap it with a fragment because it's not allowed for children to be a ReactNode\n         * https://github.com/DefinitelyTyped/DefinitelyTyped/issues/18051\n         */\n        return jsx(Fragment, { children: children });\n    }\n    return (jsx(ReactFlowProvider, { initialNodes: nodes, initialEdges: edges, defaultNodes: defaultNodes, defaultEdges: defaultEdges, initialWidth: width, initialHeight: height, fitView: fitView, initialFitViewOptions: fitViewOptions, initialMinZoom: minZoom, initialMaxZoom: maxZoom, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent, children: children }));\n}\n\nconst wrapperStyle = {\n    width: '100%',\n    height: '100%',\n    overflow: 'hidden',\n    position: 'relative',\n    zIndex: 0,\n};\nfunction ReactFlow({ nodes, edges, defaultNodes, defaultEdges, className, nodeTypes, edgeTypes, onNodeClick, onEdgeClick, onInit, onMove, onMoveStart, onMoveEnd, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onNodeDoubleClick, onNodeDragStart, onNodeDrag, onNodeDragStop, onNodesDelete, onEdgesDelete, onDelete, onSelectionChange, onSelectionDragStart, onSelectionDrag, onSelectionDragStop, onSelectionContextMenu, onSelectionStart, onSelectionEnd, onBeforeDelete, connectionMode, connectionLineType = ConnectionLineType.Bezier, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, deleteKeyCode = 'Backspace', selectionKeyCode = 'Shift', selectionOnDrag = false, selectionMode = SelectionMode.Full, panActivationKeyCode = 'Space', multiSelectionKeyCode = isMacOs() ? 'Meta' : 'Control', zoomActivationKeyCode = isMacOs() ? 'Meta' : 'Control', snapToGrid, snapGrid, onlyRenderVisibleElements = false, selectNodesOnDrag, nodesDraggable, autoPanOnNodeFocus, nodesConnectable, nodesFocusable, nodeOrigin = defaultNodeOrigin, edgesFocusable, edgesReconnectable, elementsSelectable = true, defaultViewport: defaultViewport$1 = defaultViewport, minZoom = 0.5, maxZoom = 2, translateExtent = infiniteExtent, preventScrolling = true, nodeExtent, defaultMarkerColor = '#b1b1b7', zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, paneClickDistance = 0, nodeClickDistance = 0, children, onReconnect, onReconnectStart, onReconnectEnd, onEdgeContextMenu, onEdgeDoubleClick, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, reconnectRadius = 10, onNodesChange, onEdgesChange, noDragClassName = 'nodrag', noWheelClassName = 'nowheel', noPanClassName = 'nopan', fitView, fitViewOptions, connectOnClick, attributionPosition, proOptions, defaultEdgeOptions, elevateNodesOnSelect, elevateEdgesOnSelect, disableKeyboardA11y = false, autoPanOnConnect, autoPanOnNodeDrag, autoPanSpeed, connectionRadius, isValidConnection, onError, style, id, nodeDragThreshold, viewport, onViewportChange, width, height, colorMode = 'light', debug, onScroll, ariaLabelConfig, ...rest }, ref) {\n    const rfId = id || '1';\n    const colorModeClassName = useColorModeClass(colorMode);\n    // Undo scroll events, preventing viewport from shifting when nodes outside of it are focused\n    const wrapperOnScroll = useCallback((e) => {\n        e.currentTarget.scrollTo({ top: 0, left: 0, behavior: 'instant' });\n        onScroll?.(e);\n    }, [onScroll]);\n    return (jsx(\"div\", { \"data-testid\": \"rf__wrapper\", ...rest, onScroll: wrapperOnScroll, style: { ...style, ...wrapperStyle }, ref: ref, className: cc(['react-flow', className, colorModeClassName]), id: id, role: \"application\", children: jsxs(Wrapper, { nodes: nodes, edges: edges, width: width, height: height, fitView: fitView, fitViewOptions: fitViewOptions, minZoom: minZoom, maxZoom: maxZoom, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent, children: [jsx(GraphView, { onInit: onInit, onNodeClick: onNodeClick, onEdgeClick: onEdgeClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, onNodeDoubleClick: onNodeDoubleClick, nodeTypes: nodeTypes, edgeTypes: edgeTypes, connectionLineType: connectionLineType, connectionLineStyle: connectionLineStyle, connectionLineComponent: connectionLineComponent, connectionLineContainerStyle: connectionLineContainerStyle, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, deleteKeyCode: deleteKeyCode, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, onlyRenderVisibleElements: onlyRenderVisibleElements, defaultViewport: defaultViewport$1, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, preventScrolling: preventScrolling, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneScroll: onPaneScroll, onPaneContextMenu: onPaneContextMenu, paneClickDistance: paneClickDistance, nodeClickDistance: nodeClickDistance, onSelectionContextMenu: onSelectionContextMenu, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, onEdgeContextMenu: onEdgeContextMenu, onEdgeDoubleClick: onEdgeDoubleClick, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, reconnectRadius: reconnectRadius, defaultMarkerColor: defaultMarkerColor, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, rfId: rfId, disableKeyboardA11y: disableKeyboardA11y, nodeExtent: nodeExtent, viewport: viewport, onViewportChange: onViewportChange }), jsx(StoreUpdater, { nodes: nodes, edges: edges, defaultNodes: defaultNodes, defaultEdges: defaultEdges, onConnect: onConnect, onConnectStart: onConnectStart, onConnectEnd: onConnectEnd, onClickConnectStart: onClickConnectStart, onClickConnectEnd: onClickConnectEnd, nodesDraggable: nodesDraggable, autoPanOnNodeFocus: autoPanOnNodeFocus, nodesConnectable: nodesConnectable, nodesFocusable: nodesFocusable, edgesFocusable: edgesFocusable, edgesReconnectable: edgesReconnectable, elementsSelectable: elementsSelectable, elevateNodesOnSelect: elevateNodesOnSelect, elevateEdgesOnSelect: elevateEdgesOnSelect, minZoom: minZoom, maxZoom: maxZoom, nodeExtent: nodeExtent, onNodesChange: onNodesChange, onEdgesChange: onEdgesChange, snapToGrid: snapToGrid, snapGrid: snapGrid, connectionMode: connectionMode, translateExtent: translateExtent, connectOnClick: connectOnClick, defaultEdgeOptions: defaultEdgeOptions, fitView: fitView, fitViewOptions: fitViewOptions, onNodesDelete: onNodesDelete, onEdgesDelete: onEdgesDelete, onDelete: onDelete, onNodeDragStart: onNodeDragStart, onNodeDrag: onNodeDrag, onNodeDragStop: onNodeDragStop, onSelectionDrag: onSelectionDrag, onSelectionDragStart: onSelectionDragStart, onSelectionDragStop: onSelectionDragStop, onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, noPanClassName: noPanClassName, nodeOrigin: nodeOrigin, rfId: rfId, autoPanOnConnect: autoPanOnConnect, autoPanOnNodeDrag: autoPanOnNodeDrag, autoPanSpeed: autoPanSpeed, onError: onError, connectionRadius: connectionRadius, isValidConnection: isValidConnection, selectNodesOnDrag: selectNodesOnDrag, nodeDragThreshold: nodeDragThreshold, onBeforeDelete: onBeforeDelete, paneClickDistance: paneClickDistance, debug: debug, ariaLabelConfig: ariaLabelConfig }), jsx(SelectionListener, { onSelectionChange: onSelectionChange }), children, jsx(Attribution, { proOptions: proOptions, position: attributionPosition }), jsx(A11yDescriptions, { rfId: rfId, disableKeyboardA11y: disableKeyboardA11y })] }) }));\n}\n/**\n * The `<ReactFlow />` component is the heart of your React Flow application.\n * It renders your nodes and edges and handles user interaction\n *\n * @public\n *\n * @example\n * ```tsx\n *import { ReactFlow } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (<ReactFlow\n *    nodes={...}\n *    edges={...}\n *    onNodesChange={...}\n *    ...\n *  />);\n *}\n *```\n */\nvar index = fixedForwardRef(ReactFlow);\n\nconst selector$6 = (s) => s.domNode?.querySelector('.react-flow__edgelabel-renderer');\n/**\n * Edges are SVG-based. If you want to render more complex labels you can use the\n * `<EdgeLabelRenderer />` component to access a div based renderer. This component\n * is a portal that renders the label in a `<div />` that is positioned on top of\n * the edges. You can see an example usage of the component in the\n * [edge label renderer example](/examples/edges/edge-label-renderer).\n * @public\n *\n * @example\n * ```jsx\n * import React from 'react';\n * import { getBezierPath, EdgeLabelRenderer, BaseEdge } from '@xyflow/react';\n *\n * export function CustomEdge({ id, data, ...props }) {\n *   const [edgePath, labelX, labelY] = getBezierPath(props);\n *\n *   return (\n *     <>\n *       <BaseEdge id={id} path={edgePath} />\n *       <EdgeLabelRenderer>\n *         <div\n *           style={{\n *             position: 'absolute',\n *             transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,\n *             background: '#ffcc00',\n *             padding: 10,\n *         }}\n *           className=\"nodrag nopan\"\n *         >\n *          {data.label}\n *         </div>\n *       </EdgeLabelRenderer>\n *     </>\n *   );\n * };\n * ```\n *\n * @remarks The `<EdgeLabelRenderer />` has no pointer events by default. If you want to\n * add mouse interactions you need to set the style `pointerEvents: all` and add\n * the `nopan` class on the label or the element you want to interact with.\n */\nfunction EdgeLabelRenderer({ children }) {\n    const edgeLabelRenderer = useStore(selector$6);\n    if (!edgeLabelRenderer) {\n        return null;\n    }\n    return createPortal(children, edgeLabelRenderer);\n}\n\nconst selector$5 = (s) => s.domNode?.querySelector('.react-flow__viewport-portal');\n/**\n * The `<ViewportPortal />` component can be used to add components to the same viewport\n * of the flow where nodes and edges are rendered. This is useful when you want to render\n * your own components that are adhere to the same coordinate system as the nodes & edges\n * and are also affected by zooming and panning\n * @public\n * @example\n *\n * ```jsx\n *import React from 'react';\n *import { ViewportPortal } from '@xyflow/react';\n *\n *export default function () {\n *  return (\n *    <ViewportPortal>\n *      <div\n *        style={{ transform: 'translate(100px, 100px)', position: 'absolute' }}\n *      >\n *        This div is positioned at [100, 100] on the flow.\n *      </div>\n *    </ViewportPortal>\n *  );\n *}\n *```\n */\nfunction ViewportPortal({ children }) {\n    const viewPortalDiv = useStore(selector$5);\n    if (!viewPortalDiv) {\n        return null;\n    }\n    return createPortal(children, viewPortalDiv);\n}\n\n/**\n * When you programmatically add or remove handles to a node or update a node's\n * handle position, you need to let React Flow know about it using this hook. This\n * will update the internal dimensions of the node and properly reposition handles\n * on the canvas if necessary.\n *\n * @public\n * @returns Use this function to tell React Flow to update the internal state of one or more nodes\n * that you have changed programmatically.\n *\n * @example\n * ```jsx\n *import { useCallback, useState } from 'react';\n *import { Handle, useUpdateNodeInternals } from '@xyflow/react';\n *\n *export default function RandomHandleNode({ id }) {\n *  const updateNodeInternals = useUpdateNodeInternals();\n *  const [handleCount, setHandleCount] = useState(0);\n *  const randomizeHandleCount = useCallback(() => {\n *   setHandleCount(Math.floor(Math.random() * 10));\n *    updateNodeInternals(id);\n *  }, [id, updateNodeInternals]);\n *\n *  return (\n *    <>\n *      {Array.from({ length: handleCount }).map((_, index) => (\n *        <Handle\n *          key={index}\n *          type=\"target\"\n *          position=\"left\"\n *          id={`handle-${index}`}\n *        />\n *      ))}\n *\n *      <div>\n *        <button onClick={randomizeHandleCount}>Randomize handle count</button>\n *        <p>There are {handleCount} handles on this node.</p>\n *      </div>\n *    </>\n *  );\n *}\n *```\n * @remarks This hook can only be used in a component that is a child of a\n *{@link ReactFlowProvider} or a {@link ReactFlow} component.\n */\nfunction useUpdateNodeInternals() {\n    const store = useStoreApi();\n    return useCallback((id) => {\n        const { domNode, updateNodeInternals } = store.getState();\n        const updateIds = Array.isArray(id) ? id : [id];\n        const updates = new Map();\n        updateIds.forEach((updateId) => {\n            const nodeElement = domNode?.querySelector(`.react-flow__node[data-id=\"${updateId}\"]`);\n            if (nodeElement) {\n                updates.set(updateId, { id: updateId, nodeElement, force: true });\n            }\n        });\n        requestAnimationFrame(() => updateNodeInternals(updates, { triggerFitView: false }));\n    }, []);\n}\n\nconst nodesSelector = (state) => state.nodes;\n/**\n * This hook returns an array of the current nodes. Components that use this hook\n * will re-render **whenever any node changes**, including when a node is selected\n * or moved.\n *\n * @public\n * @returns An array of all nodes currently in the flow.\n *\n * @example\n * ```jsx\n *import { useNodes } from '@xyflow/react';\n *\n *export default function() {\n *  const nodes = useNodes();\n *\n *  return <div>There are currently {nodes.length} nodes!</div>;\n *}\n *```\n */\nfunction useNodes() {\n    const nodes = useStore(nodesSelector, shallow);\n    return nodes;\n}\n\nconst edgesSelector = (state) => state.edges;\n/**\n * This hook returns an array of the current edges. Components that use this hook\n * will re-render **whenever any edge changes**.\n *\n * @public\n * @returns An array of all edges currently in the flow.\n *\n * @example\n * ```tsx\n *import { useEdges } from '@xyflow/react';\n *\n *export default function () {\n *  const edges = useEdges();\n *\n *  return <div>There are currently {edges.length} edges!</div>;\n *}\n *```\n */\nfunction useEdges() {\n    const edges = useStore(edgesSelector, shallow);\n    return edges;\n}\n\nconst viewportSelector = (state) => ({\n    x: state.transform[0],\n    y: state.transform[1],\n    zoom: state.transform[2],\n});\n/**\n * The `useViewport` hook is a convenient way to read the current state of the\n * {@link Viewport} in a component. Components that use this hook\n * will re-render **whenever the viewport changes**.\n *\n * @public\n * @returns The current viewport.\n *\n * @example\n *\n *```jsx\n *import { useViewport } from '@xyflow/react';\n *\n *export default function ViewportDisplay() {\n *  const { x, y, zoom } = useViewport();\n *\n *  return (\n *    <div>\n *      <p>\n *        The viewport is currently at ({x}, {y}) and zoomed to {zoom}.\n *      </p>\n *    </div>\n *  );\n *}\n *```\n *\n * @remarks This hook can only be used in a component that is a child of a\n *{@link ReactFlowProvider} or a {@link ReactFlow} component.\n */\nfunction useViewport() {\n    const viewport = useStore(viewportSelector, shallow);\n    return viewport;\n}\n\n/**\n * This hook makes it easy to prototype a controlled flow where you manage the\n * state of nodes and edges outside the `ReactFlowInstance`. You can think of it\n * like React's `useState` hook with an additional helper callback.\n *\n * @public\n * @returns\n * - `nodes`: The current array of nodes. You might pass this directly to the `nodes` prop of your\n * `<ReactFlow />` component, or you may want to manipulate it first to perform some layouting,\n * for example.\n * - `setNodes`: A function that you can use to update the nodes. You can pass it a new array of\n * nodes or a callback that receives the current array of nodes and returns a new array of nodes.\n * This is the same as the second element of the tuple returned by React's `useState` hook.\n * - `onNodesChange`: A handy callback that can take an array of `NodeChanges` and update the nodes\n * state accordingly. You'll typically pass this directly to the `onNodesChange` prop of your\n * `<ReactFlow />` component.\n * @example\n *\n *```tsx\n *import { ReactFlow, useNodesState, useEdgesState } from '@xyflow/react';\n *\n *const initialNodes = [];\n *const initialEdges = [];\n *\n *export default function () {\n *  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n *  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n *\n *  return (\n *    <ReactFlow\n *      nodes={nodes}\n *      edges={edges}\n *      onNodesChange={onNodesChange}\n *      onEdgesChange={onEdgesChange}\n *    />\n *  );\n *}\n *```\n *\n * @remarks This hook was created to make prototyping easier and our documentation\n * examples clearer. Although it is OK to use this hook in production, in\n * practice you may want to use a more sophisticated state management solution\n * like Zustand {@link https://reactflow.dev/docs/guides/state-management/} instead.\n *\n */\nfunction useNodesState(initialNodes) {\n    const [nodes, setNodes] = useState(initialNodes);\n    const onNodesChange = useCallback((changes) => setNodes((nds) => applyNodeChanges(changes, nds)), []);\n    return [nodes, setNodes, onNodesChange];\n}\n/**\n * This hook makes it easy to prototype a controlled flow where you manage the\n * state of nodes and edges outside the `ReactFlowInstance`. You can think of it\n * like React's `useState` hook with an additional helper callback.\n *\n * @public\n * @returns\n * - `edges`: The current array of edges. You might pass this directly to the `edges` prop of your\n * `<ReactFlow />` component, or you may want to manipulate it first to perform some layouting,\n * for example.\n *\n * - `setEdges`: A function that you can use to update the edges. You can pass it a new array of\n * edges or a callback that receives the current array of edges and returns a new array of edges.\n * This is the same as the second element of the tuple returned by React's `useState` hook.\n *\n * - `onEdgesChange`: A handy callback that can take an array of `EdgeChanges` and update the edges\n * state accordingly. You'll typically pass this directly to the `onEdgesChange` prop of your\n * `<ReactFlow />` component.\n * @example\n *\n *```tsx\n *import { ReactFlow, useNodesState, useEdgesState } from '@xyflow/react';\n *\n *const initialNodes = [];\n *const initialEdges = [];\n *\n *export default function () {\n *  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n *  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n *\n *  return (\n *    <ReactFlow\n *      nodes={nodes}\n *      edges={edges}\n *      onNodesChange={onNodesChange}\n *      onEdgesChange={onEdgesChange}\n *    />\n *  );\n *}\n *```\n *\n * @remarks This hook was created to make prototyping easier and our documentation\n * examples clearer. Although it is OK to use this hook in production, in\n * practice you may want to use a more sophisticated state management solution\n * like Zustand {@link https://reactflow.dev/docs/guides/state-management/} instead.\n *\n */\nfunction useEdgesState(initialEdges) {\n    const [edges, setEdges] = useState(initialEdges);\n    const onEdgesChange = useCallback((changes) => setEdges((eds) => applyEdgeChanges(changes, eds)), []);\n    return [edges, setEdges, onEdgesChange];\n}\n\n/**\n * The `useOnViewportChange` hook lets you listen for changes to the viewport such\n * as panning and zooming. You can provide a callback for each phase of a viewport\n * change: `onStart`, `onChange`, and `onEnd`.\n *\n * @public\n * @example\n * ```jsx\n *import { useCallback } from 'react';\n *import { useOnViewportChange } from '@xyflow/react';\n *\n *function ViewportChangeLogger() {\n *  useOnViewportChange({\n *    onStart: (viewport: Viewport) => console.log('start', viewport),\n *    onChange: (viewport: Viewport) => console.log('change', viewport),\n *    onEnd: (viewport: Viewport) => console.log('end', viewport),\n *  });\n *\n *  return null;\n *}\n *```\n */\nfunction useOnViewportChange({ onStart, onChange, onEnd }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        store.setState({ onViewportChangeStart: onStart });\n    }, [onStart]);\n    useEffect(() => {\n        store.setState({ onViewportChange: onChange });\n    }, [onChange]);\n    useEffect(() => {\n        store.setState({ onViewportChangeEnd: onEnd });\n    }, [onEnd]);\n}\n\n/**\n * This hook lets you listen for changes to both node and edge selection. As the\n *name implies, the callback you provide will be called whenever the selection of\n *_either_ nodes or edges changes.\n *\n * @public\n * @example\n * ```jsx\n *import { useState } from 'react';\n *import { ReactFlow, useOnSelectionChange } from '@xyflow/react';\n *\n *function SelectionDisplay() {\n *  const [selectedNodes, setSelectedNodes] = useState([]);\n *  const [selectedEdges, setSelectedEdges] = useState([]);\n *\n *  // the passed handler has to be memoized, otherwise the hook will not work correctly\n *  const onChange = useCallback(({ nodes, edges }) => {\n *    setSelectedNodes(nodes.map((node) => node.id));\n *    setSelectedEdges(edges.map((edge) => edge.id));\n *  }, []);\n *\n *  useOnSelectionChange({\n *    onChange,\n *  });\n *\n *  return (\n *    <div>\n *      <p>Selected nodes: {selectedNodes.join(', ')}</p>\n *      <p>Selected edges: {selectedEdges.join(', ')}</p>\n *    </div>\n *  );\n *}\n *```\n *\n * @remarks You need to memoize the passed `onChange` handler, otherwise the hook will not work correctly.\n */\nfunction useOnSelectionChange({ onChange, }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        const nextOnSelectionChangeHandlers = [...store.getState().onSelectionChangeHandlers, onChange];\n        store.setState({ onSelectionChangeHandlers: nextOnSelectionChangeHandlers });\n        return () => {\n            const nextHandlers = store.getState().onSelectionChangeHandlers.filter((fn) => fn !== onChange);\n            store.setState({ onSelectionChangeHandlers: nextHandlers });\n        };\n    }, [onChange]);\n}\n\nconst selector$4 = (options) => (s) => {\n    if (!options.includeHiddenNodes) {\n        return s.nodesInitialized;\n    }\n    if (s.nodeLookup.size === 0) {\n        return false;\n    }\n    for (const [, { internals }] of s.nodeLookup) {\n        if (internals.handleBounds === undefined || !nodeHasDimensions(internals.userNode)) {\n            return false;\n        }\n    }\n    return true;\n};\n/**\n * This hook tells you whether all the nodes in a flow have been measured and given\n *a width and height. When you add a node to the flow, this hook will return\n *`false` and then `true` again once the node has been measured.\n *\n * @public\n * @returns Whether or not the nodes have been initialized by the `<ReactFlow />` component and\n * given a width and height.\n *\n * @example\n * ```jsx\n *import { useReactFlow, useNodesInitialized } from '@xyflow/react';\n *import { useEffect, useState } from 'react';\n *\n *const options = {\n *  includeHiddenNodes: false,\n *};\n *\n *export default function useLayout() {\n *  const { getNodes } = useReactFlow();\n *  const nodesInitialized = useNodesInitialized(options);\n *  const [layoutedNodes, setLayoutedNodes] = useState(getNodes());\n *\n *  useEffect(() => {\n *    if (nodesInitialized) {\n *      setLayoutedNodes(yourLayoutingFunction(getNodes()));\n *    }\n *  }, [nodesInitialized]);\n *\n *  return layoutedNodes;\n *}\n *```\n */\nfunction useNodesInitialized(options = {\n    includeHiddenNodes: false,\n}) {\n    const initialized = useStore(selector$4(options));\n    return initialized;\n}\n\n/**\n * Hook to check if a <Handle /> is connected to another <Handle /> and get the connections.\n *\n * @public\n * @deprecated Use `useNodeConnections` instead.\n * @returns An array with handle connections.\n */\nfunction useHandleConnections({ type, id, nodeId, onConnect, onDisconnect, }) {\n    console.warn('[DEPRECATED] `useHandleConnections` is deprecated. Instead use `useNodeConnections` https://reactflow.dev/api-reference/hooks/useNodeConnections');\n    const _nodeId = useNodeId();\n    const currentNodeId = nodeId ?? _nodeId;\n    const prevConnections = useRef(null);\n    const connections = useStore((state) => state.connectionLookup.get(`${currentNodeId}-${type}${id ? `-${id}` : ''}`), areConnectionMapsEqual);\n    useEffect(() => {\n        // @todo dicuss if onConnect/onDisconnect should be called when the component mounts/unmounts\n        if (prevConnections.current && prevConnections.current !== connections) {\n            const _connections = connections ?? new Map();\n            handleConnectionChange(prevConnections.current, _connections, onDisconnect);\n            handleConnectionChange(_connections, prevConnections.current, onConnect);\n        }\n        prevConnections.current = connections ?? new Map();\n    }, [connections, onConnect, onDisconnect]);\n    return useMemo(() => Array.from(connections?.values() ?? []), [connections]);\n}\n\nconst error014 = errorMessages['error014']();\n/**\n * This hook returns an array of connections on a specific node, handle type ('source', 'target') or handle ID.\n *\n * @public\n * @returns An array with connections.\n *\n * @example\n * ```jsx\n *import { useNodeConnections } from '@xyflow/react';\n *\n *export default function () {\n *  const connections = useNodeConnections({\n *    handleType: 'target',\n *    handleId: 'my-handle',\n *  });\n *\n *  return (\n *    <div>There are currently {connections.length} incoming connections!</div>\n *  );\n *}\n *```\n */\nfunction useNodeConnections({ id, handleType, handleId, onConnect, onDisconnect, } = {}) {\n    const nodeId = useNodeId();\n    const currentNodeId = id ?? nodeId;\n    if (!currentNodeId) {\n        throw new Error(error014);\n    }\n    const prevConnections = useRef(null);\n    const connections = useStore((state) => state.connectionLookup.get(`${currentNodeId}${handleType ? (handleId ? `-${handleType}-${handleId}` : `-${handleType}`) : ''}`), areConnectionMapsEqual);\n    useEffect(() => {\n        // @todo discuss if onConnect/onDisconnect should be called when the component mounts/unmounts\n        if (prevConnections.current && prevConnections.current !== connections) {\n            const _connections = connections ?? new Map();\n            handleConnectionChange(prevConnections.current, _connections, onDisconnect);\n            handleConnectionChange(_connections, prevConnections.current, onConnect);\n        }\n        prevConnections.current = connections ?? new Map();\n    }, [connections, onConnect, onDisconnect]);\n    return useMemo(() => Array.from(connections?.values() ?? []), [connections]);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useNodesData(nodeIds) {\n    const nodesData = useStore(useCallback((s) => {\n        const data = [];\n        const isArrayOfIds = Array.isArray(nodeIds);\n        const _nodeIds = isArrayOfIds ? nodeIds : [nodeIds];\n        for (const nodeId of _nodeIds) {\n            const node = s.nodeLookup.get(nodeId);\n            if (node) {\n                data.push({\n                    id: node.id,\n                    type: node.type,\n                    data: node.data,\n                });\n            }\n        }\n        return isArrayOfIds ? data : data[0] ?? null;\n    }, [nodeIds]), shallowNodeData);\n    return nodesData;\n}\n\n/**\n * This hook returns the internal representation of a specific node.\n * Components that use this hook will re-render **whenever the node changes**,\n * including when a node is selected or moved.\n *\n * @public\n * @param id - The ID of a node you want to observe.\n * @returns The `InternalNode` object for the node with the given ID.\n *\n * @example\n * ```tsx\n *import { useInternalNode } from '@xyflow/react';\n *\n *export default function () {\n *  const internalNode = useInternalNode('node-1');\n *  const absolutePosition = internalNode.internals.positionAbsolute;\n *\n *  return (\n *    <div>\n *      The absolute position of the node is at:\n *      <p>x: {absolutePosition.x}</p>\n *      <p>y: {absolutePosition.y}</p>\n *    </div>\n *  );\n *}\n *```\n */\nfunction useInternalNode(id) {\n    const node = useStore(useCallback((s) => s.nodeLookup.get(id), [id]), shallow);\n    return node;\n}\n\nfunction LinePattern({ dimensions, lineWidth, variant, className }) {\n    return (jsx(\"path\", { strokeWidth: lineWidth, d: `M${dimensions[0] / 2} 0 V${dimensions[1]} M0 ${dimensions[1] / 2} H${dimensions[0]}`, className: cc(['react-flow__background-pattern', variant, className]) }));\n}\nfunction DotPattern({ radius, className }) {\n    return (jsx(\"circle\", { cx: radius, cy: radius, r: radius, className: cc(['react-flow__background-pattern', 'dots', className]) }));\n}\n\n/**\n * The three variants are exported as an enum for convenience. You can either import\n * the enum and use it like `BackgroundVariant.Lines` or you can use the raw string\n * value directly.\n * @public\n */\nvar BackgroundVariant;\n(function (BackgroundVariant) {\n    BackgroundVariant[\"Lines\"] = \"lines\";\n    BackgroundVariant[\"Dots\"] = \"dots\";\n    BackgroundVariant[\"Cross\"] = \"cross\";\n})(BackgroundVariant || (BackgroundVariant = {}));\n\nconst defaultSize = {\n    [BackgroundVariant.Dots]: 1,\n    [BackgroundVariant.Lines]: 1,\n    [BackgroundVariant.Cross]: 6,\n};\nconst selector$3 = (s) => ({ transform: s.transform, patternId: `pattern-${s.rfId}` });\nfunction BackgroundComponent({ id, variant = BackgroundVariant.Dots, \n// only used for dots and cross\ngap = 20, \n// only used for lines and cross\nsize, lineWidth = 1, offset = 0, color, bgColor, style, className, patternClassName, }) {\n    const ref = useRef(null);\n    const { transform, patternId } = useStore(selector$3, shallow);\n    const patternSize = size || defaultSize[variant];\n    const isDots = variant === BackgroundVariant.Dots;\n    const isCross = variant === BackgroundVariant.Cross;\n    const gapXY = Array.isArray(gap) ? gap : [gap, gap];\n    const scaledGap = [gapXY[0] * transform[2] || 1, gapXY[1] * transform[2] || 1];\n    const scaledSize = patternSize * transform[2];\n    const offsetXY = Array.isArray(offset) ? offset : [offset, offset];\n    const patternDimensions = isCross ? [scaledSize, scaledSize] : scaledGap;\n    const scaledOffset = [\n        offsetXY[0] * transform[2] || 1 + patternDimensions[0] / 2,\n        offsetXY[1] * transform[2] || 1 + patternDimensions[1] / 2,\n    ];\n    const _patternId = `${patternId}${id ? id : ''}`;\n    return (jsxs(\"svg\", { className: cc(['react-flow__background', className]), style: {\n            ...style,\n            ...containerStyle,\n            '--xy-background-color-props': bgColor,\n            '--xy-background-pattern-color-props': color,\n        }, ref: ref, \"data-testid\": \"rf__background\", children: [jsx(\"pattern\", { id: _patternId, x: transform[0] % scaledGap[0], y: transform[1] % scaledGap[1], width: scaledGap[0], height: scaledGap[1], patternUnits: \"userSpaceOnUse\", patternTransform: `translate(-${scaledOffset[0]},-${scaledOffset[1]})`, children: isDots ? (jsx(DotPattern, { radius: scaledSize / 2, className: patternClassName })) : (jsx(LinePattern, { dimensions: patternDimensions, lineWidth: lineWidth, variant: variant, className: patternClassName })) }), jsx(\"rect\", { x: \"0\", y: \"0\", width: \"100%\", height: \"100%\", fill: `url(#${_patternId})` })] }));\n}\nBackgroundComponent.displayName = 'Background';\n/**\n * The `<Background />` component makes it convenient to render different types of backgrounds common in node-based UIs. It comes with three variants: lines, dots and cross.\n *\n * @example\n *\n * A simple example of how to use the Background component.\n *\n * ```tsx\n * import { useState } from 'react';\n * import { ReactFlow, Background, BackgroundVariant } from '@xyflow/react';\n *\n * export default function Flow() {\n *   return (\n *     <ReactFlow defaultNodes={[...]} defaultEdges={[...]}>\n *       <Background color=\"#ccc\" variant={BackgroundVariant.Dots} />\n *     </ReactFlow>\n *   );\n * }\n * ```\n *\n * @example\n *\n * In this example you can see how to combine multiple backgrounds\n *\n * ```tsx\n * import { ReactFlow, Background, BackgroundVariant } from '@xyflow/react';\n * import '@xyflow/react/dist/style.css';\n *\n * export default function Flow() {\n *   return (\n *     <ReactFlow defaultNodes={[...]} defaultEdges={[...]}>\n *       <Background\n *         id=\"1\"\n *         gap={10}\n *         color=\"#f1f1f1\"\n *         variant={BackgroundVariant.Lines}\n *       />\n *       <Background\n *         id=\"2\"\n *         gap={100}\n *         color=\"#ccc\"\n *         variant={BackgroundVariant.Lines}\n *       />\n *     </ReactFlow>\n *   );\n * }\n * ```\n *\n * @remarks\n *\n * When combining multiple <Background /> components it’s important to give each of them a unique id prop!\n *\n */\nconst Background = memo(BackgroundComponent);\n\nfunction PlusIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 32\", children: jsx(\"path\", { d: \"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z\" }) }));\n}\n\nfunction MinusIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 5\", children: jsx(\"path\", { d: \"M0 0h32v4.2H0z\" }) }));\n}\n\nfunction FitViewIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 30\", children: jsx(\"path\", { d: \"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z\" }) }));\n}\n\nfunction LockIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 25 32\", children: jsx(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z\" }) }));\n}\n\nfunction UnlockIcon() {\n    return (jsx(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 25 32\", children: jsx(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z\" }) }));\n}\n\n/**\n * You can add buttons to the control panel by using the `<ControlButton />` component\n * and pass it as a child to the [`<Controls />`](/api-reference/components/controls) component.\n *\n * @public\n * @example\n *```jsx\n *import { MagicWand } from '@radix-ui/react-icons'\n *import { ReactFlow, Controls, ControlButton } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]} edges={[...]}>\n *      <Controls>\n *        <ControlButton onClick={() => alert('Something magical just happened. ✨')}>\n *          <MagicWand />\n *        </ControlButton>\n *      </Controls>\n *    </ReactFlow>\n *  )\n *}\n *```\n */\nfunction ControlButton({ children, className, ...rest }) {\n    return (jsx(\"button\", { type: \"button\", className: cc(['react-flow__controls-button', className]), ...rest, children: children }));\n}\n\nconst selector$2 = (s) => ({\n    isInteractive: s.nodesDraggable || s.nodesConnectable || s.elementsSelectable,\n    minZoomReached: s.transform[2] <= s.minZoom,\n    maxZoomReached: s.transform[2] >= s.maxZoom,\n    ariaLabelConfig: s.ariaLabelConfig,\n});\nfunction ControlsComponent({ style, showZoom = true, showFitView = true, showInteractive = true, fitViewOptions, onZoomIn, onZoomOut, onFitView, onInteractiveChange, className, children, position = 'bottom-left', orientation = 'vertical', 'aria-label': ariaLabel, }) {\n    const store = useStoreApi();\n    const { isInteractive, minZoomReached, maxZoomReached, ariaLabelConfig } = useStore(selector$2, shallow);\n    const { zoomIn, zoomOut, fitView } = useReactFlow();\n    const onZoomInHandler = () => {\n        zoomIn();\n        onZoomIn?.();\n    };\n    const onZoomOutHandler = () => {\n        zoomOut();\n        onZoomOut?.();\n    };\n    const onFitViewHandler = () => {\n        fitView(fitViewOptions);\n        onFitView?.();\n    };\n    const onToggleInteractivity = () => {\n        store.setState({\n            nodesDraggable: !isInteractive,\n            nodesConnectable: !isInteractive,\n            elementsSelectable: !isInteractive,\n        });\n        onInteractiveChange?.(!isInteractive);\n    };\n    const orientationClass = orientation === 'horizontal' ? 'horizontal' : 'vertical';\n    return (jsxs(Panel, { className: cc(['react-flow__controls', orientationClass, className]), position: position, style: style, \"data-testid\": \"rf__controls\", \"aria-label\": ariaLabel ?? ariaLabelConfig['controls.ariaLabel'], children: [showZoom && (jsxs(Fragment, { children: [jsx(ControlButton, { onClick: onZoomInHandler, className: \"react-flow__controls-zoomin\", title: ariaLabelConfig['controls.zoomIn.ariaLabel'], \"aria-label\": ariaLabelConfig['controls.zoomIn.ariaLabel'], disabled: maxZoomReached, children: jsx(PlusIcon, {}) }), jsx(ControlButton, { onClick: onZoomOutHandler, className: \"react-flow__controls-zoomout\", title: ariaLabelConfig['controls.zoomOut.ariaLabel'], \"aria-label\": ariaLabelConfig['controls.zoomOut.ariaLabel'], disabled: minZoomReached, children: jsx(MinusIcon, {}) })] })), showFitView && (jsx(ControlButton, { className: \"react-flow__controls-fitview\", onClick: onFitViewHandler, title: ariaLabelConfig['controls.fitView.ariaLabel'], \"aria-label\": ariaLabelConfig['controls.fitView.ariaLabel'], children: jsx(FitViewIcon, {}) })), showInteractive && (jsx(ControlButton, { className: \"react-flow__controls-interactive\", onClick: onToggleInteractivity, title: ariaLabelConfig['controls.interactive.ariaLabel'], \"aria-label\": ariaLabelConfig['controls.interactive.ariaLabel'], children: isInteractive ? jsx(UnlockIcon, {}) : jsx(LockIcon, {}) })), children] }));\n}\nControlsComponent.displayName = 'Controls';\n/**\n * The `<Controls />` component renders a small panel that contains convenient\n * buttons to zoom in, zoom out, fit the view, and lock the viewport.\n *\n * @public\n * @example\n *```tsx\n *import { ReactFlow, Controls } from '@xyflow/react'\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]} edges={[...]}>\n *      <Controls />\n *    </ReactFlow>\n *  )\n *}\n *```\n *\n * @remarks To extend or customise the controls, you can use the [`<ControlButton />`](/api-reference/components/control-button) component\n *\n */\nconst Controls = memo(ControlsComponent);\n\nfunction MiniMapNodeComponent({ id, x, y, width, height, style, color, strokeColor, strokeWidth, className, borderRadius, shapeRendering, selected, onClick, }) {\n    const { background, backgroundColor } = style || {};\n    const fill = (color || background || backgroundColor);\n    return (jsx(\"rect\", { className: cc(['react-flow__minimap-node', { selected }, className]), x: x, y: y, rx: borderRadius, ry: borderRadius, width: width, height: height, style: {\n            fill,\n            stroke: strokeColor,\n            strokeWidth,\n        }, shapeRendering: shapeRendering, onClick: onClick ? (event) => onClick(event, id) : undefined }));\n}\nconst MiniMapNode = memo(MiniMapNodeComponent);\n\nconst selectorNodeIds = (s) => s.nodes.map((node) => node.id);\nconst getAttrFunction = (func) => func instanceof Function ? func : () => func;\nfunction MiniMapNodes({ nodeStrokeColor, nodeColor, nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth, \n/*\n * We need to rename the prop to be `CapitalCase` so that JSX will render it as\n * a component properly.\n */\nnodeComponent: NodeComponent = MiniMapNode, onClick, }) {\n    const nodeIds = useStore(selectorNodeIds, shallow);\n    const nodeColorFunc = getAttrFunction(nodeColor);\n    const nodeStrokeColorFunc = getAttrFunction(nodeStrokeColor);\n    const nodeClassNameFunc = getAttrFunction(nodeClassName);\n    const shapeRendering = typeof window === 'undefined' || !!window.chrome ? 'crispEdges' : 'geometricPrecision';\n    return (jsx(Fragment, { children: nodeIds.map((nodeId) => (\n        /*\n         * The split of responsibilities between MiniMapNodes and\n         * NodeComponentWrapper may appear weird. However, it’s designed to\n         * minimize the cost of updates when individual nodes change.\n         *\n         * For more details, see a similar commit in `NodeRenderer/index.tsx`.\n         */\n        jsx(NodeComponentWrapper, { id: nodeId, nodeColorFunc: nodeColorFunc, nodeStrokeColorFunc: nodeStrokeColorFunc, nodeClassNameFunc: nodeClassNameFunc, nodeBorderRadius: nodeBorderRadius, nodeStrokeWidth: nodeStrokeWidth, NodeComponent: NodeComponent, onClick: onClick, shapeRendering: shapeRendering }, nodeId))) }));\n}\nfunction NodeComponentWrapperInner({ id, nodeColorFunc, nodeStrokeColorFunc, nodeClassNameFunc, nodeBorderRadius, nodeStrokeWidth, shapeRendering, NodeComponent, onClick, }) {\n    const { node, x, y, width, height } = useStore((s) => {\n        const { internals } = s.nodeLookup.get(id);\n        const node = internals.userNode;\n        const { x, y } = internals.positionAbsolute;\n        const { width, height } = getNodeDimensions(node);\n        return {\n            node,\n            x,\n            y,\n            width,\n            height,\n        };\n    }, shallow);\n    if (!node || node.hidden || !nodeHasDimensions(node)) {\n        return null;\n    }\n    return (jsx(NodeComponent, { x: x, y: y, width: width, height: height, style: node.style, selected: !!node.selected, className: nodeClassNameFunc(node), color: nodeColorFunc(node), borderRadius: nodeBorderRadius, strokeColor: nodeStrokeColorFunc(node), strokeWidth: nodeStrokeWidth, shapeRendering: shapeRendering, onClick: onClick, id: node.id }));\n}\nconst NodeComponentWrapper = memo(NodeComponentWrapperInner);\nvar MiniMapNodes$1 = memo(MiniMapNodes);\n\nconst defaultWidth = 200;\nconst defaultHeight = 150;\nconst filterHidden = (node) => !node.hidden;\nconst selector$1 = (s) => {\n    const viewBB = {\n        x: -s.transform[0] / s.transform[2],\n        y: -s.transform[1] / s.transform[2],\n        width: s.width / s.transform[2],\n        height: s.height / s.transform[2],\n    };\n    return {\n        viewBB,\n        boundingRect: s.nodeLookup.size > 0\n            ? getBoundsOfRects(getInternalNodesBounds(s.nodeLookup, { filter: filterHidden }), viewBB)\n            : viewBB,\n        rfId: s.rfId,\n        panZoom: s.panZoom,\n        translateExtent: s.translateExtent,\n        flowWidth: s.width,\n        flowHeight: s.height,\n        ariaLabelConfig: s.ariaLabelConfig,\n    };\n};\nconst ARIA_LABEL_KEY = 'react-flow__minimap-desc';\nfunction MiniMapComponent({ style, className, nodeStrokeColor, nodeColor, nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth, \n/*\n * We need to rename the prop to be `CapitalCase` so that JSX will render it as\n * a component properly.\n */\nnodeComponent, bgColor, maskColor, maskStrokeColor, maskStrokeWidth, position = 'bottom-right', onClick, onNodeClick, pannable = false, zoomable = false, ariaLabel, inversePan, zoomStep = 10, offsetScale = 5, }) {\n    const store = useStoreApi();\n    const svg = useRef(null);\n    const { boundingRect, viewBB, rfId, panZoom, translateExtent, flowWidth, flowHeight, ariaLabelConfig } = useStore(selector$1, shallow);\n    const elementWidth = style?.width ?? defaultWidth;\n    const elementHeight = style?.height ?? defaultHeight;\n    const scaledWidth = boundingRect.width / elementWidth;\n    const scaledHeight = boundingRect.height / elementHeight;\n    const viewScale = Math.max(scaledWidth, scaledHeight);\n    const viewWidth = viewScale * elementWidth;\n    const viewHeight = viewScale * elementHeight;\n    const offset = offsetScale * viewScale;\n    const x = boundingRect.x - (viewWidth - boundingRect.width) / 2 - offset;\n    const y = boundingRect.y - (viewHeight - boundingRect.height) / 2 - offset;\n    const width = viewWidth + offset * 2;\n    const height = viewHeight + offset * 2;\n    const labelledBy = `${ARIA_LABEL_KEY}-${rfId}`;\n    const viewScaleRef = useRef(0);\n    const minimapInstance = useRef();\n    viewScaleRef.current = viewScale;\n    useEffect(() => {\n        if (svg.current && panZoom) {\n            minimapInstance.current = XYMinimap({\n                domNode: svg.current,\n                panZoom,\n                getTransform: () => store.getState().transform,\n                getViewScale: () => viewScaleRef.current,\n            });\n            return () => {\n                minimapInstance.current?.destroy();\n            };\n        }\n    }, [panZoom]);\n    useEffect(() => {\n        minimapInstance.current?.update({\n            translateExtent,\n            width: flowWidth,\n            height: flowHeight,\n            inversePan,\n            pannable,\n            zoomStep,\n            zoomable,\n        });\n    }, [pannable, zoomable, inversePan, zoomStep, translateExtent, flowWidth, flowHeight]);\n    const onSvgClick = onClick\n        ? (event) => {\n            const [x, y] = minimapInstance.current?.pointer(event) || [0, 0];\n            onClick(event, { x, y });\n        }\n        : undefined;\n    const onSvgNodeClick = onNodeClick\n        ? useCallback((event, nodeId) => {\n            const node = store.getState().nodeLookup.get(nodeId).internals.userNode;\n            onNodeClick(event, node);\n        }, [])\n        : undefined;\n    const _ariaLabel = ariaLabel ?? ariaLabelConfig['minimap.ariaLabel'];\n    return (jsx(Panel, { position: position, style: {\n            ...style,\n            '--xy-minimap-background-color-props': typeof bgColor === 'string' ? bgColor : undefined,\n            '--xy-minimap-mask-background-color-props': typeof maskColor === 'string' ? maskColor : undefined,\n            '--xy-minimap-mask-stroke-color-props': typeof maskStrokeColor === 'string' ? maskStrokeColor : undefined,\n            '--xy-minimap-mask-stroke-width-props': typeof maskStrokeWidth === 'number' ? maskStrokeWidth * viewScale : undefined,\n            '--xy-minimap-node-background-color-props': typeof nodeColor === 'string' ? nodeColor : undefined,\n            '--xy-minimap-node-stroke-color-props': typeof nodeStrokeColor === 'string' ? nodeStrokeColor : undefined,\n            '--xy-minimap-node-stroke-width-props': typeof nodeStrokeWidth === 'number' ? nodeStrokeWidth : undefined,\n        }, className: cc(['react-flow__minimap', className]), \"data-testid\": \"rf__minimap\", children: jsxs(\"svg\", { width: elementWidth, height: elementHeight, viewBox: `${x} ${y} ${width} ${height}`, className: \"react-flow__minimap-svg\", role: \"img\", \"aria-labelledby\": labelledBy, ref: svg, onClick: onSvgClick, children: [_ariaLabel && jsx(\"title\", { id: labelledBy, children: _ariaLabel }), jsx(MiniMapNodes$1, { onClick: onSvgNodeClick, nodeColor: nodeColor, nodeStrokeColor: nodeStrokeColor, nodeBorderRadius: nodeBorderRadius, nodeClassName: nodeClassName, nodeStrokeWidth: nodeStrokeWidth, nodeComponent: nodeComponent }), jsx(\"path\", { className: \"react-flow__minimap-mask\", d: `M${x - offset},${y - offset}h${width + offset * 2}v${height + offset * 2}h${-width - offset * 2}z\n        M${viewBB.x},${viewBB.y}h${viewBB.width}v${viewBB.height}h${-viewBB.width}z`, fillRule: \"evenodd\", pointerEvents: \"none\" })] }) }));\n}\nMiniMapComponent.displayName = 'MiniMap';\n/**\n * The `<MiniMap />` component can be used to render an overview of your flow. It\n * renders each node as an SVG element and visualizes where the current viewport is\n * in relation to the rest of the flow.\n *\n * @public\n * @example\n *\n * ```jsx\n *import { ReactFlow, MiniMap } from '@xyflow/react';\n *\n *export default function Flow() {\n *  return (\n *    <ReactFlow nodes={[...]]} edges={[...]]}>\n *      <MiniMap nodeStrokeWidth={3} />\n *    </ReactFlow>\n *  );\n *}\n *```\n */\nconst MiniMap = memo(MiniMapComponent);\n\nconst scaleSelector = (calculateScale) => (store) => calculateScale ? `${Math.max(1 / store.transform[2], 1)}` : undefined;\nconst defaultPositions = {\n    [ResizeControlVariant.Line]: 'right',\n    [ResizeControlVariant.Handle]: 'bottom-right',\n};\nfunction ResizeControl({ nodeId, position, variant = ResizeControlVariant.Handle, className, style = undefined, children, color, minWidth = 10, minHeight = 10, maxWidth = Number.MAX_VALUE, maxHeight = Number.MAX_VALUE, keepAspectRatio = false, resizeDirection, autoScale = true, shouldResize, onResizeStart, onResize, onResizeEnd, }) {\n    const contextNodeId = useNodeId();\n    const id = typeof nodeId === 'string' ? nodeId : contextNodeId;\n    const store = useStoreApi();\n    const resizeControlRef = useRef(null);\n    const isHandleControl = variant === ResizeControlVariant.Handle;\n    const scale = useStore(useCallback(scaleSelector(isHandleControl && autoScale), [isHandleControl, autoScale]), shallow);\n    const resizer = useRef(null);\n    const controlPosition = position ?? defaultPositions[variant];\n    useEffect(() => {\n        if (!resizeControlRef.current || !id) {\n            return;\n        }\n        if (!resizer.current) {\n            resizer.current = XYResizer({\n                domNode: resizeControlRef.current,\n                nodeId: id,\n                getStoreItems: () => {\n                    const { nodeLookup, transform, snapGrid, snapToGrid, nodeOrigin, domNode } = store.getState();\n                    return {\n                        nodeLookup,\n                        transform,\n                        snapGrid,\n                        snapToGrid,\n                        nodeOrigin,\n                        paneDomNode: domNode,\n                    };\n                },\n                onChange: (change, childChanges) => {\n                    const { triggerNodeChanges, nodeLookup, parentLookup, nodeOrigin } = store.getState();\n                    const changes = [];\n                    const nextPosition = { x: change.x, y: change.y };\n                    const node = nodeLookup.get(id);\n                    if (node && node.expandParent && node.parentId) {\n                        const origin = node.origin ?? nodeOrigin;\n                        const width = change.width ?? node.measured.width ?? 0;\n                        const height = change.height ?? node.measured.height ?? 0;\n                        const child = {\n                            id: node.id,\n                            parentId: node.parentId,\n                            rect: {\n                                width,\n                                height,\n                                ...evaluateAbsolutePosition({\n                                    x: change.x ?? node.position.x,\n                                    y: change.y ?? node.position.y,\n                                }, { width, height }, node.parentId, nodeLookup, origin),\n                            },\n                        };\n                        const parentExpandChanges = handleExpandParent([child], nodeLookup, parentLookup, nodeOrigin);\n                        changes.push(...parentExpandChanges);\n                        /*\n                         * when the parent was expanded by the child node, its position will be clamped at\n                         * 0,0 when node origin is 0,0 and to width, height if it's 1,1\n                         */\n                        nextPosition.x = change.x ? Math.max(origin[0] * width, change.x) : undefined;\n                        nextPosition.y = change.y ? Math.max(origin[1] * height, change.y) : undefined;\n                    }\n                    if (nextPosition.x !== undefined && nextPosition.y !== undefined) {\n                        const positionChange = {\n                            id,\n                            type: 'position',\n                            position: { ...nextPosition },\n                        };\n                        changes.push(positionChange);\n                    }\n                    if (change.width !== undefined && change.height !== undefined) {\n                        const setAttributes = !resizeDirection ? true : resizeDirection === 'horizontal' ? 'width' : 'height';\n                        const dimensionChange = {\n                            id,\n                            type: 'dimensions',\n                            resizing: true,\n                            setAttributes,\n                            dimensions: {\n                                width: change.width,\n                                height: change.height,\n                            },\n                        };\n                        changes.push(dimensionChange);\n                    }\n                    for (const childChange of childChanges) {\n                        const positionChange = {\n                            ...childChange,\n                            type: 'position',\n                        };\n                        changes.push(positionChange);\n                    }\n                    triggerNodeChanges(changes);\n                },\n                onEnd: ({ width, height }) => {\n                    const dimensionChange = {\n                        id: id,\n                        type: 'dimensions',\n                        resizing: false,\n                        dimensions: {\n                            width,\n                            height,\n                        },\n                    };\n                    store.getState().triggerNodeChanges([dimensionChange]);\n                },\n            });\n        }\n        resizer.current.update({\n            controlPosition,\n            boundaries: {\n                minWidth,\n                minHeight,\n                maxWidth,\n                maxHeight,\n            },\n            keepAspectRatio,\n            resizeDirection,\n            onResizeStart,\n            onResize,\n            onResizeEnd,\n            shouldResize,\n        });\n        return () => {\n            resizer.current?.destroy();\n        };\n    }, [\n        controlPosition,\n        minWidth,\n        minHeight,\n        maxWidth,\n        maxHeight,\n        keepAspectRatio,\n        onResizeStart,\n        onResize,\n        onResizeEnd,\n        shouldResize,\n    ]);\n    const positionClassNames = controlPosition.split('-');\n    return (jsx(\"div\", { className: cc(['react-flow__resize-control', 'nodrag', ...positionClassNames, variant, className]), ref: resizeControlRef, style: {\n            ...style,\n            scale,\n            ...(color && { [isHandleControl ? 'backgroundColor' : 'borderColor']: color }),\n        }, children: children }));\n}\n/**\n * To create your own resizing UI, you can use the `NodeResizeControl` component where you can pass children (such as icons).\n * @public\n *\n */\nconst NodeResizeControl = memo(ResizeControl);\n\n/**\n * The `<NodeResizer />` component can be used to add a resize functionality to your\n * nodes. It renders draggable controls around the node to resize in all directions.\n * @public\n *\n * @example\n *```jsx\n *import { memo } from 'react';\n *import { Handle, Position, NodeResizer } from '@xyflow/react';\n *\n *function ResizableNode({ data }) {\n *  return (\n *    <>\n *      <NodeResizer minWidth={100} minHeight={30} />\n *      <Handle type=\"target\" position={Position.Left} />\n *      <div style={{ padding: 10 }}>{data.label}</div>\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *\n *export default memo(ResizableNode);\n *```\n */\nfunction NodeResizer({ nodeId, isVisible = true, handleClassName, handleStyle, lineClassName, lineStyle, color, minWidth = 10, minHeight = 10, maxWidth = Number.MAX_VALUE, maxHeight = Number.MAX_VALUE, keepAspectRatio = false, autoScale = true, shouldResize, onResizeStart, onResize, onResizeEnd, }) {\n    if (!isVisible) {\n        return null;\n    }\n    return (jsxs(Fragment, { children: [XY_RESIZER_LINE_POSITIONS.map((position) => (jsx(NodeResizeControl, { className: lineClassName, style: lineStyle, nodeId: nodeId, position: position, variant: ResizeControlVariant.Line, color: color, minWidth: minWidth, minHeight: minHeight, maxWidth: maxWidth, maxHeight: maxHeight, onResizeStart: onResizeStart, keepAspectRatio: keepAspectRatio, autoScale: autoScale, shouldResize: shouldResize, onResize: onResize, onResizeEnd: onResizeEnd }, position))), XY_RESIZER_HANDLE_POSITIONS.map((position) => (jsx(NodeResizeControl, { className: handleClassName, style: handleStyle, nodeId: nodeId, position: position, color: color, minWidth: minWidth, minHeight: minHeight, maxWidth: maxWidth, maxHeight: maxHeight, onResizeStart: onResizeStart, keepAspectRatio: keepAspectRatio, autoScale: autoScale, shouldResize: shouldResize, onResize: onResize, onResizeEnd: onResizeEnd }, position)))] }));\n}\n\nconst selector = (state) => state.domNode?.querySelector('.react-flow__renderer');\nfunction NodeToolbarPortal({ children }) {\n    const wrapperRef = useStore(selector);\n    if (!wrapperRef) {\n        return null;\n    }\n    return createPortal(children, wrapperRef);\n}\n\nconst nodeEqualityFn = (a, b) => a?.internals.positionAbsolute.x !== b?.internals.positionAbsolute.x ||\n    a?.internals.positionAbsolute.y !== b?.internals.positionAbsolute.y ||\n    a?.measured.width !== b?.measured.width ||\n    a?.measured.height !== b?.measured.height ||\n    a?.selected !== b?.selected ||\n    a?.internals.z !== b?.internals.z;\nconst nodesEqualityFn = (a, b) => {\n    if (a.size !== b.size) {\n        return false;\n    }\n    for (const [key, node] of a) {\n        if (nodeEqualityFn(node, b.get(key))) {\n            return false;\n        }\n    }\n    return true;\n};\nconst storeSelector = (state) => ({\n    x: state.transform[0],\n    y: state.transform[1],\n    zoom: state.transform[2],\n    selectedNodesCount: state.nodes.filter((node) => node.selected).length,\n});\n/**\n * This component can render a toolbar or tooltip to one side of a custom node. This\n * toolbar doesn't scale with the viewport so that the content is always visible.\n *\n * @public\n * @example\n * ```jsx\n *import { memo } from 'react';\n *import { Handle, Position, NodeToolbar } from '@xyflow/react';\n *\n *function CustomNode({ data }) {\n *  return (\n *    <>\n *      <NodeToolbar isVisible={data.toolbarVisible} position={data.toolbarPosition}>\n *        <button>delete</button>\n *        <button>copy</button>\n *        <button>expand</button>\n *      </NodeToolbar>\n *\n *      <div style={{ padding: '10px 20px' }}>\n *        {data.label}\n *      </div>\n *\n *      <Handle type=\"target\" position={Position.Left} />\n *      <Handle type=\"source\" position={Position.Right} />\n *    </>\n *  );\n *};\n *\n *export default memo(CustomNode);\n *```\n * @remarks By default, the toolbar is only visible when a node is selected. If multiple\n * nodes are selected it will not be visible to prevent overlapping toolbars or\n * clutter. You can override this behavior by setting the `isVisible` prop to `true`.\n */\nfunction NodeToolbar({ nodeId, children, className, style, isVisible, position = Position.Top, offset = 10, align = 'center', ...rest }) {\n    const contextNodeId = useNodeId();\n    const nodesSelector = useCallback((state) => {\n        const nodeIds = Array.isArray(nodeId) ? nodeId : [nodeId || contextNodeId || ''];\n        const internalNodes = nodeIds.reduce((res, id) => {\n            const node = state.nodeLookup.get(id);\n            if (node) {\n                res.set(node.id, node);\n            }\n            return res;\n        }, new Map());\n        return internalNodes;\n    }, [nodeId, contextNodeId]);\n    const nodes = useStore(nodesSelector, nodesEqualityFn);\n    const { x, y, zoom, selectedNodesCount } = useStore(storeSelector, shallow);\n    // if isVisible is not set, we show the toolbar only if its node is selected and no other node is selected\n    const isActive = typeof isVisible === 'boolean'\n        ? isVisible\n        : nodes.size === 1 && nodes.values().next().value?.selected && selectedNodesCount === 1;\n    if (!isActive || !nodes.size) {\n        return null;\n    }\n    const nodeRect = getInternalNodesBounds(nodes);\n    const nodesArray = Array.from(nodes.values());\n    const zIndex = Math.max(...nodesArray.map((node) => node.internals.z + 1));\n    const wrapperStyle = {\n        position: 'absolute',\n        transform: getNodeToolbarTransform(nodeRect, { x, y, zoom }, position, offset, align),\n        zIndex,\n        ...style,\n    };\n    return (jsx(NodeToolbarPortal, { children: jsx(\"div\", { style: wrapperStyle, className: cc(['react-flow__node-toolbar', className]), ...rest, \"data-id\": nodesArray.reduce((acc, node) => `${acc}${node.id} `, '').trim(), children: children }) }));\n}\n\nexport { Background, BackgroundVariant, BaseEdge, BezierEdge, ControlButton, Controls, EdgeLabelRenderer, EdgeText, Handle, MiniMap, NodeResizeControl, NodeResizer, NodeToolbar, Panel, index as ReactFlow, ReactFlowProvider, SimpleBezierEdge, SmoothStepEdge, StepEdge, StraightEdge, ViewportPortal, applyEdgeChanges, applyNodeChanges, getSimpleBezierPath, isEdge, isNode, useConnection, useEdges, useEdgesState, useHandleConnections, useInternalNode, useKeyPress, useNodeConnections, useNodeId, useNodes, useNodesData, useNodesInitialized, useNodesState, useOnSelectionChange, useOnViewportChange, useReactFlow, useStore, useStoreApi, useUpdateNodeInternals, useViewport };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACnC,MAAM,aAAa,aAAa,QAAQ;AAExC,MAAM,sBAAsB,0JAAA,CAAA,gBAAa,CAAC,WAAW;AACrD;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,QAAQ,EAAE,UAAU;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACzB,IAAI,UAAU,MAAM;QAChB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,CAAA,GAAA,8IAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,UAAU;AACnD;AACA;;;;;;;;;;;;CAYC,GACD,SAAS;IACL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACzB,IAAI,UAAU,MAAM;QAChB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAClB,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;QAC9B,CAAC,GAAG;QAAC;KAAM;AACf;AAEA,MAAM,QAAQ;IAAE,SAAS;AAAO;AAChC,MAAM,gBAAgB;IAClB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,QAAQ,CAAC;IACT,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,UAAU;AACd;AACA,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAC3B,MAAM,oBAAoB;AAC1B,MAAM,mBAAmB,CAAC,IAAM,EAAE,eAAe;AACjD,MAAM,0BAA0B,CAAC,IAAM,EAAE,eAAe;AACxD,SAAS,gBAAgB,EAAE,IAAI,EAAE;IAC7B,MAAM,kBAAkB,SAAS;IACjC,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,IAAI,GAAG,kBAAkB,CAAC,EAAE,MAAM;QAAE,aAAa;QAAa,eAAe;QAAQ,OAAO;QAAe,UAAU;IAAgB;AAC9J;AACA,SAAS,iBAAiB,EAAE,IAAI,EAAE,mBAAmB,EAAE;IACnD,MAAM,kBAAkB,SAAS;IACjC,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU;YAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,IAAI,GAAG,mBAAmB,CAAC,EAAE,MAAM;gBAAE,OAAO;gBAAO,UAAU,sBAC5F,eAAe,CAAC,+BAA+B,GAC/C,eAAe,CAAC,wCAAwC;YAAC;YAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,IAAI,GAAG,mBAAmB,CAAC,EAAE,MAAM;gBAAE,OAAO;gBAAO,UAAU,eAAe,CAAC,+BAA+B;YAAC;YAAI,CAAC,uBAAuB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB;gBAAE,MAAM;YAAK;SAAG;IAAC;AAC/Q;AAEA,MAAM,aAAa,CAAC,IAAO,EAAE,mBAAmB,GAAG,SAAS;AAC5D;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,WAAW,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,EAAE;IACtF,MAAM,gBAAgB,SAAS;IAC/B,MAAM,kBAAkB,GAAG,UAAU,CAAC,KAAK,CAAC;IAC5C,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAqB;eAAc;SAAgB;QAAG,OAAO;YAAE,GAAG,KAAK;YAAE;QAAc;QAAG,KAAK;QAAK,GAAG,IAAI;QAAE,UAAU;IAAS;AACxK;AACA,MAAM,WAAW,GAAG;AAEpB,SAAS,YAAY,EAAE,UAAU,EAAE,WAAW,cAAc,EAAE;IAC1D,IAAI,YAAY,iBAAiB;QAC7B,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,UAAU;QAAU,WAAW;QAA2B,gBAAgB;QAA0G,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,KAAK;YAAE,MAAM;YAAyB,QAAQ;YAAU,KAAK;YAAuB,cAAc;YAA0B,UAAU;QAAa;IAAG;AACjX;AAEA,MAAM,aAAa,CAAC;IAChB,MAAM,gBAAgB,EAAE;IACxB,MAAM,gBAAgB,EAAE;IACxB,KAAK,MAAM,GAAG,KAAK,IAAI,EAAE,UAAU,CAAE;QACjC,IAAI,KAAK,QAAQ,EAAE;YACf,cAAc,IAAI,CAAC,KAAK,SAAS,CAAC,QAAQ;QAC9C;IACJ;IACA,KAAK,MAAM,GAAG,KAAK,IAAI,EAAE,UAAU,CAAE;QACjC,IAAI,KAAK,QAAQ,EAAE;YACf,cAAc,IAAI,CAAC;QACvB;IACJ;IACA,OAAO;QAAE;QAAe;IAAc;AAC1C;AACA,MAAM,WAAW,CAAC,MAAQ,IAAI,EAAE;AAChC,SAAS,SAAS,CAAC,EAAE,CAAC;IAClB,OAAQ,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,EAAE,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,cAC/D,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,EAAE,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC;AACnE;AACA,SAAS,uBAAuB,EAAE,iBAAiB,EAAG;IAClD,MAAM,QAAQ;IACd,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,SAAS,YAAY;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,SAAS;YAAE,OAAO;YAAe,OAAO;QAAc;QAC5D,oBAAoB;QACpB,MAAM,QAAQ,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC,KAAO,GAAG;IAClE,GAAG;QAAC;QAAe;QAAe;KAAkB;IACpD,OAAO;AACX;AACA,MAAM,iBAAiB,CAAC,IAAM,CAAC,CAAC,EAAE,yBAAyB;AAC3D,SAAS,kBAAkB,EAAE,iBAAiB,EAAG;IAC7C,MAAM,kCAAkC,SAAS;IACjD,IAAI,qBAAqB,iCAAiC;QACtD,OAAO,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,wBAAwB;YAAE,mBAAmB;QAAkB;IAC9E;IACA,OAAO;AACX;AAEA,MAAM,oBAAoB;IAAC;IAAG;CAAE;AAChC,MAAM,kBAAkB;IAAE,GAAG;IAAG,GAAG;IAAG,MAAM;AAAE;AAE9C;;;;CAIC,GACD,8EAA8E;AAC9E,MAAM,yBAAyB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,qFAAqF;AACrF,MAAM,gBAAgB;OAAI;IAAwB;CAAO;AACzD,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,UAAU,EAAE,QAAQ;QACpB,UAAU,EAAE,QAAQ;QACpB,YAAY,EAAE,UAAU;QACxB,YAAY,EAAE,UAAU;QACxB,oBAAoB,EAAE,kBAAkB;QACxC,eAAe,EAAE,aAAa;QAC9B,OAAO,EAAE,KAAK;QACd,yBAAyB,EAAE,uBAAuB;QAClD,sBAAsB,EAAE,oBAAoB;IAChD,CAAC;AACD,MAAM,iBAAiB;IACnB;;;;KAIC,GACD,iBAAiB,0JAAA,CAAA,iBAAc;IAC/B,YAAY;IACZ,SAAS;IACT,SAAS;IACT,oBAAoB;IACpB,gBAAgB;IAChB,MAAM;IACN,mBAAmB;AACvB;AACA,SAAS,aAAa,KAAK;IACvB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAE,aAAa,EAAE,KAAK,EAAE,uBAAuB,EAAE,oBAAoB,EAAG,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IAC7K,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,wBAAwB,MAAM,YAAY,EAAE,MAAM,YAAY;QAC9D,OAAO;YACH,oEAAoE;YACpE,eAAe,OAAO,GAAG;YACzB;QACJ;IACJ,GAAG,EAAE;IACL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,KAAK,MAAM,aAAa,cAAe;YACnC,MAAM,aAAa,KAAK,CAAC,UAAU;YACnC,MAAM,qBAAqB,eAAe,OAAO,CAAC,UAAU;YAC5D,IAAI,eAAe,oBACf;YACJ,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,aAC5B;YACJ,yDAAyD;YACzD,IAAI,cAAc,SACd,SAAS;iBACR,IAAI,cAAc,SACnB,SAAS;iBACR,IAAI,cAAc,WACnB,WAAW;iBACV,IAAI,cAAc,WACnB,WAAW;iBACV,IAAI,cAAc,mBACnB,mBAAmB;iBAClB,IAAI,cAAc,cACnB,cAAc;iBACb,IAAI,cAAc,qBACnB,qBAAqB;iBAEpB,IAAI,cAAc,WACnB,MAAM,QAAQ,CAAC;gBAAE,eAAe;YAAW;iBAC1C,IAAI,cAAc,kBACnB,MAAM,QAAQ,CAAC;gBAAE,gBAAgB;YAAW;YAChD,IAAI,cAAc,mBAAmB;gBACjC,MAAM,QAAQ,CAAC;oBAAE,iBAAiB,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD,EAAE;gBAAY;YACvE,OAGI,MAAM,QAAQ,CAAC;gBAAE,CAAC,UAAU,EAAE;YAAW;QACjD;QACA,eAAe,OAAO,GAAG;IAC7B,GACA,+DAA+D;IAC/D,cAAc,GAAG,CAAC,CAAC,YAAc,KAAK,CAAC,UAAU;IACjD,OAAO;AACX;AAEA,SAAS;IACL,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,EAAE;QACrD,OAAO;IACX;IACA,OAAO,OAAO,UAAU,CAAC;AAC7B;AACA;;;;;CAKC,GACD,SAAS,kBAAkB,SAAS;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,WAAW,OAAO;IACrF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,cAAc,UAAU;YACxB,kBAAkB;YAClB;QACJ;QACA,MAAM,aAAa;QACnB,MAAM,uBAAuB,IAAM,kBAAkB,YAAY,UAAU,SAAS;QACpF;QACA,YAAY,iBAAiB,UAAU;QACvC,OAAO;YACH,YAAY,oBAAoB,UAAU;QAC9C;IACJ,GAAG;QAAC;KAAU;IACd,OAAO,mBAAmB,OAAO,iBAAiB,iBAAiB,UAAU,SAAS;AAC1F;AAEA,MAAM,aAAa,OAAO,aAAa,cAAc,WAAW;AAChE;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,YACT;;;;;;;;;;;CAWC,GACD,UAAU,IAAI,EAAE,UAAU;IAAE,QAAQ;IAAY,4BAA4B;AAAK,CAAC;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,wEAAwE;IACxE,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,wEAAwE;IACxE,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,IAAI,EAAE;IACrC;;;;;;;KAOC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,YAAY,MAAM;YAClB,MAAM,aAAa,MAAM,OAAO,CAAC,WAAW,UAAU;gBAAC;aAAQ;YAC/D,MAAM,OAAO,WACR,MAAM,CAAC,CAAC,KAAO,OAAO,OAAO,SAC9B;;;;iBAIC,IACA,GAAG,CAAC,CAAC,KAAO,GAAG,OAAO,CAAC,KAAK,MAAM,OAAO,CAAC,QAAQ,OAAO,KAAK,CAAC;YACpE,MAAM,WAAW,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,IAAI,MAAM,IAAI,OAAO,EAAE;YACnE,OAAO;gBAAC;gBAAM;aAAS;QAC3B;QACA,OAAO;YAAC,EAAE;YAAE,EAAE;SAAC;IACnB,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,SAAS,SAAS,UAAU;QAClC,MAAM,6BAA6B,SAAS,8BAA8B;QAC1E,IAAI,YAAY,MAAM;YAClB,MAAM,cAAc,CAAC;gBACjB,gBAAgB,OAAO,GAAG,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,QAAQ,IAAI,MAAM,MAAM;gBAC1F,MAAM,gBAAgB,CAAC,CAAC,gBAAgB,OAAO,IAAK,gBAAgB,OAAO,IAAI,CAAC,0BAA2B,KACvG,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE;gBACnB,IAAI,eAAe;oBACf,OAAO;gBACX;gBACA,MAAM,YAAY,aAAa,MAAM,IAAI,EAAE;gBAC3C,YAAY,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU;gBACxC,IAAI,cAAc,UAAU,YAAY,OAAO,EAAE,QAAQ;oBACrD,MAAM,SAAU,MAAM,YAAY,MAAM,CAAC,EAAE,IAAI,MAAM,MAAM;oBAC3D,MAAM,uBAAuB,QAAQ,aAAa,YAAY,QAAQ,aAAa;oBACnF,IAAI,QAAQ,cAAc,KAAK,SAAS,CAAC,gBAAgB,OAAO,IAAI,CAAC,oBAAoB,GAAG;wBACxF,MAAM,cAAc;oBACxB;oBACA,cAAc;gBAClB;YACJ;YACA,MAAM,YAAY,CAAC;gBACf,MAAM,YAAY,aAAa,MAAM,IAAI,EAAE;gBAC3C,IAAI,cAAc,UAAU,YAAY,OAAO,EAAE,OAAO;oBACpD,cAAc;oBACd,YAAY,OAAO,CAAC,KAAK;gBAC7B,OACK;oBACD,YAAY,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU;gBAC/C;gBACA,0MAA0M;gBAC1M,IAAI,MAAM,GAAG,KAAK,QAAQ;oBACtB,YAAY,OAAO,CAAC,KAAK;gBAC7B;gBACA,gBAAgB,OAAO,GAAG;YAC9B;YACA,MAAM,eAAe;gBACjB,YAAY,OAAO,CAAC,KAAK;gBACzB,cAAc;YAClB;YACA,QAAQ,iBAAiB,WAAW;YACpC,QAAQ,iBAAiB,SAAS;YAClC,OAAO,gBAAgB,CAAC,QAAQ;YAChC,OAAO,gBAAgB,CAAC,eAAe;YACvC,OAAO;gBACH,QAAQ,oBAAoB,WAAW;gBACvC,QAAQ,oBAAoB,SAAS;gBACrC,OAAO,mBAAmB,CAAC,QAAQ;gBACnC,OAAO,mBAAmB,CAAC,eAAe;YAC9C;QACJ;IACJ,GAAG;QAAC;QAAS;KAAc;IAC3B,OAAO;AACX;AACA,QAAQ;AACR,SAAS,cAAc,QAAQ,EAAE,WAAW,EAAE,IAAI;IAC9C,OAAQ,QACJ;;;;SAIC,IACA,MAAM,CAAC,CAAC,OAAS,QAAQ,KAAK,MAAM,KAAK,YAAY,IAAI,CAC1D;;;SAGC,IACA,IAAI,CAAC,CAAC,OAAS,KAAK,KAAK,CAAC,CAAC,IAAM,YAAY,GAAG,CAAC;AAC1D;AACA,SAAS,aAAa,SAAS,EAAE,WAAW;IACxC,OAAO,YAAY,QAAQ,CAAC,aAAa,SAAS;AACtD;AAEA;;;;;CAKC,GACD,MAAM,oBAAoB;IACtB,MAAM,QAAQ;IACd,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACX,OAAO;YACH,QAAQ,CAAC;gBACL,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;gBAClC,OAAO,UAAU,QAAQ,OAAO,CAAC,KAAK;oBAAE,UAAU,SAAS;gBAAS,KAAK,QAAQ,OAAO,CAAC;YAC7F;YACA,SAAS,CAAC;gBACN,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;gBAClC,OAAO,UAAU,QAAQ,OAAO,CAAC,IAAI,KAAK;oBAAE,UAAU,SAAS;gBAAS,KAAK,QAAQ,OAAO,CAAC;YACjG;YACA,QAAQ,CAAC,WAAW;gBAChB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;gBAClC,OAAO,UAAU,QAAQ,OAAO,CAAC,WAAW;oBAAE,UAAU,SAAS;gBAAS,KAAK,QAAQ,OAAO,CAAC;YACnG;YACA,SAAS,IAAM,MAAM,QAAQ,GAAG,SAAS,CAAC,EAAE;YAC5C,aAAa,OAAO,UAAU;gBAC1B,MAAM,EAAE,WAAW,CAAC,IAAI,IAAI,MAAM,EAAE,OAAO,EAAG,GAAG,MAAM,QAAQ;gBAC/D,IAAI,CAAC,SAAS;oBACV,OAAO,QAAQ,OAAO,CAAC;gBAC3B;gBACA,MAAM,QAAQ,WAAW,CAAC;oBACtB,GAAG,SAAS,CAAC,IAAI;oBACjB,GAAG,SAAS,CAAC,IAAI;oBACjB,MAAM,SAAS,IAAI,IAAI;gBAC3B,GAAG;gBACH,OAAO,QAAQ,OAAO,CAAC;YAC3B;YACA,aAAa;gBACT,MAAM,CAAC,GAAG,GAAG,KAAK,GAAG,MAAM,QAAQ,GAAG,SAAS;gBAC/C,OAAO;oBAAE;oBAAG;oBAAG;gBAAK;YACxB;YACA,WAAW,OAAO,GAAG,GAAG;gBACpB,OAAO,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG;YAC5C;YACA,WAAW,OAAO,QAAQ;gBACtB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;gBACnE,MAAM,WAAW,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,OAAO,QAAQ,SAAS,SAAS,SAAS,WAAW;gBACnG,IAAI,CAAC,SAAS;oBACV,OAAO,QAAQ,OAAO,CAAC;gBAC3B;gBACA,MAAM,QAAQ,WAAW,CAAC,UAAU;oBAChC,UAAU,SAAS;oBACnB,MAAM,SAAS;oBACf,aAAa,SAAS;gBAC1B;gBACA,OAAO,QAAQ,OAAO,CAAC;YAC3B;YACA,sBAAsB,CAAC,gBAAgB,UAAU,CAAC,CAAC;gBAC/C,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;gBACnE,IAAI,CAAC,SAAS;oBACV,OAAO;gBACX;gBACA,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ,qBAAqB;gBAC1D,MAAM,oBAAoB;oBACtB,GAAG,eAAe,CAAC,GAAG;oBACtB,GAAG,eAAe,CAAC,GAAG;gBAC1B;gBACA,MAAM,YAAY,QAAQ,QAAQ,IAAI;gBACtC,MAAM,cAAc,QAAQ,UAAU,IAAI;gBAC1C,OAAO,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD,EAAE,mBAAmB,WAAW,aAAa;YAC3E;YACA,sBAAsB,CAAC;gBACnB,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;gBAC7C,IAAI,CAAC,SAAS;oBACV,OAAO;gBACX;gBACA,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ,qBAAqB;gBAC1D,MAAM,mBAAmB,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD,EAAE,cAAc;gBAC5D,OAAO;oBACH,GAAG,iBAAiB,CAAC,GAAG;oBACxB,GAAG,iBAAiB,CAAC,GAAG;gBAC5B;YACJ;QACJ;IACJ,GAAG,EAAE;AACT;AAEA;;;;CAIC,GACD,SAAS,aAAa,OAAO,EAAE,QAAQ;IACnC,MAAM,kBAAkB,EAAE;IAC1B;;;KAGC,GACD,MAAM,aAAa,IAAI;IACvB,MAAM,iBAAiB,EAAE;IACzB,KAAK,MAAM,UAAU,QAAS;QAC1B,IAAI,OAAO,IAAI,KAAK,OAAO;YACvB,eAAe,IAAI,CAAC;YACpB;QACJ,OACK,IAAI,OAAO,IAAI,KAAK,YAAY,OAAO,IAAI,KAAK,WAAW;YAC5D;;;aAGC,GACD,WAAW,GAAG,CAAC,OAAO,EAAE,EAAE;gBAAC;aAAO;QACtC,OACK;YACD,MAAM,iBAAiB,WAAW,GAAG,CAAC,OAAO,EAAE;YAC/C,IAAI,gBAAgB;gBAChB;;;iBAGC,GACD,eAAe,IAAI,CAAC;YACxB,OACK;gBACD,WAAW,GAAG,CAAC,OAAO,EAAE,EAAE;oBAAC;iBAAO;YACtC;QACJ;IACJ;IACA,KAAK,MAAM,WAAW,SAAU;QAC5B,MAAM,UAAU,WAAW,GAAG,CAAC,QAAQ,EAAE;QACzC;;;SAGC,GACD,IAAI,CAAC,SAAS;YACV,gBAAgB,IAAI,CAAC;YACrB;QACJ;QACA,6EAA6E;QAC7E,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,UAAU;YAC9B;QACJ;QACA,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,WAAW;YAC/B,gBAAgB,IAAI,CAAC;gBAAE,GAAG,OAAO,CAAC,EAAE,CAAC,IAAI;YAAC;YAC1C;QACJ;QACA;;;;SAIC,GACD,MAAM,iBAAiB;YAAE,GAAG,OAAO;QAAC;QACpC,KAAK,MAAM,UAAU,QAAS;YAC1B,YAAY,QAAQ;QACxB;QACA,gBAAgB,IAAI,CAAC;IACzB;IACA;;;KAGC,GACD,IAAI,eAAe,MAAM,EAAE;QACvB,eAAe,OAAO,CAAC,CAAC;YACpB,IAAI,OAAO,KAAK,KAAK,WAAW;gBAC5B,gBAAgB,MAAM,CAAC,OAAO,KAAK,EAAE,GAAG;oBAAE,GAAG,OAAO,IAAI;gBAAC;YAC7D,OACK;gBACD,gBAAgB,IAAI,CAAC;oBAAE,GAAG,OAAO,IAAI;gBAAC;YAC1C;QACJ;IACJ;IACA,OAAO;AACX;AACA,qEAAqE;AACrE,SAAS,YAAY,MAAM,EAAE,OAAO;IAChC,OAAQ,OAAO,IAAI;QACf,KAAK;YAAU;gBACX,QAAQ,QAAQ,GAAG,OAAO,QAAQ;gBAClC;YACJ;QACA,KAAK;YAAY;gBACb,IAAI,OAAO,OAAO,QAAQ,KAAK,aAAa;oBACxC,QAAQ,QAAQ,GAAG,OAAO,QAAQ;gBACtC;gBACA,IAAI,OAAO,OAAO,QAAQ,KAAK,aAAa;oBACxC,QAAQ,QAAQ,GAAG,OAAO,QAAQ;gBACtC;gBACA;YACJ;QACA,KAAK;YAAc;gBACf,IAAI,OAAO,OAAO,UAAU,KAAK,aAAa;oBAC1C,QAAQ,QAAQ,KAAK,CAAC;oBACtB,QAAQ,QAAQ,CAAC,KAAK,GAAG,OAAO,UAAU,CAAC,KAAK;oBAChD,QAAQ,QAAQ,CAAC,MAAM,GAAG,OAAO,UAAU,CAAC,MAAM;oBAClD,IAAI,OAAO,aAAa,EAAE;wBACtB,IAAI,OAAO,aAAa,KAAK,QAAQ,OAAO,aAAa,KAAK,SAAS;4BACnE,QAAQ,KAAK,GAAG,OAAO,UAAU,CAAC,KAAK;wBAC3C;wBACA,IAAI,OAAO,aAAa,KAAK,QAAQ,OAAO,aAAa,KAAK,UAAU;4BACpE,QAAQ,MAAM,GAAG,OAAO,UAAU,CAAC,MAAM;wBAC7C;oBACJ;gBACJ;gBACA,IAAI,OAAO,OAAO,QAAQ,KAAK,WAAW;oBACtC,QAAQ,QAAQ,GAAG,OAAO,QAAQ;gBACtC;gBACA;YACJ;IACJ;AACJ;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,SAAS,iBAAiB,OAAO,EAAE,KAAK;IACpC,OAAO,aAAa,SAAS;AACjC;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,SAAS,iBAAiB,OAAO,EAAE,KAAK;IACpC,OAAO,aAAa,SAAS;AACjC;AACA,SAAS,sBAAsB,EAAE,EAAE,QAAQ;IACvC,OAAO;QACH;QACA,MAAM;QACN;IACJ;AACJ;AACA,SAAS,oBAAoB,KAAK,EAAE,cAAc,IAAI,KAAK,EAAE,aAAa,KAAK;IAC3E,MAAM,UAAU,EAAE;IAClB,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,MAAO;QAC5B,MAAM,iBAAiB,YAAY,GAAG,CAAC;QACvC,0EAA0E;QAC1E,IAAI,CAAC,CAAC,KAAK,QAAQ,KAAK,aAAa,CAAC,cAAc,KAAK,KAAK,QAAQ,KAAK,gBAAgB;YACvF,IAAI,YAAY;gBACZ;;;;iBAIC,GACD,KAAK,QAAQ,GAAG;YACpB;YACA,QAAQ,IAAI,CAAC,sBAAsB,KAAK,EAAE,EAAE;QAChD;IACJ;IACA,OAAO;AACX;AACA,SAAS,uBAAuB,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAG;IACnD,MAAM,UAAU,EAAE;IAClB,MAAM,cAAc,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,OAAS;YAAC,KAAK,EAAE;YAAE;SAAK;IAC/D,KAAK,MAAM,CAAC,OAAO,KAAK,IAAI,MAAM,OAAO,GAAI;QACzC,MAAM,aAAa,OAAO,GAAG,CAAC,KAAK,EAAE;QACrC,MAAM,YAAY,YAAY,WAAW,YAAY;QACrD,IAAI,cAAc,aAAa,cAAc,MAAM;YAC/C,QAAQ,IAAI,CAAC;gBAAE,IAAI,KAAK,EAAE;gBAAE,MAAM;gBAAM,MAAM;YAAU;QAC5D;QACA,IAAI,cAAc,WAAW;YACzB,QAAQ,IAAI,CAAC;gBAAE,MAAM;gBAAM,MAAM;gBAAO;YAAM;QAClD;IACJ;IACA,KAAK,MAAM,CAAC,GAAG,IAAI,OAAQ;QACvB,MAAM,WAAW,YAAY,GAAG,CAAC;QACjC,IAAI,aAAa,WAAW;YACxB,QAAQ,IAAI,CAAC;gBAAE;gBAAI,MAAM;YAAS;QACtC;IACJ;IACA,OAAO;AACX;AACA,SAAS,sBAAsB,IAAI;IAC/B,OAAO;QACH,IAAI,KAAK,EAAE;QACX,MAAM;IACV;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,MAAM,SAAS,CAAC,UAAY,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE;AACvC;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,MAAM,SAAS,CAAC,UAAY,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE;AACvC,mEAAmE;AACnE,SAAS,gBAAgB,MAAM;IAC3B,8DAA8D;IAC9D,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACtB;AAEA,sEAAsE;AACtE,MAAM,4BAA4B,OAAO,WAAW,cAAc,qMAAA,CAAA,kBAAe,GAAG,qMAAA,CAAA,YAAS;AAE7F;;;;;;;CAOC,GACD,SAAS,SAAS,QAAQ;IACtB;;;;;;KAMC,GACD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;IAC5C;;;;KAIC,GACD,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,YAAY,IAAM,UAAU,CAAA,IAAK,IAAI,OAAO;IAC3E;;;;KAIC,GACD,0BAA0B;QACtB,MAAM,aAAa,MAAM,GAAG;QAC5B,IAAI,WAAW,MAAM,EAAE;YACnB,SAAS;YACT,MAAM,KAAK;QACf;IACJ,GAAG;QAAC;KAAO;IACX,OAAO;AACX;AACA,SAAS,YAAY,EAAE;IACnB,IAAI,QAAQ,EAAE;IACd,OAAO;QACH,KAAK,IAAM;QACX,OAAO;YACH,QAAQ,EAAE;QACd;QACA,MAAM,CAAC;YACH,MAAM,IAAI,CAAC;YACX;QACJ;IACJ;AACJ;AAEA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACnC;;;;;CAKC,GACD,SAAS,cAAc,EAAE,QAAQ,EAAG;IAChC,MAAM,QAAQ;IACd,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,MAAM,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;QAC1G;;;;SAIC,GACD,IAAI,OAAO;QACX,KAAK,MAAM,WAAW,WAAY;YAC9B,OAAO,OAAO,YAAY,aAAa,QAAQ,QAAQ;QAC3D;QACA,MAAM,UAAU,uBAAuB;YACnC,OAAO;YACP,QAAQ;QACZ;QACA,IAAI,iBAAiB;YACjB,SAAS;QACb;QACA,uEAAuE;QACvE,IAAI,QAAQ,MAAM,GAAG,GAAG;YACpB,gBAAgB;QACpB,OACK,IAAI,eAAe;YACpB,uEAAuE;YACvE,sCAAsC;YACtC,OAAO,qBAAqB,CAAC;gBACzB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ;gBACzD,IAAI,eAAe;oBACf,SAAS;gBACb;YACJ;QACJ;IACJ,GAAG,EAAE;IACL,MAAM,YAAY,SAAS;IAC3B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,MAAM,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;QAC3F,IAAI,OAAO;QACX,KAAK,MAAM,WAAW,WAAY;YAC9B,OAAO,OAAO,YAAY,aAAa,QAAQ,QAAQ;QAC3D;QACA,IAAI,iBAAiB;YACjB,SAAS;QACb,OACK,IAAI,eAAe;YACpB,cAAc,uBAAuB;gBACjC,OAAO;gBACP,QAAQ;YACZ;QACJ;IACJ,GAAG,EAAE;IACL,MAAM,YAAY,SAAS;IAC3B,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAAE;YAAW;QAAU,CAAC,GAAG,EAAE;IAC1D,OAAO,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,aAAa,QAAQ,EAAE;QAAE,OAAO;QAAO,UAAU;IAAS;AACzE;AACA,SAAS;IACL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAChC,IAAI,CAAC,cAAc;QACf,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC,CAAC,EAAE,OAAO;AACrC;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,SAAS;IACL,MAAM,iBAAiB;IACvB,MAAM,QAAQ;IACd,MAAM,eAAe;IACrB,MAAM,sBAAsB,SAAS;IACrC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,MAAM,kBAAkB,CAAC,KAAO,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC;QAChE,MAAM,WAAW,CAAC;YACd,aAAa,SAAS,CAAC,IAAI,CAAC;QAChC;QACA,MAAM,WAAW,CAAC;YACd,aAAa,SAAS,CAAC,IAAI,CAAC;QAChC;QACA,MAAM,cAAc,CAAC;YACjB,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;YACjD,MAAM,YAAY,OAAO,QAAQ,OAAO,WAAW,GAAG,CAAC,KAAK,EAAE;YAC9D,MAAM,WAAW,UAAU,QAAQ,GAC7B,CAAA,GAAA,0JAAA,CAAA,2BAAwB,AAAD,EAAE,UAAU,QAAQ,EAAE,UAAU,QAAQ,EAAE,UAAU,QAAQ,EAAE,YAAY,cACjG,UAAU,QAAQ;YACxB,MAAM,mBAAmB;gBACrB,GAAG,SAAS;gBACZ;gBACA,OAAO,UAAU,QAAQ,EAAE,SAAS,UAAU,KAAK;gBACnD,QAAQ,UAAU,QAAQ,EAAE,UAAU,UAAU,MAAM;YAC1D;YACA,OAAO,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE;QACtB;QACA,MAAM,aAAa,CAAC,IAAI,YAAY,UAAU;YAAE,SAAS;QAAM,CAAC;YAC5D,SAAS,CAAC,YAAc,UAAU,GAAG,CAAC,CAAC;oBACnC,IAAI,KAAK,EAAE,KAAK,IAAI;wBAChB,MAAM,WAAW,OAAO,eAAe,aAAa,WAAW,QAAQ;wBACvE,OAAO,QAAQ,OAAO,IAAI,OAAO,YAAY,WAAW;4BAAE,GAAG,IAAI;4BAAE,GAAG,QAAQ;wBAAC;oBACnF;oBACA,OAAO;gBACX;QACJ;QACA,MAAM,aAAa,CAAC,IAAI,YAAY,UAAU;YAAE,SAAS;QAAM,CAAC;YAC5D,SAAS,CAAC,YAAc,UAAU,GAAG,CAAC,CAAC;oBACnC,IAAI,KAAK,EAAE,KAAK,IAAI;wBAChB,MAAM,WAAW,OAAO,eAAe,aAAa,WAAW,QAAQ;wBACvE,OAAO,QAAQ,OAAO,IAAI,OAAO,YAAY,WAAW;4BAAE,GAAG,IAAI;4BAAE,GAAG,QAAQ;wBAAC;oBACnF;oBACA,OAAO;gBACX;QACJ;QACA,OAAO;YACH,UAAU,IAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wBAAE,GAAG,CAAC;oBAAC,CAAC;YAC3D,SAAS,CAAC,KAAO,gBAAgB,KAAK,UAAU;YAChD;YACA,UAAU;gBACN,MAAM,EAAE,QAAQ,EAAE,EAAE,GAAG,MAAM,QAAQ;gBACrC,OAAO,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC;wBAAE,GAAG,CAAC;oBAAC,CAAC;YACrC;YACA,SAAS,CAAC,KAAO,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC;YACjD;YACA;YACA,UAAU,CAAC;gBACP,MAAM,WAAW,MAAM,OAAO,CAAC,WAAW,UAAU;oBAAC;iBAAQ;gBAC7D,aAAa,SAAS,CAAC,IAAI,CAAC,CAAC,QAAU;2BAAI;2BAAU;qBAAS;YAClE;YACA,UAAU,CAAC;gBACP,MAAM,WAAW,MAAM,OAAO,CAAC,WAAW,UAAU;oBAAC;iBAAQ;gBAC7D,aAAa,SAAS,CAAC,IAAI,CAAC,CAAC,QAAU;2BAAI;2BAAU;qBAAS;YAClE;YACA,UAAU;gBACN,MAAM,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ;gBAC5D,MAAM,CAAC,GAAG,GAAG,KAAK,GAAG;gBACrB,OAAO;oBACH,OAAO,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC;4BAAE,GAAG,CAAC;wBAAC,CAAC;oBACjC,OAAO,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC;4BAAE,GAAG,CAAC;wBAAC,CAAC;oBACjC,UAAU;wBACN;wBACA;wBACA;oBACJ;gBACJ;YACJ;YACA,gBAAgB,OAAO,EAAE,OAAO,gBAAgB,EAAE,EAAE,OAAO,gBAAgB,EAAE,EAAE;gBAC3E,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,cAAc,EAAG,GAAG,MAAM,QAAQ;gBACxI,MAAM,EAAE,OAAO,aAAa,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,CAAA,GAAA,0JAAA,CAAA,sBAAmB,AAAD,EAAE;oBAC7E;oBACA;oBACA;oBACA;oBACA;gBACJ;gBACA,MAAM,mBAAmB,cAAc,MAAM,GAAG;gBAChD,MAAM,mBAAmB,cAAc,MAAM,GAAG;gBAChD,IAAI,kBAAkB;oBAClB,MAAM,cAAc,cAAc,GAAG,CAAC;oBACtC,gBAAgB;oBAChB,mBAAmB;gBACvB;gBACA,IAAI,kBAAkB;oBAClB,MAAM,cAAc,cAAc,GAAG,CAAC;oBACtC,gBAAgB;oBAChB,mBAAmB;gBACvB;gBACA,IAAI,oBAAoB,kBAAkB;oBACtC,WAAW;wBAAE,OAAO;wBAAe,OAAO;oBAAc;gBAC5D;gBACA,OAAO;oBAAE,cAAc;oBAAe,cAAc;gBAAc;YACtE;YACA,sBAAsB,CAAC,YAAY,YAAY,IAAI,EAAE;gBACjD,MAAM,SAAS,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD,EAAE;gBAC5B,MAAM,WAAW,SAAS,aAAa,YAAY;gBACnD,MAAM,iBAAiB,UAAU;gBACjC,IAAI,CAAC,UAAU;oBACX,OAAO,EAAE;gBACb;gBACA,OAAO,CAAC,SAAS,MAAM,QAAQ,GAAG,KAAK,EAAE,MAAM,CAAC,CAAC;oBAC7C,MAAM,eAAe,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE;oBACzD,IAAI,gBAAgB,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,WAAW,EAAE,IAAI,CAAC,aAAa,SAAS,CAAC,gBAAgB,GAAG;wBACjG,OAAO;oBACX;oBACA,MAAM,eAAe,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,IAAI;oBACrD,MAAM,kBAAkB,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc;oBACzD,MAAM,mBAAmB,aAAa,kBAAkB;oBACxD,OAAO,oBAAoB,mBAAmB,SAAS,KAAK,GAAG,SAAS,MAAM;gBAClF;YACJ;YACA,oBAAoB,CAAC,YAAY,MAAM,YAAY,IAAI;gBACnD,MAAM,SAAS,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD,EAAE;gBAC5B,MAAM,WAAW,SAAS,aAAa,YAAY;gBACnD,IAAI,CAAC,UAAU;oBACX,OAAO;gBACX;gBACA,MAAM,kBAAkB,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;gBACrD,MAAM,mBAAmB,aAAa,kBAAkB;gBACxD,OAAO,oBAAoB,mBAAmB,SAAS,KAAK,GAAG,SAAS,MAAM;YAClF;YACA;YACA,gBAAgB,CAAC,IAAI,YAAY,UAAU;gBAAE,SAAS;YAAM,CAAC;gBACzD,WAAW,IAAI,CAAC;oBACZ,MAAM,WAAW,OAAO,eAAe,aAAa,WAAW,QAAQ;oBACvE,OAAO,QAAQ,OAAO,GAAG;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAS,IAAI;wBAAE,GAAG,IAAI;wBAAE,MAAM;4BAAE,GAAG,KAAK,IAAI;4BAAE,GAAG,QAAQ;wBAAC;oBAAE;gBAC1G,GAAG;YACP;YACA;YACA,gBAAgB,CAAC,IAAI,YAAY,UAAU;gBAAE,SAAS;YAAM,CAAC;gBACzD,WAAW,IAAI,CAAC;oBACZ,MAAM,WAAW,OAAO,eAAe,aAAa,WAAW,QAAQ;oBACvE,OAAO,QAAQ,OAAO,GAAG;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAS,IAAI;wBAAE,GAAG,IAAI;wBAAE,MAAM;4BAAE,GAAG,KAAK,IAAI;4BAAE,GAAG,QAAQ;wBAAC;oBAAE;gBAC1G,GAAG;YACP;YACA,gBAAgB,CAAC;gBACb,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;gBACjD,OAAO,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;oBAAE;oBAAY;gBAAW;YAC1D;YACA,sBAAsB,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,GAAK,MAAM,IAAI,CAAC,MACtD,QAAQ,GACR,gBAAgB,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,GAC5D,YAAY,EAAE;YACpB,oBAAoB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAK,MAAM,IAAI,CAAC,MAC1D,QAAQ,GACR,gBAAgB,CAAC,GAAG,CAAC,GAAG,SAAS,OAAQ,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,MAAM,GAAI,IAAI,GAC9F,YAAY,EAAE;YACpB,SAAS,OAAO;gBACZ,2DAA2D;gBAC3D,0FAA0F;gBAC1F,MAAM,kBAAkB,MAAM,QAAQ,GAAG,eAAe,IAAI,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD;gBACxE,2EAA2E;gBAC3E,MAAM,QAAQ,CAAC;oBAAE,eAAe;oBAAM,gBAAgB;oBAAS;gBAAgB;gBAC/E,aAAa,SAAS,CAAC,IAAI,CAAC,CAAC,QAAU;2BAAI;qBAAM;gBACjD,OAAO,gBAAgB,OAAO;YAClC;QACJ;IACJ,GAAG,EAAE;IACL,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACX,OAAO;YACH,GAAG,aAAa;YAChB,GAAG,cAAc;YACjB;QACJ;IACJ,GAAG;QAAC;KAAoB;AAC5B;AAEA,MAAM,WAAW,CAAC,OAAS,KAAK,QAAQ;AACxC,MAAM,QAAQ,OAAO,WAAW,cAAc,SAAS;AACvD;;;;CAIC,GACD,SAAS,oBAAoB,EAAE,aAAa,EAAE,qBAAqB,EAAG;IAClE,MAAM,QAAQ;IACd,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,MAAM,mBAAmB,YAAY,eAAe;QAAE,4BAA4B;IAAM;IACxF,MAAM,2BAA2B,YAAY,uBAAuB;QAAE,QAAQ;IAAM;IACpF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,kBAAkB;YAClB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;YACvC,eAAe;gBAAE,OAAO,MAAM,MAAM,CAAC;gBAAW,OAAO,MAAM,MAAM,CAAC;YAAU;YAC9E,MAAM,QAAQ,CAAC;gBAAE,sBAAsB;YAAM;QACjD;IACJ,GAAG;QAAC;KAAiB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,CAAC;YAAE,sBAAsB;QAAyB;IACpE,GAAG;QAAC;KAAyB;AACjC;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,OAAO;IAC7B,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,mBAAmB;YACrB,IAAI,CAAC,QAAQ,OAAO,EAAE;gBAClB,OAAO;YACX;YACA,MAAM,OAAO,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,OAAO;YAC1C,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;gBACvC,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,0JAAA,CAAA,gBAAa,CAAC,WAAW;YAC/D;YACA,MAAM,QAAQ,CAAC;gBAAE,OAAO,KAAK,KAAK,IAAI;gBAAK,QAAQ,KAAK,MAAM,IAAI;YAAI;QAC1E;QACA,IAAI,QAAQ,OAAO,EAAE;YACjB;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC,MAAM,iBAAiB,IAAI,eAAe,IAAM;YAChD,eAAe,OAAO,CAAC,QAAQ,OAAO;YACtC,OAAO;gBACH,OAAO,mBAAmB,CAAC,UAAU;gBACrC,IAAI,kBAAkB,QAAQ,OAAO,EAAE;oBACnC,eAAe,SAAS,CAAC,QAAQ,OAAO;gBAC5C;YACJ;QACJ;IACJ,GAAG,EAAE;AACT;AAEA,MAAM,iBAAiB;IACnB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,KAAK;IACL,MAAM;AACV;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,qBAAqB,EAAE,mBAAmB;QAC1C,KAAK,EAAE,GAAG;IACd,CAAC;AACD,SAAS,SAAS,EAAE,iBAAiB,EAAE,eAAe,IAAI,EAAE,cAAc,IAAI,EAAE,cAAc,KAAK,EAAE,mBAAmB,GAAG,EAAE,kBAAkB,0JAAA,CAAA,kBAAe,CAAC,IAAI,EAAE,oBAAoB,IAAI,EAAE,YAAY,IAAI,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,qBAAqB,EAAE,mBAAmB,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,iBAAiB,EAAG;IACzZ,MAAM,QAAQ;IACd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,EAAE,mBAAmB,EAAE,GAAG,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IACjE,MAAM,2BAA2B,YAAY;IAC7C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACrB,iBAAiB;IACjB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,mBAAmB;YAAE,GAAG,SAAS,CAAC,EAAE;YAAE,GAAG,SAAS,CAAC,EAAE;YAAE,MAAM,SAAS,CAAC,EAAE;QAAC;QAC1E,IAAI,CAAC,sBAAsB;YACvB,MAAM,QAAQ,CAAC;gBAAE;YAAU;QAC/B;IACJ,GAAG;QAAC;QAAkB;KAAqB;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,SAAS,OAAO,EAAE;YAClB,QAAQ,OAAO,GAAG,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE;gBACxB,SAAS,SAAS,OAAO;gBACzB;gBACA;gBACA;gBACA,UAAU;gBACV;gBACA,kBAAkB,CAAC,eAAiB,MAAM,QAAQ,CAAC;wBAAE;oBAAa;gBAClE,gBAAgB,CAAC,OAAO;oBACpB,MAAM,EAAE,qBAAqB,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;oBAC7D,cAAc,OAAO;oBACrB,wBAAwB;gBAC5B;gBACA,WAAW,CAAC,OAAO;oBACf,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;oBACnD,SAAS,OAAO;oBAChB,mBAAmB;gBACvB;gBACA,cAAc,CAAC,OAAO;oBAClB,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ;oBACzD,YAAY,OAAO;oBACnB,sBAAsB;gBAC1B;YACJ;YACA,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,QAAQ,OAAO,CAAC,WAAW;YAClD,MAAM,QAAQ,CAAC;gBACX,SAAS,QAAQ,OAAO;gBACxB,WAAW;oBAAC;oBAAG;oBAAG;iBAAK;gBACvB,SAAS,SAAS,OAAO,CAAC,OAAO,CAAC;YACtC;YACA,OAAO;gBACH,QAAQ,OAAO,EAAE;YACrB;QACJ;IACJ,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,QAAQ,OAAO,EAAE,OAAO;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW;QAAwB,KAAK;QAAU,OAAO;QAAgB,UAAU;IAAS;AACrH;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,qBAAqB,EAAE,mBAAmB;QAC1C,mBAAmB,EAAE,iBAAiB;IAC1C,CAAC;AACD,SAAS;IACL,MAAM,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IAC/E,MAAM,WAAW,uBAAuB;IACxC,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW;QAA+C,OAAO;YAC9E,OAAO,kBAAkB,KAAK;YAC9B,QAAQ,kBAAkB,MAAM;YAChC,WAAW,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC,GAAG,CAAC;QAC9E;IAAE;AACV;AAEA,MAAM,cAAc,CAAC,SAAS;IAC1B,OAAO,CAAC;QACJ,IAAI,MAAM,MAAM,KAAK,aAAa,OAAO,EAAE;YACvC;QACJ;QACA,UAAU;IACd;AACJ;AACA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,qBAAqB,EAAE,mBAAmB;QAC1C,oBAAoB,EAAE,kBAAkB;QACxC,sBAAsB,EAAE,UAAU,CAAC,UAAU;QAC7C,UAAU,EAAE,YAAY;IAC5B,CAAC;AACD,SAAS,KAAK,EAAE,WAAW,EAAE,mBAAmB,EAAE,gBAAgB,0JAAA,CAAA,gBAAa,CAAC,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,WAAW,EAAE,iBAAiB,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,QAAQ,EAAG;IAC9P,MAAM,QAAQ;IACd,MAAM,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,oBAAoB,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IAChH,MAAM,qBAAqB,sBAAsB,CAAC,eAAe,mBAAmB;IACpF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC7B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI;IACnC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI;IACnC,4FAA4F;IAC5F,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,UAAU,CAAC;QACb,sFAAsF;QACtF,gEAAgE;QAChE,IAAI,oBAAoB,OAAO,IAAI,sBAAsB;YACrD,oBAAoB,OAAO,GAAG;YAC9B;QACJ;QACA,cAAc;QACd,MAAM,QAAQ,GAAG,qBAAqB;QACtC,MAAM,QAAQ,CAAC;YAAE,sBAAsB;QAAM;IACjD;IACA,MAAM,gBAAgB,CAAC;QACnB,IAAI,MAAM,OAAO,CAAC,cAAc,WAAW,SAAS,IAAI;YACpD,MAAM,cAAc;YACpB;QACJ;QACA,oBAAoB;IACxB;IACA,MAAM,UAAU,eAAe,CAAC,QAAU,aAAa,SAAS;IAChE,MAAM,gBAAgB,CAAC;QACnB,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;QACzD,gBAAgB,OAAO,GAAG,SAAS;QACnC,IAAI,CAAC,sBACD,CAAC,eACD,MAAM,MAAM,KAAK,KACjB,MAAM,MAAM,KAAK,UAAU,OAAO,IAClC,CAAC,gBAAgB,OAAO,EAAE;YAC1B;QACJ;QACA,MAAM,MAAM,EAAE,oBAAoB,MAAM,SAAS;QACjD,iBAAiB,OAAO,GAAG;QAC3B,oBAAoB,OAAO,GAAG;QAC9B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,WAAW,EAAE,gBAAgB,OAAO;QAC5E;QACA,MAAM,QAAQ,CAAC;YACX,mBAAmB;gBACf,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR;gBACA;YACJ;QACJ;QACA,mBAAmB;IACvB;IACA,MAAM,gBAAgB,CAAC;QACnB,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,EAAG,GAAG,MAAM,QAAQ;QAC9J,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,mBAAmB;YAChD;QACJ;QACA,oBAAoB,OAAO,GAAG;QAC9B,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,WAAW,EAAE,gBAAgB,OAAO;QAC5F,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;QAC3B,MAAM,qBAAqB;YACvB;YACA;YACA,GAAG,SAAS,SAAS,SAAS;YAC9B,GAAG,SAAS,SAAS,SAAS;YAC9B,OAAO,KAAK,GAAG,CAAC,SAAS;YACzB,QAAQ,KAAK,GAAG,CAAC,SAAS;QAC9B;QACA,MAAM,sBAAsB,gBAAgB,OAAO;QACnD,MAAM,sBAAsB,gBAAgB,OAAO;QACnD,gBAAgB,OAAO,GAAG,IAAI,IAAI,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,oBAAoB,WAAW,kBAAkB,0JAAA,CAAA,gBAAa,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;QAChK,gBAAgB,OAAO,GAAG,IAAI;QAC9B,MAAM,kBAAkB,oBAAoB,cAAc;QAC1D,wDAAwD;QACxD,KAAK,MAAM,UAAU,gBAAgB,OAAO,CAAE;YAC1C,MAAM,cAAc,iBAAiB,GAAG,CAAC;YACzC,IAAI,CAAC,aACD;YACJ,KAAK,MAAM,EAAE,MAAM,EAAE,IAAI,YAAY,MAAM,GAAI;gBAC3C,MAAM,OAAO,WAAW,GAAG,CAAC;gBAC5B,IAAI,QAAQ,CAAC,KAAK,UAAU,IAAI,eAAe,GAAG;oBAC9C,gBAAgB,OAAO,CAAC,GAAG,CAAC;gBAChC;YACJ;QACJ;QACA,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD,EAAE,qBAAqB,gBAAgB,OAAO,GAAG;YAC7D,MAAM,UAAU,oBAAoB,YAAY,gBAAgB,OAAO,EAAE;YACzE,mBAAmB;QACvB;QACA,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD,EAAE,qBAAqB,gBAAgB,OAAO,GAAG;YAC7D,MAAM,UAAU,oBAAoB,YAAY,gBAAgB,OAAO;YACvE,mBAAmB;QACvB;QACA,MAAM,QAAQ,CAAC;YACX,mBAAmB;YACnB,qBAAqB;YACrB,sBAAsB;QAC1B;IACJ;IACA,MAAM,cAAc,CAAC;QACjB,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,iBAAiB,OAAO,EAAE;YACjD;QACJ;QACA,MAAM,MAAM,EAAE,wBAAwB,MAAM,SAAS;QACrD,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ;QAC5C;;;SAGC,GACD,IAAI,CAAC,uBAAuB,qBAAqB,MAAM,MAAM,KAAK,UAAU,OAAO,EAAE;YACjF,UAAU;QACd;QACA,MAAM,QAAQ,CAAC;YACX,qBAAqB;YACrB,mBAAmB;YACnB,sBAAsB,gBAAgB,OAAO,CAAC,IAAI,GAAG;QACzD;QACA,iBAAiB;QACjB;;;SAGC,GACD,IAAI,uBAAuB,iBAAiB;YACxC,oBAAoB,OAAO,GAAG;QAClC;QACA,iBAAiB,OAAO,GAAG;IAC/B;IACA,MAAM,YAAY,cAAc,QAAS,MAAM,OAAO,CAAC,cAAc,UAAU,QAAQ,CAAC;IACxF,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAoB;gBAAE;gBAAW;gBAAU,WAAW;YAAY;SAAE;QAAG,SAAS,qBAAqB,YAAY,YAAY,SAAS;QAAY,eAAe,YAAY,eAAe;QAAY,SAAS,YAAY,SAAS;QAAY,gBAAgB,qBAAqB,YAAY;QAAkB,eAAe,qBAAqB,gBAAgB;QAAiB,eAAe,qBAAqB,gBAAgB;QAAiB,aAAa,qBAAqB,cAAc;QAAW,gBAAgB;QAAkB,KAAK;QAAW,OAAO;QAAgB,UAAU;YAAC;YAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe,CAAC;SAAG;IAAC;AACzpB;AAEA;;;;;CAKC,GACD,SAAS,gBAAgB,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,KAAK,EAAE,OAAO,EAAG;IAC9D,MAAM,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;IAC7G,MAAM,OAAO,WAAW,GAAG,CAAC;IAC5B,IAAI,CAAC,MAAM;QACP,UAAU,OAAO,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;QAC3C;IACJ;IACA,MAAM,QAAQ,CAAC;QAAE,sBAAsB;IAAM;IAC7C,IAAI,CAAC,KAAK,QAAQ,EAAE;QAChB,iBAAiB;YAAC;SAAG;IACzB,OACK,IAAI,YAAa,KAAK,QAAQ,IAAI,sBAAuB;QAC1D,sBAAsB;YAAE,OAAO;gBAAC;aAAK;YAAE,OAAO,EAAE;QAAC;QACjD,sBAAsB,IAAM,SAAS,SAAS;IAClD;AACJ;AAEA;;;;CAIC,GACD,SAAS,QAAQ,EAAE,OAAO,EAAE,WAAW,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAG;IACrH,MAAM,QAAQ;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,OAAO,OAAO,GAAG,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE;YACpB,eAAe,IAAM,MAAM,QAAQ;YACnC,iBAAiB,CAAC;gBACd,gBAAgB;oBACZ;oBACA;oBACA;gBACJ;YACJ;YACA,aAAa;gBACT,YAAY;YAChB;YACA,YAAY;gBACR,YAAY;YAChB;QACJ;IACJ,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,UAAU;YACV,OAAO,OAAO,EAAE;QACpB,OACK,IAAI,QAAQ,OAAO,EAAE;YACtB,OAAO,OAAO,EAAE,OAAO;gBACnB;gBACA;gBACA,SAAS,QAAQ,OAAO;gBACxB;gBACA;gBACA;YACJ;YACA,OAAO;gBACH,OAAO,OAAO,EAAE;YACpB;QACJ;IACJ,GAAG;QAAC;QAAiB;QAAgB;QAAU;QAAc;QAAS;KAAO;IAC7E,OAAO;AACX;AAEA,MAAM,uBAAuB,CAAC,iBAAmB,CAAC,IAAM,EAAE,QAAQ,IAAI,CAAC,EAAE,SAAS,IAAK,kBAAkB,OAAO,EAAE,SAAS,KAAK,WAAY;AAC5I;;;;;CAKC,GACD,SAAS;IACL,MAAM,QAAQ;IACd,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,mBAAmB,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;QACjI,MAAM,cAAc,IAAI;QACxB,MAAM,aAAa,qBAAqB;QACxC;;;SAGC,GACD,MAAM,QAAQ,aAAa,QAAQ,CAAC,EAAE,GAAG;QACzC,MAAM,QAAQ,aAAa,QAAQ,CAAC,EAAE,GAAG;QACzC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,GAAG,QAAQ,OAAO,MAAM;QACxD,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,GAAG,QAAQ,OAAO,MAAM;QACxD,KAAK,MAAM,GAAG,KAAK,IAAI,WAAY;YAC/B,IAAI,CAAC,WAAW,OAAO;gBACnB;YACJ;YACA,IAAI,eAAe;gBACf,GAAG,KAAK,SAAS,CAAC,gBAAgB,CAAC,CAAC,GAAG;gBACvC,GAAG,KAAK,SAAS,CAAC,gBAAgB,CAAC,CAAC,GAAG;YAC3C;YACA,IAAI,YAAY;gBACZ,eAAe,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD,EAAE,cAAc;YAC9C;YACA,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAAE;gBACzD,QAAQ,KAAK,EAAE;gBACf;gBACA;gBACA;gBACA;gBACA;YACJ;YACA,KAAK,QAAQ,GAAG;YAChB,KAAK,SAAS,CAAC,gBAAgB,GAAG;YAClC,YAAY,GAAG,CAAC,KAAK,EAAE,EAAE;QAC7B;QACA,oBAAoB;IACxB,GAAG,EAAE;IACL,OAAO;AACX;AAEA,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACpC,MAAM,WAAW,cAAc,QAAQ;AACvC,cAAc,QAAQ;AACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,MAAM,YAAY;IACd,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC1B,OAAO;AACX;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,gBAAgB,EAAE,cAAc;QAChC,gBAAgB,EAAE,cAAc;QAChC,MAAM,EAAE,IAAI;IAChB,CAAC;AACD,MAAM,qBAAqB,CAAC,QAAQ,UAAU,OAAS,CAAC;QACpD,MAAM,EAAE,4BAA4B,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG;QAChF,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAC1C,MAAM,eAAe,UAAU,WAAW,UAAU,UAAU,OAAO,YAAY,UAAU,SAAS;QACpG,OAAO;YACH,gBAAgB,YAAY,WAAW,UAAU,YAAY,OAAO,YAAY,YAAY,SAAS;YACrG;YACA,iBAAiB,aAAa,WAAW,UAAU,aAAa,OAAO,YAAY,aAAa,SAAS;YACzG,qBAAqB,mBAAmB,0JAAA,CAAA,iBAAc,CAAC,MAAM,GACvD,YAAY,SAAS,OACrB,WAAW,YAAY,UAAU,aAAa,YAAY;YAChE,qBAAqB,CAAC,CAAC;YACvB,0BAA0B,CAAC,CAAC;YAC5B,OAAO,gBAAgB;QAC3B;IACJ;AACA,SAAS,gBAAgB,EAAE,OAAO,QAAQ,EAAE,WAAW,0JAAA,CAAA,WAAQ,CAAC,GAAG,EAAE,iBAAiB,EAAE,gBAAgB,IAAI,EAAE,qBAAqB,IAAI,EAAE,mBAAmB,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,MAAM,EAAE,GAAG;IAC3O,MAAM,WAAW,MAAM;IACvB,MAAM,WAAW,SAAS;IAC1B,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,IAAI,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IAC7E,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,KAAK,EAAG,GAAG,SAAS,mBAAmB,QAAQ,UAAU,OAAO,0IAAA,CAAA,UAAO;IAClM,IAAI,CAAC,QAAQ;QACT,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,0JAAA,CAAA,gBAAa,CAAC,WAAW;IAC/D;IACA,MAAM,oBAAoB,CAAC;QACvB,MAAM,EAAE,kBAAkB,EAAE,WAAW,eAAe,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ;QAC1F,MAAM,aAAa;YACf,GAAG,kBAAkB;YACrB,GAAG,MAAM;QACb;QACA,IAAI,iBAAiB;YACjB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ;YAC1C,SAAS,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QACjC;QACA,kBAAkB;QAClB,YAAY;IAChB;IACA,MAAM,gBAAgB,CAAC;QACnB,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,MAAM,mBAAmB,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD,EAAE,MAAM,WAAW;QACvD,IAAI,sBACA,CAAC,AAAC,oBAAoB,MAAM,MAAM,KAAK,KAAM,CAAC,gBAAgB,GAAG;YACjE,MAAM,eAAe,MAAM,QAAQ;YACnC,0JAAA,CAAA,WAAQ,CAAC,aAAa,CAAC,MAAM,WAAW,EAAE;gBACtC,kBAAkB,aAAa,gBAAgB;gBAC/C,gBAAgB,aAAa,cAAc;gBAC3C,kBAAkB,aAAa,gBAAgB;gBAC/C,SAAS,aAAa,OAAO;gBAC7B,YAAY,aAAa,UAAU;gBACnC,KAAK,aAAa,GAAG;gBACrB;gBACA;gBACA;gBACA,QAAQ,aAAa,IAAI;gBACzB,OAAO,aAAa,KAAK;gBACzB,kBAAkB,aAAa,gBAAgB;gBAC/C,gBAAgB,aAAa,cAAc;gBAC3C,cAAc,aAAa,YAAY;gBACvC,kBAAkB,aAAa,gBAAgB;gBAC/C,WAAW;gBACX,mBAAmB,qBAAqB,aAAa,iBAAiB;gBACtE,cAAc,IAAM,MAAM,QAAQ,GAAG,SAAS;gBAC9C,eAAe,IAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU;gBAC3D,cAAc,aAAa,YAAY;YAC3C;QACJ;QACA,IAAI,kBAAkB;YAClB,cAAc;QAClB,OACK;YACD,eAAe;QACnB;IACJ;IACA,MAAM,UAAU,CAAC;QACb,MAAM,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,0BAA0B,EAAE,cAAc,EAAE,mBAAmB,sBAAsB,EAAE,GAAG,EAAE,MAAM,MAAM,EAAE,UAAU,EAAE,YAAY,eAAe,EAAG,GAAG,MAAM,QAAQ;QACrN,IAAI,CAAC,UAAW,CAAC,8BAA8B,CAAC,oBAAqB;YACjE;QACJ;QACA,IAAI,CAAC,4BAA4B;YAC7B,sBAAsB,MAAM,WAAW,EAAE;gBAAE;gBAAQ;gBAAU,YAAY;YAAK;YAC9E,MAAM,QAAQ,CAAC;gBAAE,4BAA4B;oBAAE;oBAAQ;oBAAM,IAAI;gBAAS;YAAE;YAC5E;QACJ;QACA,MAAM,MAAM,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,MAAM;QAC1C,MAAM,2BAA2B,qBAAqB;QACtD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,0JAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,MAAM,WAAW,EAAE;YAChE,QAAQ;gBACJ;gBACA,IAAI;gBACJ;YACJ;YACA;YACA,YAAY,2BAA2B,MAAM;YAC7C,cAAc,2BAA2B,EAAE,IAAI;YAC/C,UAAU,2BAA2B,IAAI;YACzC,mBAAmB;YACnB;YACA;YACA;YACA;QACJ;QACA,IAAI,WAAW,YAAY;YACvB,kBAAkB;QACtB;QACA,MAAM,kBAAkB,gBAAgB;QACxC,OAAO,gBAAgB,UAAU;QACjC,gBAAgB,UAAU,GAAG,gBAAgB,QAAQ,GAAG,gBAAgB,QAAQ,CAAC,QAAQ,GAAG;QAC5F,oBAAoB,OAAO;QAC3B,MAAM,QAAQ,CAAC;YAAE,4BAA4B;QAAK;IACtD;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,iBAAiB;QAAU,eAAe;QAAQ,kBAAkB;QAAU,WAAW,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAC7J;YACA,CAAC,mBAAmB,EAAE,UAAU;YAChC;YACA;YACA;YACA;gBACI,QAAQ,CAAC;gBACT,QAAQ;gBACR,aAAa;gBACb,kBAAkB;gBAClB,gBAAgB;gBAChB,iBAAiB;gBACjB,gBAAgB;gBAChB,cAAc;gBACd;gBACA;;;iBAGC,GACD,qBAAqB,iBACjB,CAAC,CAAC,uBAAuB,mBAAmB,KAC5C,CAAC,uBAAuB,2BAA2B,mBAAmB,kBAAkB;YAChG;SACH;QAAG,aAAa;QAAe,cAAc;QAAe,SAAS,iBAAiB,UAAU;QAAW,KAAK;QAAK,GAAG,IAAI;QAAE,UAAU;IAAS;AAC1J;AACA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB;AAEpC,SAAS,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,MAAM,EAAE;IACxE,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU;YAAC,MAAM;YAAO,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,MAAM;gBAAU,UAAU;gBAAgB,eAAe;YAAc;SAAG;IAAC;AAC9I;AAEA,SAAS,YAAY,EAAE,IAAI,EAAE,aAAa,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,GAAG,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,MAAM,EAAG;IAC1G,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU;YAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,MAAM;gBAAU,UAAU;gBAAgB,eAAe;YAAc;YAAI,MAAM;YAAO,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,MAAM;gBAAU,UAAU;gBAAgB,eAAe;YAAc;SAAG;IAAC;AACvO;AAEA,SAAS;IACL,OAAO;AACX;AAEA,SAAS,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,GAAG,EAAE;IACtE,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU;YAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,MAAM;gBAAU,UAAU;gBAAgB,eAAe;YAAc;YAAI,MAAM;SAAM;IAAC;AAC9I;AAEA,MAAM,gBAAgB;IAClB,SAAS;QAAE,GAAG;QAAG,GAAG,CAAC;IAAE;IACvB,WAAW;QAAE,GAAG;QAAG,GAAG;IAAE;IACxB,WAAW;QAAE,GAAG,CAAC;QAAG,GAAG;IAAE;IACzB,YAAY;QAAE,GAAG;QAAG,GAAG;IAAE;AAC7B;AACA,MAAM,mBAAmB;IACrB,OAAO;IACP,SAAS;IACT,QAAQ;IACR,OAAO;AACX;AACA,SAAS,6BAA6B,IAAI;IACtC,IAAI,KAAK,SAAS,CAAC,YAAY,KAAK,WAAW;QAC3C,OAAO;YACH,OAAO,KAAK,KAAK,IAAI,KAAK,YAAY,IAAI,KAAK,KAAK,EAAE;YACtD,QAAQ,KAAK,MAAM,IAAI,KAAK,aAAa,IAAI,KAAK,KAAK,EAAE;QAC7D;IACJ;IACA,OAAO;QACH,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;QACjC,QAAQ,KAAK,MAAM,IAAI,KAAK,KAAK,EAAE;IACvC;AACJ;AAEA,MAAM,aAAa,CAAC;IAChB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,EAAE,UAAU,EAAE;QACjE,QAAQ,CAAC,OAAS,CAAC,CAAC,KAAK,QAAQ;IACrC;IACA,OAAO;QACH,OAAO,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ;QAClC,QAAQ,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE,UAAU,SAAS;QACrC,qBAAqB,EAAE,mBAAmB;QAC1C,iBAAiB,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,CAAC;IAC3H;AACJ;AACA,SAAS,eAAe,EAAE,sBAAsB,EAAE,cAAc,EAAE,mBAAmB,EAAG;IACpF,MAAM,QAAQ;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IAC5F,MAAM,oBAAoB;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,qBAAqB;YACtB,QAAQ,OAAO,EAAE,MAAM;gBACnB,eAAe;YACnB;QACJ;IACJ,GAAG;QAAC;KAAoB;IACxB,QAAQ;QACJ;IACJ;IACA,IAAI,uBAAuB,CAAC,SAAS,CAAC,QAAQ;QAC1C,OAAO;IACX;IACA,MAAM,gBAAgB,yBAChB,CAAC;QACC,MAAM,gBAAgB,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ;QACrE,uBAAuB,OAAO;IAClC,IACE;IACN,MAAM,YAAY,CAAC;QACf,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,MAAM,GAAG,GAAG;YAChE,MAAM,cAAc;YACpB,kBAAkB;gBACd,WAAW,aAAa,CAAC,MAAM,GAAG,CAAC;gBACnC,QAAQ,MAAM,QAAQ,GAAG,IAAI;YACjC;QACJ;IACJ;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAA8B;YAAyB;SAAe;QAAG,OAAO;YAC5G,WAAW;QACf;QAAG,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAAE,KAAK;YAAS,WAAW;YAAmC,eAAe;YAAe,UAAU,sBAAsB,YAAY,CAAC;YAAG,WAAW,sBAAsB,YAAY;YAAW,OAAO;gBAC3N;gBACA;YACJ;QAAE;IAAG;AACjB;AAEA,MAAM,MAAM,OAAO,WAAW,cAAc,SAAS;AACrD,MAAM,aAAa,CAAC;IAChB,OAAO;QAAE,sBAAsB,EAAE,oBAAoB;QAAE,qBAAqB,EAAE,mBAAmB;IAAC;AACtG;AACA,SAAS,sBAAsB,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,WAAW,UAAU,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,cAAc,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,oBAAoB,EAAG;IACzpB,MAAM,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,GAAG,SAAS;IAC/D,MAAM,sBAAsB,YAAY,kBAAkB;QAAE,QAAQ;IAAI;IACxE,MAAM,0BAA0B,YAAY,sBAAsB;QAAE,QAAQ;IAAI;IAChF,MAAM,YAAY,2BAA2B;IAC7C,MAAM,cAAc,2BAA2B;IAC/C,MAAM,mBAAmB,mBAAmB,cAAc;IAC1D,MAAM,cAAc,uBAAuB,uBAAuB;IAClE,oBAAoB;QAAE;QAAe;IAAsB;IAC3D,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;QAAE,mBAAmB;QAAmB,oBAAoB;QAAoB,cAAc;QAAc,aAAa;QAAa,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,mBAAmB;QAAmB,WAAW,CAAC,uBAAuB;QAAW,iBAAiB;QAAiB,iBAAiB;QAAiB,SAAS;QAAS,SAAS;QAAS,uBAAuB;QAAuB,kBAAkB;QAAkB,kBAAkB;QAAkB,gBAAgB;QAAgB,kBAAkB;QAAkB,sBAAsB;QAAsB,mBAAmB;QAAmB,UAAU,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,MAAM;YAAE,kBAAkB;YAAkB,gBAAgB;YAAgB,aAAa;YAAa,kBAAkB;YAAkB,iBAAiB;YAAiB,kBAAkB;YAAkB,mBAAmB;YAAmB,cAAc;YAAc,WAAW;YAAW,aAAa,CAAC,CAAC;YAAa,eAAe;YAAe,qBAAqB;YAAqB,iBAAiB;YAAkB,UAAU;gBAAC;gBAAU,wBAAyB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gBAAgB;oBAAE,wBAAwB;oBAAwB,gBAAgB;oBAAgB,qBAAqB;gBAAoB;aAAI;QAAC;IAAG;AAC30C;AACA,sBAAsB,WAAW,GAAG;AACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE1B,MAAM,aAAa,CAAC,oBAAsB,CAAC;QACvC,OAAO,oBACD,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE,EAAE,UAAU,EAAE;YAAE,GAAG;YAAG,GAAG;YAAG,OAAO,EAAE,KAAK;YAAE,QAAQ,EAAE,MAAM;QAAC,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE,IACvH,MAAM,IAAI,CAAC,EAAE,UAAU,CAAC,IAAI;IACtC;AACA;;;;;;CAMC,GACD,SAAS,kBAAkB,iBAAiB;IACxC,MAAM,UAAU,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,WAAW,oBAAoB;QAAC;KAAkB,GAAG,0IAAA,CAAA,UAAO;IACjG,OAAO;AACX;AAEA,MAAM,aAAa,CAAC,IAAM,EAAE,mBAAmB;AAC/C,SAAS;IACL,MAAM,sBAAsB,SAAS;IACrC,MAAM,CAAC,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC9B,IAAI,OAAO,mBAAmB,aAAa;YACvC,OAAO;QACX;QACA,OAAO,IAAI,eAAe,CAAC;YACvB,MAAM,UAAU,IAAI;YACpB,QAAQ,OAAO,CAAC,CAAC;gBACb,MAAM,KAAK,MAAM,MAAM,CAAC,YAAY,CAAC;gBACrC,QAAQ,GAAG,CAAC,IAAI;oBACZ;oBACA,aAAa,MAAM,MAAM;oBACzB,OAAO;gBACX;YACJ;YACA,oBAAoB;QACxB;IACJ;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,OAAO;YACH,gBAAgB;QACpB;IACJ,GAAG;QAAC;KAAe;IACnB,OAAO;AACX;AAEA;;;;;CAKC,GACD,SAAS,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,EAAG;IACvE,MAAM,QAAQ;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,KAAK,cAAc;IACrD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,KAAK,cAAc;IACrD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,gBAAgB,iBAAiB,CAAC,CAAC,KAAK,SAAS,CAAC,YAAY;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,QAAQ,OAAO,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,CAAC,iBAAiB,aAAa,OAAO,KAAK,QAAQ,OAAO,GAAG;YACjG,IAAI,aAAa,OAAO,EAAE;gBACtB,gBAAgB,UAAU,aAAa,OAAO;YAClD;YACA,gBAAgB,QAAQ,QAAQ,OAAO;YACvC,aAAa,OAAO,GAAG,QAAQ,OAAO;QAC1C;IACJ,GAAG;QAAC;QAAe,KAAK,MAAM;KAAC;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,OAAO;YACH,IAAI,aAAa,OAAO,EAAE;gBACtB,gBAAgB,UAAU,aAAa,OAAO;gBAC9C,aAAa,OAAO,GAAG;YAC3B;QACJ;IACJ,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,QAAQ,OAAO,EAAE;YACjB;;;aAGC,GACD,MAAM,cAAc,SAAS,OAAO,KAAK;YACzC,MAAM,mBAAmB,mBAAmB,OAAO,KAAK,KAAK,cAAc;YAC3E,MAAM,mBAAmB,mBAAmB,OAAO,KAAK,KAAK,cAAc;YAC3E,IAAI,eAAe,oBAAoB,kBAAkB;gBACrD,SAAS,OAAO,GAAG;gBACnB,mBAAmB,OAAO,GAAG,KAAK,cAAc;gBAChD,mBAAmB,OAAO,GAAG,KAAK,cAAc;gBAChD,MACK,QAAQ,GACR,mBAAmB,CAAC,IAAI,IAAI;oBAAC;wBAAC,KAAK,EAAE;wBAAE;4BAAE,IAAI,KAAK,EAAE;4BAAE,aAAa,QAAQ,OAAO;4BAAE,OAAO;wBAAK;qBAAE;iBAAC;YAC5G;QACJ;IACJ,GAAG;QAAC,KAAK,EAAE;QAAE;QAAU,KAAK,cAAc;QAAE,KAAK,cAAc;KAAC;IAChE,OAAO;AACX;AAEA,SAAS,YAAY,EAAE,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE,iBAAiB,EAAE,OAAO,EAAG;IACjS,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;QAC5C,MAAM,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC;QAC9B,MAAM,WAAW,EAAE,YAAY,CAAC,GAAG,CAAC;QACpC,OAAO;YACH;YACA,WAAW,KAAK,SAAS;YACzB;QACJ;IACJ,GAAG,0IAAA,CAAA,UAAO;IACV,IAAI,WAAW,KAAK,IAAI,IAAI;IAC5B,IAAI,gBAAgB,WAAW,CAAC,SAAS,IAAI,gBAAgB,CAAC,SAAS;IACvE,IAAI,kBAAkB,WAAW;QAC7B,UAAU,OAAO,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;QAC3C,WAAW;QACX,gBAAgB,iBAAiB,OAAO;IAC5C;IACA,MAAM,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,IAAK,kBAAkB,OAAO,KAAK,SAAS,KAAK,WAAY;IAClG,MAAM,eAAe,CAAC,CAAC,CAAC,KAAK,UAAU,IAAK,sBAAsB,OAAO,KAAK,UAAU,KAAK,WAAY;IACzG,MAAM,gBAAgB,CAAC,CAAC,CAAC,KAAK,WAAW,IAAK,oBAAoB,OAAO,KAAK,WAAW,KAAK,WAAY;IAC1G,MAAM,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,IAAK,kBAAkB,OAAO,KAAK,SAAS,KAAK,WAAY;IAClG,MAAM,QAAQ;IACd,MAAM,gBAAgB,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE;IACxC,MAAM,UAAU,gBAAgB;QAAE;QAAM;QAAU;QAAe;IAAe;IAChF,MAAM,WAAW,QAAQ;QACrB;QACA,UAAU,KAAK,MAAM,IAAI,CAAC;QAC1B;QACA,gBAAgB,KAAK,UAAU;QAC/B,QAAQ;QACR;QACA;IACJ;IACA,MAAM,oBAAoB;IAC1B,IAAI,KAAK,MAAM,EAAE;QACb,OAAO;IACX;IACA,MAAM,iBAAiB,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE;IACzC,MAAM,mBAAmB,6BAA6B;IACtD,MAAM,mBAAmB,gBAAgB,eAAe,WAAW,gBAAgB,eAAe;IAClG,MAAM,sBAAsB,eACtB,CAAC,QAAU,aAAa,OAAO;YAAE,GAAG,UAAU,QAAQ;QAAC,KACvD;IACN,MAAM,qBAAqB,cACrB,CAAC,QAAU,YAAY,OAAO;YAAE,GAAG,UAAU,QAAQ;QAAC,KACtD;IACN,MAAM,sBAAsB,eACtB,CAAC,QAAU,aAAa,OAAO;YAAE,GAAG,UAAU,QAAQ;QAAC,KACvD;IACN,MAAM,uBAAuB,gBACvB,CAAC,QAAU,cAAc,OAAO;YAAE,GAAG,UAAU,QAAQ;QAAC,KACxD;IACN,MAAM,uBAAuB,gBACvB,CAAC,QAAU,cAAc,OAAO;YAAE,GAAG,UAAU,QAAQ;QAAC,KACxD;IACN,MAAM,sBAAsB,CAAC;QACzB,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ;QAC/D,IAAI,gBAAgB,CAAC,CAAC,qBAAqB,CAAC,eAAe,oBAAoB,CAAC,GAAG;YAC/E;;;aAGC,GACD,gBAAgB;gBACZ;gBACA;gBACA;YACJ;QACJ;QACA,IAAI,SAAS;YACT,QAAQ,OAAO;gBAAE,GAAG,UAAU,QAAQ;YAAC;QAC3C;IACJ;IACA,MAAM,YAAY,CAAC;QACf,IAAI,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW,KAAK,qBAAqB;YAC1D;QACJ;QACA,IAAI,0JAAA,CAAA,uBAAoB,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,cAAc;YAC1D,MAAM,WAAW,MAAM,GAAG,KAAK;YAC/B,gBAAgB;gBACZ;gBACA;gBACA;gBACA;YACJ;QACJ,OACK,IAAI,eAAe,KAAK,QAAQ,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,MAAM,GAAG,GAAG;YACrG,2EAA2E;YAC3E,MAAM,cAAc;YACpB,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ;YAC1C,MAAM,QAAQ,CAAC;gBACX,iBAAiB,eAAe,CAAC,uCAAuC,CAAC;oBACrE,WAAW,MAAM,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,WAAW;oBACrD,GAAG,CAAC,CAAC,UAAU,gBAAgB,CAAC,CAAC;oBACjC,GAAG,CAAC,CAAC,UAAU,gBAAgB,CAAC,CAAC;gBACrC;YACJ;YACA,kBAAkB;gBACd,WAAW,aAAa,CAAC,MAAM,GAAG,CAAC;gBACnC,QAAQ,MAAM,QAAQ,GAAG,IAAI;YACjC;QACJ;IACJ;IACA,MAAM,UAAU;QACZ,IAAI,uBAAuB,CAAC,QAAQ,OAAO,EAAE,QAAQ,mBAAmB;YACpE;QACJ;QACA,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,kBAAkB,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ;QAClF,IAAI,CAAC,oBAAoB;YACrB;QACJ;QACA,MAAM,iBAAiB,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,IAAI;YAAC;gBAAC;gBAAI;aAAK;SAAC,GAAG;YAAE,GAAG;YAAG,GAAG;YAAG;YAAO;QAAO,GAAG,WAAW,MAAM,MAAM,GAAG;QACtH,IAAI,CAAC,gBAAgB;YACjB,UAAU,KAAK,QAAQ,CAAC,CAAC,GAAG,eAAe,KAAK,GAAG,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG,eAAe,MAAM,GAAG,GAAG;gBAC/F,MAAM,SAAS,CAAC,EAAE;YACtB;QACJ;IACJ;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAC3B;YACA,CAAC,iBAAiB,EAAE,UAAU;YAC9B;gBACI,0DAA0D;gBAC1D,CAAC,eAAe,EAAE;YACtB;YACA,KAAK,SAAS;YACd;gBACI,UAAU,KAAK,QAAQ;gBACvB,YAAY;gBACZ,QAAQ;gBACR,WAAW;gBACX;YACJ;SACH;QAAG,KAAK;QAAS,OAAO;YACrB,QAAQ,UAAU,CAAC;YACnB,WAAW,CAAC,UAAU,EAAE,UAAU,gBAAgB,CAAC,CAAC,CAAC,GAAG,EAAE,UAAU,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC;YAC3F,eAAe,mBAAmB,QAAQ;YAC1C,YAAY,gBAAgB,YAAY;YACxC,GAAG,KAAK,KAAK;YACb,GAAG,gBAAgB;QACvB;QAAG,WAAW;QAAI,eAAe,CAAC,SAAS,EAAE,IAAI;QAAE,cAAc;QAAqB,aAAa;QAAoB,cAAc;QAAqB,eAAe;QAAsB,SAAS;QAAqB,eAAe;QAAsB,WAAW,cAAc,YAAY;QAAW,UAAU,cAAc,IAAI;QAAW,SAAS,cAAc,UAAU;QAAW,MAAM,KAAK,QAAQ,IAAI,CAAC,cAAc,UAAU,SAAS;QAAG,wBAAwB;QAAQ,oBAAoB,sBAAsB,YAAY,GAAG,mBAAmB,CAAC,EAAE,MAAM;QAAE,cAAc,KAAK,SAAS;QAAE,GAAG,KAAK,aAAa;QAAE,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;YAAE,OAAO;YAAI,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;gBAAE,IAAI;gBAAI,MAAM,KAAK,IAAI;gBAAE,MAAM;gBAAU,mBAAmB,UAAU,gBAAgB,CAAC,CAAC;gBAAE,mBAAmB,UAAU,gBAAgB,CAAC,CAAC;gBAAE,UAAU,KAAK,QAAQ,IAAI;gBAAO,YAAY;gBAAc,WAAW;gBAAa,WAAW,KAAK,SAAS,IAAI;gBAAM,eAAe;gBAAe,gBAAgB,KAAK,cAAc;gBAAE,gBAAgB,KAAK,cAAc;gBAAE,UAAU;gBAAU,YAAY,KAAK,UAAU;gBAAE,QAAQ,UAAU,CAAC;gBAAE,UAAU,KAAK,QAAQ;gBAAE,GAAG,cAAc;YAAC;QAAG;IAAG;AAC1pC;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,gBAAgB,EAAE,cAAc;QAChC,kBAAkB,EAAE,gBAAgB;QACpC,gBAAgB,EAAE,cAAc;QAChC,oBAAoB,EAAE,kBAAkB;QACxC,SAAS,EAAE,OAAO;IACtB,CAAC;AACD,SAAS,sBAAsB,KAAK;IAChC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,OAAO,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IACtH,MAAM,UAAU,kBAAkB,MAAM,yBAAyB;IACjE,MAAM,iBAAiB;IACvB,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW;QAAqB,OAAO;QAAgB,UAAU,QAAQ,GAAG,CAAC,CAAC;YAC3F,OACA;;;;;;;;;;;;;;;;;;;;;;;;aAwBC,GACD,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,aAAa;gBAAE,IAAI;gBAAQ,WAAW,MAAM,SAAS;gBAAE,YAAY,MAAM,UAAU;gBAAE,SAAS,MAAM,WAAW;gBAAE,cAAc,MAAM,gBAAgB;gBAAE,aAAa,MAAM,eAAe;gBAAE,cAAc,MAAM,gBAAgB;gBAAE,eAAe,MAAM,iBAAiB;gBAAE,eAAe,MAAM,iBAAiB;gBAAE,iBAAiB,MAAM,eAAe;gBAAE,gBAAgB,MAAM,cAAc;gBAAE,MAAM,MAAM,IAAI;gBAAE,qBAAqB,MAAM,mBAAmB;gBAAE,gBAAgB;gBAAgB,gBAAgB;gBAAgB,kBAAkB;gBAAkB,gBAAgB;gBAAgB,oBAAoB;gBAAoB,mBAAmB,MAAM,iBAAiB;gBAAE,SAAS;YAAQ,GAAG;QACrrB;IAAG;AACX;AACA,sBAAsB,WAAW,GAAG;AACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE1B;;;;;;CAMC,GACD,SAAS,kBAAkB,iBAAiB;IACxC,MAAM,UAAU,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,IAAI,CAAC,mBAAmB;YACpB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;QACxC;QACA,MAAM,iBAAiB,EAAE;QACzB,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE;YACrB,KAAK,MAAM,QAAQ,EAAE,KAAK,CAAE;gBACxB,MAAM,aAAa,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM;gBAC/C,MAAM,aAAa,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM;gBAC/C,IAAI,cACA,cACA,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD,EAAE;oBACV;oBACA;oBACA,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,MAAM;oBAChB,WAAW,EAAE,SAAS;gBAC1B,IAAI;oBACJ,eAAe,IAAI,CAAC,KAAK,EAAE;gBAC/B;YACJ;QACJ;QACA,OAAO;IACX,GAAG;QAAC;KAAkB,GAAG,0IAAA,CAAA,UAAO;IAChC,OAAO;AACX;AAEA,MAAM,cAAc,CAAC,EAAE,QAAQ,MAAM,EAAE,cAAc,CAAC,EAAE;IACpD,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,YAAY;QAAE,OAAO;YACzB,QAAQ;YACR;QACJ;QAAG,eAAe;QAAS,gBAAgB;QAAS,MAAM;QAAQ,QAAQ;IAAiB;AACnG;AACA,MAAM,oBAAoB,CAAC,EAAE,QAAQ,MAAM,EAAE,cAAc,CAAC,EAAE;IAC1D,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,YAAY;QAAE,OAAO;YACzB,QAAQ;YACR,MAAM;YACN;QACJ;QAAG,eAAe;QAAS,gBAAgB;QAAS,QAAQ;IAAuB;AAC3F;AACA,MAAM,gBAAgB;IAClB,CAAC,0JAAA,CAAA,aAAU,CAAC,KAAK,CAAC,EAAE;IACpB,CAAC,0JAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;AAC9B;AACA,SAAS,gBAAgB,IAAI;IACzB,MAAM,QAAQ;IACd,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnB,MAAM,eAAe,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe;QACzE,IAAI,CAAC,cAAc;YACf,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;YAC5D,OAAO;QACX;QACA,OAAO,aAAa,CAAC,KAAK;IAC9B,GAAG;QAAC;KAAK;IACT,OAAO;AACX;AAEA,MAAM,SAAS,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,IAAI,EAAE,SAAS,IAAI,EAAE,cAAc,aAAa,EAAE,WAAW,EAAE,SAAS,oBAAoB,EAAG;IACtI,MAAM,SAAS,gBAAgB;IAC/B,IAAI,CAAC,QAAQ;QACT,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;QAAE,WAAW;QAAyB,IAAI;QAAI,aAAa,GAAG,OAAO;QAAE,cAAc,GAAG,QAAQ;QAAE,SAAS;QAAiB,aAAa;QAAa,QAAQ;QAAQ,MAAM;QAAK,MAAM;QAAK,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,OAAO;YAAO,aAAa;QAAY;IAAG;AACxR;AACA;;;;CAIC,GACD,MAAM,oBAAoB,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE;IAC7C,MAAM,QAAQ,SAAS,CAAC,IAAM,EAAE,KAAK;IACrC,MAAM,qBAAqB,SAAS,CAAC,IAAM,EAAE,kBAAkB;IAC/D,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,UAAU,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YACnC,IAAI;YACJ;YACA,oBAAoB,oBAAoB;YACxC,kBAAkB,oBAAoB;QAC1C;QACA,OAAO;IACX,GAAG;QAAC;QAAO;QAAoB;QAAM;KAAa;IAClD,IAAI,CAAC,QAAQ,MAAM,EAAE;QACjB,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW;QAAsB,eAAe;QAAQ,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,UAAU,QAAQ,GAAG,CAAC,CAAC,SAAY,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;oBAAE,IAAI,OAAO,EAAE;oBAAE,MAAM,OAAO,IAAI;oBAAE,OAAO,OAAO,KAAK;oBAAE,OAAO,OAAO,KAAK;oBAAE,QAAQ,OAAO,MAAM;oBAAE,aAAa,OAAO,WAAW;oBAAE,aAAa,OAAO,WAAW;oBAAE,QAAQ,OAAO,MAAM;gBAAC,GAAG,OAAO,EAAE;QAAI;IAAG;AACxW;AACA,kBAAkB,WAAW,GAAG;AAChC,IAAI,sBAAsB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE/B,SAAS,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,cAAc,IAAI,EAAE,YAAY,EAAE,iBAAiB;IAAC;IAAG;CAAE,EAAE,sBAAsB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,MAAM;IACpK,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;QAAG,OAAO;QAAG,QAAQ;IAAE;IACnF,MAAM,kBAAkB,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;QAAC;QAAgC;KAAU;IACtE,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,YAAY,OAAO,EAAE;YACrB,MAAM,WAAW,YAAY,OAAO,CAAC,OAAO;YAC5C,gBAAgB;gBACZ,GAAG,SAAS,CAAC;gBACb,GAAG,SAAS,CAAC;gBACb,OAAO,SAAS,KAAK;gBACrB,QAAQ,SAAS,MAAM;YAC3B;QACJ;IACJ,GAAG;QAAC;KAAM;IACV,IAAI,CAAC,OAAO;QACR,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,KAAK;QAAE,WAAW,CAAC,UAAU,EAAE,IAAI,aAAa,KAAK,GAAG,EAAE,CAAC,EAAE,IAAI,aAAa,MAAM,GAAG,EAAE,CAAC,CAAC;QAAE,WAAW;QAAiB,YAAY,aAAa,KAAK,GAAG,YAAY;QAAU,GAAG,IAAI;QAAE,UAAU;YAAC,eAAgB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,OAAO,aAAa,KAAK,GAAG,IAAI,cAAc,CAAC,EAAE;gBAAE,GAAG,CAAC,cAAc,CAAC,EAAE;gBAAE,GAAG,CAAC,cAAc,CAAC,EAAE;gBAAE,QAAQ,aAAa,MAAM,GAAG,IAAI,cAAc,CAAC,EAAE;gBAAE,WAAW;gBAA2B,OAAO;gBAAc,IAAI;gBAAqB,IAAI;YAAoB;YAAK,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,WAAW;gBAAyB,GAAG,aAAa,MAAM,GAAG;gBAAG,IAAI;gBAAS,KAAK;gBAAa,OAAO;gBAAY,UAAU;YAAM;YAAI;SAAS;IAAC;AACxpB;AACA,kBAAkB,WAAW,GAAG;AAChC;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEtB;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,EAAE,GAAG,OAAO;IAC1J,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU;YAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG,KAAK;gBAAE,GAAG;gBAAM,MAAM;gBAAQ,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;oBAAC;oBAAyB,MAAM,SAAS;iBAAC;YAAE;YAAI,oBAAqB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG;gBAAM,MAAM;gBAAQ,eAAe;gBAAG,aAAa;gBAAkB,WAAW;YAA+B;YAAK,SAAS,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE,WAAW,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE,UAAW,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;gBAAE,GAAG;gBAAQ,GAAG;gBAAQ,OAAO;gBAAO,YAAY;gBAAY,aAAa;gBAAa,cAAc;gBAAc,gBAAgB;gBAAgB,qBAAqB;YAAoB,KAAM;SAAK;IAAC;AACzjB;AAEA,SAAS,WAAW,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACvC,IAAI,QAAQ,0JAAA,CAAA,WAAQ,CAAC,IAAI,IAAI,QAAQ,0JAAA,CAAA,WAAQ,CAAC,KAAK,EAAE;QACjD,OAAO;YAAC,MAAM,CAAC,KAAK,EAAE;YAAG;SAAG;IAChC;IACA,OAAO;QAAC;QAAI,MAAM,CAAC,KAAK,EAAE;KAAE;AAChC;AACA;;;;;;;;;;;;CAYC,GACD,SAAS,oBAAoB,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,GAAG,EAAG;IACjI,MAAM,CAAC,gBAAgB,eAAe,GAAG,WAAW;QAChD,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IACA,MAAM,CAAC,gBAAgB,eAAe,GAAG,WAAW;QAChD,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IACA,MAAM,CAAC,QAAQ,QAAQ,SAAS,QAAQ,GAAG,CAAA,GAAA,0JAAA,CAAA,sBAAmB,AAAD,EAAE;QAC3D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;IACA,OAAO;QACH,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS;QACvH;QACA;QACA;QACA;KACH;AACL;AACA,SAAS,uBAAuB,MAAM;IAClC,8CAA8C;IAC9C,OAAO,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAG;QACxN,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,oBAAoB;YAC/C;YACA;YACA;YACA;YACA;YACA;QACJ;QACA,MAAM,MAAM,OAAO,UAAU,GAAG,YAAY;QAC5C,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;YAAE,IAAI;YAAK,MAAM;YAAM,QAAQ;YAAQ,QAAQ;YAAQ,OAAO;YAAO,YAAY;YAAY,aAAa;YAAa,cAAc;YAAc,gBAAgB;YAAgB,qBAAqB;YAAqB,OAAO;YAAO,WAAW;YAAW,aAAa;YAAa,kBAAkB;QAAiB;IACxV;AACJ;AACA,MAAM,mBAAmB,uBAAuB;IAAE,YAAY;AAAM;AACpE,MAAM,2BAA2B,uBAAuB;IAAE,YAAY;AAAK;AAC3E,iBAAiB,WAAW,GAAG;AAC/B,yBAAyB,WAAW,GAAG;AAEvC,SAAS,qBAAqB,MAAM;IAChC,8CAA8C;IAC9C,OAAO,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,MAAM,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAG;QACtQ,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE;YAC7C;YACA;YACA;YACA;YACA;YACA;YACA,cAAc,aAAa;YAC3B,QAAQ,aAAa;QACzB;QACA,MAAM,MAAM,OAAO,UAAU,GAAG,YAAY;QAC5C,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;YAAE,IAAI;YAAK,MAAM;YAAM,QAAQ;YAAQ,QAAQ;YAAQ,OAAO;YAAO,YAAY;YAAY,aAAa;YAAa,cAAc;YAAc,gBAAgB;YAAgB,qBAAqB;YAAqB,OAAO;YAAO,WAAW;YAAW,aAAa;YAAa,kBAAkB;QAAiB;IACxV;AACJ;AACA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,MAAM,iBAAiB,qBAAqB;IAAE,YAAY;AAAM;AAChE;;CAEC,GACD,MAAM,yBAAyB,qBAAqB;IAAE,YAAY;AAAK;AACvE,eAAe,WAAW,GAAG;AAC7B,uBAAuB,WAAW,GAAG;AAErC,SAAS,eAAe,MAAM;IAC1B,8CAA8C;IAC9C,OAAO,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO;QACzB,MAAM,MAAM,OAAO,UAAU,GAAG,YAAY;QAC5C,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gBAAgB;YAAE,GAAG,KAAK;YAAE,IAAI;YAAK,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;oBAAE,cAAc;oBAAG,QAAQ,MAAM,WAAW,EAAE;gBAAO,CAAC,GAAG;gBAAC,MAAM,WAAW,EAAE;aAAO;QAAE;IACvK;AACJ;AACA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,MAAM,WAAW,eAAe;IAAE,YAAY;AAAM;AACpD;;CAEC,GACD,MAAM,mBAAmB,eAAe;IAAE,YAAY;AAAK;AAC3D,SAAS,WAAW,GAAG;AACvB,iBAAiB,WAAW,GAAG;AAE/B,SAAS,mBAAmB,MAAM;IAC9B,8CAA8C;IAC9C,OAAO,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAG;QACxL,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE;YAAE;YAAS;YAAS;YAAS;QAAQ;QACpF,MAAM,MAAM,OAAO,UAAU,GAAG,YAAY;QAC5C,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;YAAE,IAAI;YAAK,MAAM;YAAM,QAAQ;YAAQ,QAAQ;YAAQ,OAAO;YAAO,YAAY;YAAY,aAAa;YAAa,cAAc;YAAc,gBAAgB;YAAgB,qBAAqB;YAAqB,OAAO;YAAO,WAAW;YAAW,aAAa;YAAa,kBAAkB;QAAiB;IACxV;AACJ;AACA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,MAAM,eAAe,mBAAmB;IAAE,YAAY;AAAM;AAC5D;;CAEC,GACD,MAAM,uBAAuB,mBAAmB;IAAE,YAAY;AAAK;AACnE,aAAa,WAAW,GAAG;AAC3B,qBAAqB,WAAW,GAAG;AAEnC,SAAS,iBAAiB,MAAM;IAC5B,8CAA8C;IAC9C,OAAO,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,MAAM,EAAE,iBAAiB,0JAAA,CAAA,WAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAG;QACtQ,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD,EAAE;YACzC;YACA;YACA;YACA;YACA;YACA;YACA,WAAW,aAAa;QAC5B;QACA,MAAM,MAAM,OAAO,UAAU,GAAG,YAAY;QAC5C,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;YAAE,IAAI;YAAK,MAAM;YAAM,QAAQ;YAAQ,QAAQ;YAAQ,OAAO;YAAO,YAAY;YAAY,aAAa;YAAa,cAAc;YAAc,gBAAgB;YAAgB,qBAAqB;YAAqB,OAAO;YAAO,WAAW;YAAW,aAAa;YAAa,kBAAkB;QAAiB;IACxV;AACJ;AACA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,MAAM,aAAa,iBAAiB;IAAE,YAAY;AAAM;AACxD;;CAEC,GACD,MAAM,qBAAqB,iBAAiB;IAAE,YAAY;AAAK;AAC/D,WAAW,WAAW,GAAG;AACzB,mBAAmB,WAAW,GAAG;AAEjC,MAAM,mBAAmB;IACrB,SAAS;IACT,UAAU;IACV,MAAM;IACN,YAAY;IACZ,cAAc;AAClB;AACA,MAAM,eAAe;IACjB,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,gBAAgB;IAChB,gBAAgB;AACpB;AAEA,MAAM,SAAS,CAAC,GAAG,OAAO;IACtB,IAAI,aAAa,0JAAA,CAAA,WAAQ,CAAC,IAAI,EAC1B,OAAO,IAAI;IACf,IAAI,aAAa,0JAAA,CAAA,WAAQ,CAAC,KAAK,EAC3B,OAAO,IAAI;IACf,OAAO;AACX;AACA,MAAM,SAAS,CAAC,GAAG,OAAO;IACtB,IAAI,aAAa,0JAAA,CAAA,WAAQ,CAAC,GAAG,EACzB,OAAO,IAAI;IACf,IAAI,aAAa,0JAAA,CAAA,WAAQ,CAAC,MAAM,EAC5B,OAAO,IAAI;IACf,OAAO;AACX;AACA,MAAM,uBAAuB;AAC7B;;CAEC,GACD,SAAS,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAG;IACzG,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;QAAE,aAAa;QAAa,cAAc;QAAc,YAAY;QAAY,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAsB,GAAG,qBAAqB,CAAC,EAAE,MAAM;SAAC;QAAG,IAAI,OAAO,SAAS,QAAQ;QAAW,IAAI,OAAO,SAAS,QAAQ;QAAW,GAAG;QAAQ,QAAQ;QAAe,MAAM;IAAc;AACxT;AAEA,SAAS,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc,EAAG;IACtN,MAAM,QAAQ;IACd,MAAM,oBAAoB,CAAC,OAAO;QAC9B,yDAAyD;QACzD,IAAI,MAAM,MAAM,KAAK,GAAG;YACpB;QACJ;QACA,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,iBAAiB,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG,EAAE,cAAc,EAAE,YAAY,EAAE,gBAAgB,EAAE,UAAU,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAG,GAAG,MAAM,QAAQ;QAClN,MAAM,WAAW,eAAe,IAAI,KAAK;QACzC,gBAAgB;QAChB,mBAAmB,OAAO,MAAM,eAAe,IAAI;QACnD,MAAM,kBAAkB,CAAC,KAAK;YAC1B,gBAAgB;YAChB,iBAAiB,KAAK,MAAM,eAAe,IAAI,EAAE;QACrD;QACA,MAAM,gBAAgB,CAAC,aAAe,cAAc,MAAM;QAC1D,0JAAA,CAAA,WAAQ,CAAC,aAAa,CAAC,MAAM,WAAW,EAAE;YACtC;YACA;YACA;YACA;YACA,UAAU,eAAe,EAAE;YAC3B,QAAQ,eAAe,MAAM;YAC7B;YACA;YACA,iBAAiB,eAAe,IAAI;YACpC;YACA;YACA;YACA;YACA;YACA,WAAW;YACX;YACA;YACA,gBAAgB;YAChB;YACA,cAAc,IAAM,MAAM,QAAQ,GAAG,SAAS;YAC9C,eAAe,IAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU;QAC/D;IACJ;IACA,MAAM,6BAA6B,CAAC,QAAU,kBAAkB,OAAO;YAAE,QAAQ,KAAK,MAAM;YAAE,IAAI,KAAK,YAAY,IAAI;YAAM,MAAM;QAAS;IAC5I,MAAM,6BAA6B,CAAC,QAAU,kBAAkB,OAAO;YAAE,QAAQ,KAAK,MAAM;YAAE,IAAI,KAAK,YAAY,IAAI;YAAM,MAAM;QAAS;IAC5I,MAAM,wBAAwB,IAAM,eAAe;IACnD,MAAM,sBAAsB,IAAM,eAAe;IACjD,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU;YAAC,CAAC,oBAAoB,QAAQ,oBAAoB,QAAQ,KAAM,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,YAAY;gBAAE,UAAU;gBAAgB,SAAS;gBAAS,SAAS;gBAAS,QAAQ;gBAAiB,aAAa;gBAA4B,cAAc;gBAAuB,YAAY;gBAAqB,MAAM;YAAS;YAAK,CAAC,oBAAoB,QAAQ,oBAAoB,QAAQ,KAAM,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,YAAY;gBAAE,UAAU;gBAAgB,SAAS;gBAAS,SAAS;gBAAS,QAAQ;gBAAiB,aAAa;gBAA4B,cAAc;gBAAuB,YAAY;gBAAqB,MAAM;YAAS;SAAI;IAAC;AAC9nB;AAEA,SAAS,YAAY,EAAE,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,mBAAmB,EAAG;IAC/R,IAAI,OAAO,SAAS,CAAC,IAAM,EAAE,UAAU,CAAC,GAAG,CAAC;IAC5C,MAAM,qBAAqB,SAAS,CAAC,IAAM,EAAE,kBAAkB;IAC/D,OAAO,qBAAqB;QAAE,GAAG,kBAAkB;QAAE,GAAG,IAAI;IAAC,IAAI;IACjE,IAAI,WAAW,KAAK,IAAI,IAAI;IAC5B,IAAI,gBAAgB,WAAW,CAAC,SAAS,IAAI,gBAAgB,CAAC,SAAS;IACvE,IAAI,kBAAkB,WAAW;QAC7B,UAAU,OAAO,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;QAC3C,WAAW;QACX,gBAAgB,iBAAiB,OAAO;IAC5C;IACA,MAAM,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,IAAK,kBAAkB,OAAO,KAAK,SAAS,KAAK,WAAY;IAClG,MAAM,kBAAkB,OAAO,gBAAgB,eAC3C,CAAC,KAAK,aAAa,IAAK,sBAAsB,OAAO,KAAK,aAAa,KAAK,WAAY;IAC5F,MAAM,eAAe,CAAC,CAAC,CAAC,KAAK,UAAU,IAAK,sBAAsB,OAAO,KAAK,UAAU,KAAK,WAAY;IACzG,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,QAAQ;IACd,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzG,MAAM,aAAa,MAAM,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM;QACnD,MAAM,aAAa,MAAM,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM;QACnD,IAAI,CAAC,cAAc,CAAC,YAAY;YAC5B,OAAO;gBACH,QAAQ,KAAK,MAAM;gBACnB,GAAG,YAAY;YACnB;QACJ;QACA,MAAM,eAAe,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE;YACjC;YACA;YACA;YACA,cAAc,KAAK,YAAY,IAAI;YACnC,cAAc,KAAK,YAAY,IAAI;YACnC,gBAAgB,MAAM,cAAc;YACpC;QACJ;QACA,MAAM,SAAS,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAAE;YACjC,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,MAAM;YACnB;YACA;YACA,iBAAiB,MAAM,oBAAoB;QAC/C;QACA,OAAO;YACH;YACA,GAAI,gBAAgB,YAAY;QACpC;IACJ,GAAG;QAAC,KAAK,MAAM;QAAE,KAAK,MAAM;QAAE,KAAK,YAAY;QAAE,KAAK,YAAY;QAAE,KAAK,QAAQ;QAAE,KAAK,MAAM;KAAC,GAAG,0IAAA,CAAA,UAAO;IACzG,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAO,KAAK,WAAW,GAAG,CAAC,MAAM,EAAE,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE,KAAK,WAAW,EAAE,MAAM,EAAE,CAAC,GAAG,WAAY;QAAC,KAAK,WAAW;QAAE;KAAK;IAChJ,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAO,KAAK,SAAS,GAAG,CAAC,MAAM,EAAE,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS,EAAE,MAAM,EAAE,CAAC,GAAG,WAAY;QAAC,KAAK,SAAS;QAAE;KAAK;IACxI,IAAI,KAAK,MAAM,IAAI,YAAY,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YAAY,MAAM;QAC7F,OAAO;IACX;IACA,MAAM,cAAc,CAAC;QACjB,MAAM,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,GAAG,MAAM,QAAQ;QACxF,IAAI,cAAc;YACd,MAAM,QAAQ,CAAC;gBAAE,sBAAsB;YAAM;YAC7C,IAAI,KAAK,QAAQ,IAAI,sBAAsB;gBACvC,sBAAsB;oBAAE,OAAO,EAAE;oBAAE,OAAO;wBAAC;qBAAK;gBAAC;gBACjD,QAAQ,OAAO,EAAE;YACrB,OACK;gBACD,iBAAiB;oBAAC;iBAAG;YACzB;QACJ;QACA,IAAI,SAAS;YACT,QAAQ,OAAO;QACnB;IACJ;IACA,MAAM,oBAAoB,gBACpB,CAAC;QACC,cAAc,OAAO;YAAE,GAAG,IAAI;QAAC;IACnC,IACE;IACN,MAAM,oBAAoB,gBACpB,CAAC;QACC,cAAc,OAAO;YAAE,GAAG,IAAI;QAAC;IACnC,IACE;IACN,MAAM,mBAAmB,eACnB,CAAC;QACC,aAAa,OAAO;YAAE,GAAG,IAAI;QAAC;IAClC,IACE;IACN,MAAM,kBAAkB,cAClB,CAAC;QACC,YAAY,OAAO;YAAE,GAAG,IAAI;QAAC;IACjC,IACE;IACN,MAAM,mBAAmB,eACnB,CAAC;QACC,aAAa,OAAO;YAAE,GAAG,IAAI;QAAC;IAClC,IACE;IACN,MAAM,YAAY,CAAC;QACf,IAAI,CAAC,uBAAuB,0JAAA,CAAA,uBAAoB,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,cAAc;YAClF,MAAM,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,GAAG,MAAM,QAAQ;YAClE,MAAM,WAAW,MAAM,GAAG,KAAK;YAC/B,IAAI,UAAU;gBACV,QAAQ,OAAO,EAAE;gBACjB,sBAAsB;oBAAE,OAAO;wBAAC;qBAAK;gBAAC;YAC1C,OACK;gBACD,iBAAiB;oBAAC;iBAAG;YACzB;QACJ;IACJ;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,OAAO;YAAE;QAAO;QAAG,UAAU,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,KAAK;YAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;gBAChE;gBACA,CAAC,iBAAiB,EAAE,UAAU;gBAC9B,KAAK,SAAS;gBACd;gBACA;oBACI,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,UAAU,CAAC,gBAAgB,CAAC;oBAC5B,UAAU;oBACV,YAAY;gBAChB;aACH;YAAG,SAAS;YAAa,eAAe;YAAmB,eAAe;YAAmB,cAAc;YAAkB,aAAa;YAAiB,cAAc;YAAkB,WAAW,cAAc,YAAY;YAAW,UAAU,cAAc,IAAI;YAAW,MAAM,KAAK,QAAQ,IAAI,CAAC,cAAc,UAAU,KAAK;YAAG,wBAAwB;YAAQ,WAAW;YAAI,eAAe,CAAC,SAAS,EAAE,IAAI;YAAE,cAAc,KAAK,SAAS,KAAK,OAAO,YAAY,KAAK,SAAS,IAAI,CAAC,UAAU,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;YAAE,oBAAoB,cAAc,GAAG,mBAAmB,CAAC,EAAE,MAAM,GAAG;YAAW,KAAK;YAAS,GAAG,KAAK,aAAa;YAAE,UAAU;gBAAC,CAAC,gBAAiB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;oBAAE,IAAI;oBAAI,QAAQ,KAAK,MAAM;oBAAE,QAAQ,KAAK,MAAM;oBAAE,MAAM,KAAK,IAAI;oBAAE,UAAU,KAAK,QAAQ;oBAAE,UAAU,KAAK,QAAQ;oBAAE,YAAY;oBAAc,WAAW,KAAK,SAAS,IAAI;oBAAM,OAAO,KAAK,KAAK;oBAAE,YAAY,KAAK,UAAU;oBAAE,aAAa,KAAK,WAAW;oBAAE,cAAc,KAAK,YAAY;oBAAE,gBAAgB,KAAK,cAAc;oBAAE,qBAAqB,KAAK,mBAAmB;oBAAE,SAAS;oBAAS,SAAS;oBAAS,SAAS;oBAAS,SAAS;oBAAS,gBAAgB;oBAAgB,gBAAgB;oBAAgB,MAAM,KAAK,IAAI;oBAAE,OAAO,KAAK,KAAK;oBAAE,gBAAgB,KAAK,YAAY;oBAAE,gBAAgB,KAAK,YAAY;oBAAE,aAAa;oBAAgB,WAAW;oBAAc,aAAa,iBAAiB,OAAO,KAAK,WAAW,GAAG;oBAAW,kBAAkB,KAAK,gBAAgB;gBAAC;gBAAK,mBAAoB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,mBAAmB;oBAAE,MAAM;oBAAM,iBAAiB;oBAAiB,iBAAiB;oBAAiB,aAAa;oBAAa,kBAAkB;oBAAkB,gBAAgB;oBAAgB,SAAS;oBAAS,SAAS;oBAAS,SAAS;oBAAS,SAAS;oBAAS,gBAAgB;oBAAgB,gBAAgB;oBAAgB,gBAAgB;oBAAgB,iBAAiB;gBAAgB;aAAI;QAAC;IAAG;AAC33D;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,gBAAgB,EAAE,cAAc;QAChC,oBAAoB,EAAE,kBAAkB;QACxC,oBAAoB,EAAE,kBAAkB;QACxC,gBAAgB,EAAE,cAAc;QAChC,SAAS,EAAE,OAAO;IACtB,CAAC;AACD,SAAS,sBAAsB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,cAAc,EAAE,mBAAmB,EAAG;IAC3S,MAAM,EAAE,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IACxG,MAAM,UAAU,kBAAkB;IAClC,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,WAAW;QAAqB,UAAU;YAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,qBAAqB;gBAAE,cAAc;gBAAoB,MAAM;YAAK;YAAI,QAAQ,GAAG,CAAC,CAAC;gBAC9I,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,aAAa;oBAAE,IAAI;oBAAI,gBAAgB;oBAAgB,oBAAoB;oBAAoB,oBAAoB;oBAAoB,gBAAgB;oBAAgB,aAAa;oBAAa,eAAe;oBAAmB,cAAc;oBAAkB,aAAa;oBAAiB,cAAc;oBAAkB,SAAS;oBAAa,iBAAiB;oBAAiB,eAAe;oBAAmB,kBAAkB;oBAAkB,gBAAgB;oBAAgB,MAAM;oBAAM,SAAS;oBAAS,WAAW;oBAAW,qBAAqB;gBAAoB,GAAG;YAC/kB;SAAG;IAAC;AAChB;AACA,sBAAsB,WAAW,GAAG;AACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE1B,MAAM,aAAa,CAAC,IAAM,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACvG,SAAS,SAAS,EAAE,QAAQ,EAAE;IAC1B,MAAM,YAAY,SAAS;IAC3B,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW;QAA+D,OAAO;YAAE;QAAU;QAAG,UAAU;IAAS;AAC5I;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,MAAM;IAC5B,MAAM,aAAa;IACnB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,cAAc,OAAO,IAAI,WAAW,mBAAmB,IAAI,QAAQ;YACpE,WAAW,IAAM,OAAO,aAAa;YACrC,cAAc,OAAO,GAAG;QAC5B;IACJ,GAAG;QAAC;QAAQ,WAAW,mBAAmB;KAAC;AAC/C;AAEA,MAAM,aAAa,CAAC,QAAU,MAAM,OAAO,EAAE;AAC7C;;;;;CAKC,GACD,SAAS,gBAAgB,QAAQ;IAC7B,MAAM,eAAe,SAAS;IAC9B,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,UAAU;YACV,eAAe;YACf,MAAM,QAAQ,CAAC;gBAAE,WAAW;oBAAC,SAAS,CAAC;oBAAE,SAAS,CAAC;oBAAE,SAAS,IAAI;iBAAC;YAAC;QACxE;IACJ,GAAG;QAAC;QAAU;KAAa;IAC3B,OAAO;AACX;AAEA,SAAS,gBAAgB,CAAC;IACtB,OAAO,EAAE,UAAU,CAAC,UAAU,GACxB;QAAE,GAAG,EAAE,UAAU;QAAE,IAAI,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,SAAS;IAAE,IAC1E;QAAE,GAAG,EAAE,UAAU;IAAC;AAC5B;AACA,SAAS,YAAY,kBAAkB;IACnC,IAAI,oBAAoB;QACpB,MAAM,mBAAmB,CAAC;YACtB,MAAM,aAAa,gBAAgB;YACnC,OAAO,mBAAmB;QAC9B;QACA,OAAO;IACX;IACA,OAAO;AACX;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,SAAS,cAAc,kBAAkB;IACrC,MAAM,mBAAmB,YAAY;IACrC,OAAO,SAAS,kBAAkB,0IAAA,CAAA,UAAO;AAC7C;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,kBAAkB,EAAE,gBAAgB;QACpC,SAAS,EAAE,UAAU,CAAC,OAAO;QAC7B,YAAY,EAAE,UAAU,CAAC,UAAU;QACnC,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,MAAM;IACpB,CAAC;AACD,SAAS,sBAAsB,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAG;IACtE,MAAM,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IAC7F,MAAM,mBAAmB,CAAC,CAAC,CAAC,SAAS,oBAAoB,UAAU;IACnE,IAAI,CAAC,kBAAkB;QACnB,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,OAAO;QAAgB,OAAO;QAAO,QAAQ;QAAQ,WAAW;QAAoD,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,KAAK;YAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;gBAAC;gBAA0B,CAAA,GAAA,0JAAA,CAAA,sBAAmB,AAAD,EAAE;aAAS;YAAG,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gBAAgB;gBAAE,OAAO;gBAAO,MAAM;gBAAM,iBAAiB;gBAAW,SAAS;YAAQ;QAAG;IAAG;AACxV;AACA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,0JAAA,CAAA,qBAAkB,CAAC,MAAM,EAAE,eAAe,EAAE,OAAO,EAAG;IAC1F,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;IACnG,IAAI,CAAC,YAAY;QACb;IACJ;IACA,IAAI,iBAAiB;QACjB,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB;YAAE,oBAAoB;YAAM,qBAAqB;YAAO,UAAU;YAAU,YAAY;YAAY,OAAO,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;YAAE,KAAK,GAAG,CAAC;YAAE,KAAK,GAAG,CAAC;YAAE,cAAc;YAAc,YAAY;YAAY,kBAAkB,CAAA,GAAA,0JAAA,CAAA,sBAAmB,AAAD,EAAE;YAAU,QAAQ;YAAQ,UAAU;QAAS;IAChU;IACA,IAAI,OAAO;IACX,MAAM,aAAa;QACf,SAAS,KAAK,CAAC;QACf,SAAS,KAAK,CAAC;QACf,gBAAgB;QAChB,SAAS,GAAG,CAAC;QACb,SAAS,GAAG,CAAC;QACb,gBAAgB;IACpB;IACA,OAAQ;QACJ,KAAK,0JAAA,CAAA,qBAAkB,CAAC,MAAM;YAC1B,CAAC,KAAK,GAAG,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD,EAAE;YACvB;QACJ,KAAK,0JAAA,CAAA,qBAAkB,CAAC,YAAY;YAChC,CAAC,KAAK,GAAG,oBAAoB;YAC7B;QACJ,KAAK,0JAAA,CAAA,qBAAkB,CAAC,IAAI;YACxB,CAAC,KAAK,GAAG,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE;gBACvB,GAAG,UAAU;gBACb,cAAc;YAClB;YACA;QACJ,KAAK,0JAAA,CAAA,qBAAkB,CAAC,UAAU;YAC9B,CAAC,KAAK,GAAG,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE;YAC3B;QACJ;YACI,CAAC,KAAK,GAAG,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE;IACjC;IACA,OAAO,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;QAAE,GAAG;QAAM,MAAM;QAAQ,WAAW;QAA+B,OAAO;IAAM;AACvG;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,aAAa,CAAC;AACpB,8DAA8D;AAC9D,SAAS,0BAA0B,kBAAkB,UAAU;IAC3D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,wCAA4C;YACxC,MAAM,WAAW,IAAI,IAAI;mBAAI,OAAO,IAAI,CAAC,SAAS,OAAO;mBAAM,OAAO,IAAI,CAAC;aAAiB;YAC5F,KAAK,MAAM,OAAO,SAAU;gBACxB,IAAI,SAAS,OAAO,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;oBAChD,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,0JAAA,CAAA,gBAAa,CAAC,WAAW;oBAC3D;gBACJ;YACJ;YACA,SAAS,OAAO,GAAG;QACvB;IACJ,GAAG;QAAC;KAAgB;AACxB;AAEA,SAAS;IACL,MAAM,QAAQ;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,wCAA4C;YACxC,IAAI,CAAC,QAAQ,OAAO,EAAE;gBAClB,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,IAAI,QAAQ,CAAC,CAAC,OAAO,gBAAgB,CAAC,MAAM,MAAM,KAAK,GAAG,GAAG;oBACzD,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;gBAChE;gBACA,QAAQ,OAAO,GAAG;YACtB;QACJ;IACJ,GAAG,EAAE;AACT;AAEA,SAAS,mBAAmB,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,4BAA4B,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,aAAa,EAAE,yBAAyB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,mBAAmB,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAG;IACzlC,0BAA0B;IAC1B,0BAA0B;IAC1B;IACA,iBAAiB;IACjB,gBAAgB;IAChB,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,cAAc;QAAE,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,mBAAmB;QAAmB,cAAc;QAAc,mBAAmB;QAAmB,eAAe;QAAe,kBAAkB;QAAkB,iBAAiB;QAAiB,eAAe;QAAe,kBAAkB;QAAkB,gBAAgB;QAAgB,uBAAuB;QAAuB,sBAAsB;QAAsB,uBAAuB;QAAuB,oBAAoB;QAAoB,cAAc;QAAc,aAAa;QAAa,mBAAmB;QAAmB,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,WAAW;QAAW,iBAAiB;QAAiB,iBAAiB;QAAiB,SAAS;QAAS,SAAS;QAAS,wBAAwB;QAAwB,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,gBAAgB;QAAgB,qBAAqB;QAAqB,kBAAkB;QAAkB,sBAAsB,CAAC,CAAC;QAAU,UAAU,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,UAAU;YAAE,UAAU;gBAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,cAAc;oBAAE,WAAW;oBAAW,aAAa;oBAAa,mBAAmB;oBAAmB,aAAa;oBAAa,kBAAkB;oBAAkB,gBAAgB;oBAAgB,2BAA2B;oBAA2B,mBAAmB;oBAAmB,kBAAkB;oBAAkB,iBAAiB;oBAAiB,kBAAkB;oBAAkB,iBAAiB;oBAAiB,oBAAoB;oBAAoB,gBAAgB;oBAAgB,qBAAqB;oBAAqB,MAAM;gBAAK;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,uBAAuB;oBAAE,OAAO;oBAAqB,MAAM;oBAAoB,WAAW;oBAAyB,gBAAgB;gBAA6B;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;oBAAE,WAAW;gBAAiC;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,cAAc;oBAAE,WAAW;oBAAW,aAAa;oBAAa,mBAAmB;oBAAmB,kBAAkB;oBAAkB,iBAAiB;oBAAiB,kBAAkB;oBAAkB,mBAAmB;oBAAmB,mBAAmB;oBAAmB,2BAA2B;oBAA2B,gBAAgB;oBAAgB,iBAAiB;oBAAiB,qBAAqB;oBAAqB,YAAY;oBAAY,MAAM;gBAAK;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;oBAAE,WAAW;gBAA8B;aAAG;QAAC;IAAG;AACxkF;AACA,mBAAmB,WAAW,GAAG;AACjC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEvB,MAAM,kBAAkB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,UAAU,GAAG,EAAE,UAAU,CAAC,EAAE,UAAU,EAAE,UAAU,EAAG,GAAG,CAAC,CAAC;IACnK,MAAM,aAAa,IAAI;IACvB,MAAM,eAAe,IAAI;IACzB,MAAM,mBAAmB,IAAI;IAC7B,MAAM,aAAa,IAAI;IACvB,MAAM,aAAa,gBAAgB,SAAS,EAAE;IAC9C,MAAM,aAAa,gBAAgB,SAAS,EAAE;IAC9C,MAAM,kBAAkB,cAAc;QAAC;QAAG;KAAE;IAC5C,MAAM,kBAAkB,cAAc,0JAAA,CAAA,iBAAc;IACpD,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,kBAAkB,YAAY;IACrD,MAAM,mBAAmB,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,YAAY,cAAc;QAC1E,YAAY;QACZ,YAAY;QACZ,sBAAsB;IAC1B;IACA,IAAI,YAAY;QAAC;QAAG;QAAG;KAAE;IACzB,IAAI,WAAW,SAAS,QAAQ;QAC5B,MAAM,SAAS,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,YAAY;YAC9C,QAAQ,CAAC,OAAS,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,KAAK,YAAY,KAAK,CAAC,KAAK,MAAM,IAAI,KAAK,aAAa,CAAC;QACjG;QACA,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,OAAO,QAAQ,SAAS,SAAS,gBAAgB,WAAW;QAChH,YAAY;YAAC;YAAG;YAAG;SAAK;IAC5B;IACA,OAAO;QACH,MAAM;QACN,OAAO;QACP,QAAQ;QACR;QACA,OAAO;QACP;QACA;QACA;QACA,OAAO;QACP;QACA;QACA,eAAe;QACf,eAAe;QACf,iBAAiB,iBAAiB;QAClC,iBAAiB,iBAAiB;QAClC,SAAS;QACT;QACA;QACA,iBAAiB,0JAAA,CAAA,iBAAc;QAC/B,YAAY;QACZ,sBAAsB;QACtB,qBAAqB;QACrB,mBAAmB;QACnB,gBAAgB,0JAAA,CAAA,iBAAc,CAAC,MAAM;QACrC,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,YAAY;QACZ,mBAAmB;QACnB,UAAU;YAAC;YAAI;SAAG;QAClB,YAAY;QACZ,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;QAChB,oBAAoB;QACpB,oBAAoB;QACpB,sBAAsB;QACtB,sBAAsB;QACtB,mBAAmB;QACnB,sBAAsB;QACtB,eAAe,WAAW;QAC1B;QACA,iBAAiB;QACjB,YAAY;YAAE,GAAG,0JAAA,CAAA,oBAAiB;QAAC;QACnC,4BAA4B;QAC5B,gBAAgB;QAChB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,oBAAoB;QACpB,cAAc;QACd,kBAAkB;QAClB,SAAS,0JAAA,CAAA,UAAO;QAChB,mBAAmB;QACnB,2BAA2B,EAAE;QAC7B,KAAK;QACL,OAAO;QACP,iBAAiB,0JAAA,CAAA,yBAAsB;IAC3C;AACJ;AAEA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAG,GAAK,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE,CAAC,KAAK;QAChL,eAAe;YACX,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;YAClG,IAAI,CAAC,SAAS;gBACV;YACJ;YACA,MAAM,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE;gBACd,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;YACJ,GAAG;YACH,iBAAiB,QAAQ;YACzB;;;SAGC,GACD,IAAI;gBAAE,iBAAiB;YAAK;QAChC;QACA,OAAO;YACH,GAAG,gBAAgB;gBACf;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACJ,EAAE;YACF,UAAU,CAAC;gBACP,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,oBAAoB,EAAE,aAAa,EAAE,GAAG;gBACtF;;;;;;;aAOC,GACD,MAAM,mBAAmB,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,YAAY,cAAc;oBACrE;oBACA;oBACA;oBACA,eAAe;gBACnB;gBACA,IAAI,iBAAiB,kBAAkB;oBACnC;oBACA,IAAI;wBAAE;wBAAO;wBAAkB,eAAe;wBAAO,gBAAgB;oBAAU;gBACnF,OACK;oBACD,IAAI;wBAAE;wBAAO;oBAAiB;gBAClC;YACJ;YACA,UAAU,CAAC;gBACP,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,GAAG;gBACzC,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,kBAAkB,YAAY;gBACrD,IAAI;oBAAE;gBAAM;YAChB;YACA,yBAAyB,CAAC,OAAO;gBAC7B,IAAI,OAAO;oBACP,MAAM,EAAE,QAAQ,EAAE,GAAG;oBACrB,SAAS;oBACT,IAAI;wBAAE,iBAAiB;oBAAK;gBAChC;gBACA,IAAI,OAAO;oBACP,MAAM,EAAE,QAAQ,EAAE,GAAG;oBACrB,SAAS;oBACT,IAAI;wBAAE,iBAAiB;oBAAK;gBAChC;YACJ;YACA;;;;SAIC,GACD,qBAAqB,CAAC;gBAClB,MAAM,EAAE,kBAAkB,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;gBAChH,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,YAAY,cAAc,SAAS,YAAY;gBAClH,IAAI,CAAC,kBAAkB;oBACnB;gBACJ;gBACA,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,YAAY,cAAc;oBAAE;oBAAY;gBAAW;gBAC3E,IAAI,eAAe;oBACf;oBACA,IAAI;wBAAE,eAAe;wBAAO,gBAAgB;oBAAU;gBAC1D,OACK;oBACD,kFAAkF;oBAClF,IAAI,CAAC;gBACT;gBACA,IAAI,SAAS,SAAS,GAAG;oBACrB,IAAI,OAAO;wBACP,QAAQ,GAAG,CAAC,oCAAoC;oBACpD;oBACA,qBAAqB;gBACzB;YACJ;YACA,qBAAqB,CAAC,eAAe,WAAW,KAAK;gBACjD,MAAM,uBAAuB,EAAE;gBAC/B,MAAM,UAAU,EAAE;gBAClB,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,GAAG;gBAC3C,KAAK,MAAM,CAAC,IAAI,SAAS,IAAI,cAAe;oBACxC,4FAA4F;oBAC5F,MAAM,OAAO,WAAW,GAAG,CAAC;oBAC5B,MAAM,eAAe,CAAC,CAAC,CAAC,MAAM,gBAAgB,MAAM,YAAY,UAAU,QAAQ;oBAClF,MAAM,SAAS;wBACX;wBACA,MAAM;wBACN,UAAU,eACJ;4BACE,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,QAAQ,CAAC,CAAC;4BAClC,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,QAAQ,CAAC,CAAC;wBACtC,IACE,SAAS,QAAQ;wBACvB;oBACJ;oBACA,IAAI,gBAAgB,KAAK,QAAQ,EAAE;wBAC/B,qBAAqB,IAAI,CAAC;4BACtB;4BACA,UAAU,KAAK,QAAQ;4BACvB,MAAM;gCACF,GAAG,SAAS,SAAS,CAAC,gBAAgB;gCACtC,OAAO,SAAS,QAAQ,CAAC,KAAK,IAAI;gCAClC,QAAQ,SAAS,QAAQ,CAAC,MAAM,IAAI;4BACxC;wBACJ;oBACJ;oBACA,QAAQ,IAAI,CAAC;gBACjB;gBACA,IAAI,qBAAqB,MAAM,GAAG,GAAG;oBACjC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG;oBACrC,MAAM,sBAAsB,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE,sBAAsB,YAAY,cAAc;oBAC/F,QAAQ,IAAI,IAAI;gBACpB;gBACA,mBAAmB;YACvB;YACA,oBAAoB,CAAC;gBACjB,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG;gBACnE,IAAI,SAAS,QAAQ;oBACjB,IAAI,iBAAiB;wBACjB,MAAM,eAAe,iBAAiB,SAAS;wBAC/C,SAAS;oBACb;oBACA,IAAI,OAAO;wBACP,QAAQ,GAAG,CAAC,oCAAoC;oBACpD;oBACA,gBAAgB;gBACpB;YACJ;YACA,oBAAoB,CAAC;gBACjB,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG;gBACnE,IAAI,SAAS,QAAQ;oBACjB,IAAI,iBAAiB;wBACjB,MAAM,eAAe,iBAAiB,SAAS;wBAC/C,SAAS;oBACb;oBACA,IAAI,OAAO;wBACP,QAAQ,GAAG,CAAC,oCAAoC;oBACpD;oBACA,gBAAgB;gBACpB;YACJ;YACA,kBAAkB,CAAC;gBACf,MAAM,EAAE,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG;gBACjG,IAAI,sBAAsB;oBACtB,MAAM,cAAc,gBAAgB,GAAG,CAAC,CAAC,SAAW,sBAAsB,QAAQ;oBAClF,mBAAmB;oBACnB;gBACJ;gBACA,mBAAmB,oBAAoB,YAAY,IAAI,IAAI;uBAAI;iBAAgB,GAAG;gBAClF,mBAAmB,oBAAoB;YAC3C;YACA,kBAAkB,CAAC;gBACf,MAAM,EAAE,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG;gBACjG,IAAI,sBAAsB;oBACtB,MAAM,eAAe,gBAAgB,GAAG,CAAC,CAAC,SAAW,sBAAsB,QAAQ;oBACnF,mBAAmB;oBACnB;gBACJ;gBACA,mBAAmB,oBAAoB,YAAY,IAAI,IAAI;uBAAI;iBAAgB;gBAC/E,mBAAmB,oBAAoB,YAAY,IAAI,OAAO;YAClE;YACA,uBAAuB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG;gBACrG,MAAM,kBAAkB,QAAQ,QAAQ;gBACxC,MAAM,kBAAkB,QAAQ,QAAQ;gBACxC,MAAM,cAAc,gBAAgB,GAAG,CAAC,CAAC;oBACrC,MAAM,eAAe,WAAW,GAAG,CAAC,EAAE,EAAE;oBACxC,IAAI,cAAc;wBACd;;;qBAGC,GACD,aAAa,QAAQ,GAAG;oBAC5B;oBACA,OAAO,sBAAsB,EAAE,EAAE,EAAE;gBACvC;gBACA,MAAM,cAAc,gBAAgB,GAAG,CAAC,CAAC,OAAS,sBAAsB,KAAK,EAAE,EAAE;gBACjF,mBAAmB;gBACnB,mBAAmB;YACvB;YACA,YAAY,CAAC;gBACT,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;gBAC7B,SAAS,eAAe;oBAAC;oBAAS;iBAAQ;gBAC1C,IAAI;oBAAE;gBAAQ;YAClB;YACA,YAAY,CAAC;gBACT,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;gBAC7B,SAAS,eAAe;oBAAC;oBAAS;iBAAQ;gBAC1C,IAAI;oBAAE;gBAAQ;YAClB;YACA,oBAAoB,CAAC;gBACjB,MAAM,OAAO,EAAE,mBAAmB;gBAClC,IAAI;oBAAE;gBAAgB;YAC1B;YACA,sBAAsB,CAAC;gBACnB,MAAM,OAAO,EAAE,iBAAiB;YACpC;YACA,uBAAuB;gBACnB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG;gBACrF,IAAI,CAAC,oBAAoB;oBACrB;gBACJ;gBACA,MAAM,cAAc,MAAM,MAAM,CAAC,CAAC,KAAK,OAAU,KAAK,QAAQ,GAAG;2BAAI;wBAAK,sBAAsB,KAAK,EAAE,EAAE;qBAAO,GAAG,KAAM,EAAE;gBAC3H,MAAM,cAAc,MAAM,MAAM,CAAC,CAAC,KAAK,OAAU,KAAK,QAAQ,GAAG;2BAAI;wBAAK,sBAAsB,KAAK,EAAE,EAAE;qBAAO,GAAG,KAAM,EAAE;gBAC3H,mBAAmB;gBACnB,mBAAmB;YACvB;YACA,eAAe,CAAC;gBACZ,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,oBAAoB,EAAE,UAAU,EAAE,GAAG;gBAC1F,IAAI,cAAc,CAAC,EAAE,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,CAAC,EAAE,IACzC,cAAc,CAAC,EAAE,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,CAAC,EAAE,IACzC,cAAc,CAAC,EAAE,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,CAAC,EAAE,IACzC,cAAc,CAAC,EAAE,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC3C;gBACJ;gBACA,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,YAAY,cAAc;oBAC5C;oBACA,YAAY;oBACZ;oBACA,eAAe;gBACnB;gBACA,IAAI;oBAAE,YAAY;gBAAe;YACrC;YACA,OAAO,CAAC;gBACJ,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG;gBAC/D,OAAO,CAAA,GAAA,0JAAA,CAAA,QAAK,AAAD,EAAE;oBAAE;oBAAO;oBAAS;oBAAW;oBAAiB;oBAAO;gBAAO;YAC7E;YACA,WAAW,OAAO,GAAG,GAAG;gBACpB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;gBAC5C,IAAI,CAAC,SAAS;oBACV,OAAO,QAAQ,OAAO,CAAC;gBAC3B;gBACA,MAAM,WAAW,OAAO,SAAS,SAAS,cAAc,QAAQ,IAAI,GAAG;gBACvE,MAAM,QAAQ,WAAW,CAAC;oBACtB,GAAG,QAAQ,IAAI,IAAI;oBACnB,GAAG,SAAS,IAAI,IAAI;oBACpB,MAAM;gBACV,GAAG;oBAAE,UAAU,SAAS;oBAAU,MAAM,SAAS;oBAAM,aAAa,SAAS;gBAAY;gBACzF,OAAO,QAAQ,OAAO,CAAC;YAC3B;YACA,kBAAkB;gBACd,IAAI;oBACA,YAAY;wBAAE,GAAG,0JAAA,CAAA,oBAAiB;oBAAC;gBACvC;YACJ;YACA,kBAAkB,CAAC;gBACf,IAAI;oBAAE;gBAAW;YACrB;YACA,OAAO,IAAM,IAAI;oBAAE,GAAG,iBAAiB;gBAAC;QAC5C;IACJ,GAAG,OAAO,EAAE;AAEZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC,GACD,SAAS,kBAAkB,EAAE,cAAc,KAAK,EAAE,cAAc,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,KAAK,EAAE,eAAe,MAAM,EAAE,gBAAgB,OAAO,EAAE,gBAAgB,OAAO,EAAE,uBAAuB,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAG;IAChR,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,YAAY;YACvC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACJ;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,YAAY;QAAE,OAAO;QAAO,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;YAAE,UAAU;QAAS;IAAG;AACjG;AAEA,SAAS,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAG;IACtJ,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,IAAI,WAAW;QACX;;;SAGC,GACD,OAAO,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;YAAE,UAAU;QAAS;IAC9C;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,mBAAmB;QAAE,cAAc;QAAO,cAAc;QAAO,cAAc;QAAc,cAAc;QAAc,cAAc;QAAO,eAAe;QAAQ,SAAS;QAAS,uBAAuB;QAAgB,gBAAgB;QAAS,gBAAgB;QAAS,YAAY;QAAY,YAAY;QAAY,UAAU;IAAS;AACjW;AAEA,MAAM,eAAe;IACjB,OAAO;IACP,QAAQ;IACR,UAAU;IACV,UAAU;IACV,QAAQ;AACZ;AACA,SAAS,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,eAAe,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,qBAAqB,0JAAA,CAAA,qBAAkB,CAAC,MAAM,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,4BAA4B,EAAE,gBAAgB,WAAW,EAAE,mBAAmB,OAAO,EAAE,kBAAkB,KAAK,EAAE,gBAAgB,0JAAA,CAAA,gBAAa,CAAC,IAAI,EAAE,uBAAuB,OAAO,EAAE,wBAAwB,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,MAAM,SAAS,SAAS,EAAE,wBAAwB,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,MAAM,SAAS,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,4BAA4B,KAAK,EAAE,iBAAiB,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,iBAAiB,EAAE,cAAc,EAAE,kBAAkB,EAAE,qBAAqB,IAAI,EAAE,iBAAiB,oBAAoB,eAAe,EAAE,UAAU,GAAG,EAAE,UAAU,CAAC,EAAE,kBAAkB,0JAAA,CAAA,iBAAc,EAAE,mBAAmB,IAAI,EAAE,UAAU,EAAE,qBAAqB,SAAS,EAAE,eAAe,IAAI,EAAE,cAAc,IAAI,EAAE,cAAc,KAAK,EAAE,mBAAmB,GAAG,EAAE,kBAAkB,0JAAA,CAAA,kBAAe,CAAC,IAAI,EAAE,oBAAoB,IAAI,EAAE,YAAY,IAAI,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,iBAAiB,EAAE,oBAAoB,CAAC,EAAE,oBAAoB,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,EAAE,aAAa,EAAE,aAAa,EAAE,kBAAkB,QAAQ,EAAE,mBAAmB,SAAS,EAAE,iBAAiB,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,mBAAmB,EAAE,UAAU,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,sBAAsB,KAAK,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,iBAAiB,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,MAAM,EAAE,GAAG;IACj2E,MAAM,OAAO,MAAM;IACnB,MAAM,qBAAqB,kBAAkB;IAC7C,6FAA6F;IAC7F,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,EAAE,aAAa,CAAC,QAAQ,CAAC;YAAE,KAAK;YAAG,MAAM;YAAG,UAAU;QAAU;QAChE,WAAW;IACf,GAAG;QAAC;KAAS;IACb,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,eAAe;QAAe,GAAG,IAAI;QAAE,UAAU;QAAiB,OAAO;YAAE,GAAG,KAAK;YAAE,GAAG,YAAY;QAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAc;YAAW;SAAmB;QAAG,IAAI;QAAI,MAAM;QAAe,UAAU,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,SAAS;YAAE,OAAO;YAAO,OAAO;YAAO,OAAO;YAAO,QAAQ;YAAQ,SAAS;YAAS,gBAAgB;YAAgB,SAAS;YAAS,SAAS;YAAS,YAAY;YAAY,YAAY;YAAY,UAAU;gBAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,WAAW;oBAAE,QAAQ;oBAAQ,aAAa;oBAAa,aAAa;oBAAa,kBAAkB;oBAAkB,iBAAiB;oBAAiB,kBAAkB;oBAAkB,mBAAmB;oBAAmB,mBAAmB;oBAAmB,WAAW;oBAAW,WAAW;oBAAW,oBAAoB;oBAAoB,qBAAqB;oBAAqB,yBAAyB;oBAAyB,8BAA8B;oBAA8B,kBAAkB;oBAAkB,iBAAiB;oBAAiB,eAAe;oBAAe,eAAe;oBAAe,uBAAuB;oBAAuB,sBAAsB;oBAAsB,uBAAuB;oBAAuB,2BAA2B;oBAA2B,iBAAiB;oBAAmB,iBAAiB;oBAAiB,SAAS;oBAAS,SAAS;oBAAS,kBAAkB;oBAAkB,cAAc;oBAAc,aAAa;oBAAa,mBAAmB;oBAAmB,aAAa;oBAAa,kBAAkB;oBAAkB,iBAAiB;oBAAiB,WAAW;oBAAW,aAAa;oBAAa,kBAAkB;oBAAkB,iBAAiB;oBAAiB,kBAAkB;oBAAkB,cAAc;oBAAc,mBAAmB;oBAAmB,mBAAmB;oBAAmB,mBAAmB;oBAAmB,wBAAwB;oBAAwB,kBAAkB;oBAAkB,gBAAgB;oBAAgB,aAAa;oBAAa,kBAAkB;oBAAkB,gBAAgB;oBAAgB,mBAAmB;oBAAmB,mBAAmB;oBAAmB,kBAAkB;oBAAkB,iBAAiB;oBAAiB,kBAAkB;oBAAkB,iBAAiB;oBAAiB,oBAAoB;oBAAoB,iBAAiB;oBAAiB,kBAAkB;oBAAkB,gBAAgB;oBAAgB,MAAM;oBAAM,qBAAqB;oBAAqB,YAAY;oBAAY,UAAU;oBAAU,kBAAkB;gBAAiB;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,cAAc;oBAAE,OAAO;oBAAO,OAAO;oBAAO,cAAc;oBAAc,cAAc;oBAAc,WAAW;oBAAW,gBAAgB;oBAAgB,cAAc;oBAAc,qBAAqB;oBAAqB,mBAAmB;oBAAmB,gBAAgB;oBAAgB,oBAAoB;oBAAoB,kBAAkB;oBAAkB,gBAAgB;oBAAgB,gBAAgB;oBAAgB,oBAAoB;oBAAoB,oBAAoB;oBAAoB,sBAAsB;oBAAsB,sBAAsB;oBAAsB,SAAS;oBAAS,SAAS;oBAAS,YAAY;oBAAY,eAAe;oBAAe,eAAe;oBAAe,YAAY;oBAAY,UAAU;oBAAU,gBAAgB;oBAAgB,iBAAiB;oBAAiB,gBAAgB;oBAAgB,oBAAoB;oBAAoB,SAAS;oBAAS,gBAAgB;oBAAgB,eAAe;oBAAe,eAAe;oBAAe,UAAU;oBAAU,iBAAiB;oBAAiB,YAAY;oBAAY,gBAAgB;oBAAgB,iBAAiB;oBAAiB,sBAAsB;oBAAsB,qBAAqB;oBAAqB,QAAQ;oBAAQ,aAAa;oBAAa,WAAW;oBAAW,gBAAgB;oBAAgB,YAAY;oBAAY,MAAM;oBAAM,kBAAkB;oBAAkB,mBAAmB;oBAAmB,cAAc;oBAAc,SAAS;oBAAS,kBAAkB;oBAAkB,mBAAmB;oBAAmB,mBAAmB;oBAAmB,mBAAmB;oBAAmB,gBAAgB;oBAAgB,mBAAmB;oBAAmB,OAAO;oBAAO,iBAAiB;gBAAgB;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,mBAAmB;oBAAE,mBAAmB;gBAAkB;gBAAI;gBAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,aAAa;oBAAE,YAAY;oBAAY,UAAU;gBAAoB;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,kBAAkB;oBAAE,MAAM;oBAAM,qBAAqB;gBAAoB;aAAG;QAAC;IAAG;AAC7gJ;AACA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,IAAI,QAAQ,gBAAgB;AAE5B,MAAM,aAAa,CAAC,IAAM,EAAE,OAAO,EAAE,cAAc;AACnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwCC,GACD,SAAS,kBAAkB,EAAE,QAAQ,EAAE;IACnC,MAAM,oBAAoB,SAAS;IACnC,IAAI,CAAC,mBAAmB;QACpB,OAAO;IACX;IACA,OAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,UAAU;AAClC;AAEA,MAAM,aAAa,CAAC,IAAM,EAAE,OAAO,EAAE,cAAc;AACnD;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,eAAe,EAAE,QAAQ,EAAE;IAChC,MAAM,gBAAgB,SAAS;IAC/B,IAAI,CAAC,eAAe;QAChB,OAAO;IACX;IACA,OAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,UAAU;AAClC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4CC,GACD,SAAS;IACL,MAAM,QAAQ;IACd,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChB,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,GAAG,MAAM,QAAQ;QACvD,MAAM,YAAY,MAAM,OAAO,CAAC,MAAM,KAAK;YAAC;SAAG;QAC/C,MAAM,UAAU,IAAI;QACpB,UAAU,OAAO,CAAC,CAAC;YACf,MAAM,cAAc,SAAS,cAAc,CAAC,2BAA2B,EAAE,SAAS,EAAE,CAAC;YACrF,IAAI,aAAa;gBACb,QAAQ,GAAG,CAAC,UAAU;oBAAE,IAAI;oBAAU;oBAAa,OAAO;gBAAK;YACnE;QACJ;QACA,sBAAsB,IAAM,oBAAoB,SAAS;gBAAE,gBAAgB;YAAM;IACrF,GAAG,EAAE;AACT;AAEA,MAAM,gBAAgB,CAAC,QAAU,MAAM,KAAK;AAC5C;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS;IACL,MAAM,QAAQ,SAAS,eAAe,0IAAA,CAAA,UAAO;IAC7C,OAAO;AACX;AAEA,MAAM,gBAAgB,CAAC,QAAU,MAAM,KAAK;AAC5C;;;;;;;;;;;;;;;;;CAiBC,GACD,SAAS;IACL,MAAM,QAAQ,SAAS,eAAe,0IAAA,CAAA,UAAO;IAC7C,OAAO;AACX;AAEA,MAAM,mBAAmB,CAAC,QAAU,CAAC;QACjC,GAAG,MAAM,SAAS,CAAC,EAAE;QACrB,GAAG,MAAM,SAAS,CAAC,EAAE;QACrB,MAAM,MAAM,SAAS,CAAC,EAAE;IAC5B,CAAC;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,SAAS;IACL,MAAM,WAAW,SAAS,kBAAkB,0IAAA,CAAA,UAAO;IACnD,OAAO;AACX;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4CC,GACD,SAAS,cAAc,YAAY;IAC/B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAY,SAAS,CAAC,MAAQ,iBAAiB,SAAS,OAAO,EAAE;IACpG,OAAO;QAAC;QAAO;QAAU;KAAc;AAC3C;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8CC,GACD,SAAS,cAAc,YAAY;IAC/B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAY,SAAS,CAAC,MAAQ,iBAAiB,SAAS,OAAO,EAAE;IACpG,OAAO;QAAC;QAAO;QAAU;KAAc;AAC3C;AAEA;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,SAAS,oBAAoB,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;IACrD,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,CAAC;YAAE,uBAAuB;QAAQ;IACpD,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,CAAC;YAAE,kBAAkB;QAAS;IAChD,GAAG;QAAC;KAAS;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,CAAC;YAAE,qBAAqB;QAAM;IAChD,GAAG;QAAC;KAAM;AACd;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GACD,SAAS,qBAAqB,EAAE,QAAQ,EAAG;IACvC,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,gCAAgC;eAAI,MAAM,QAAQ,GAAG,yBAAyB;YAAE;SAAS;QAC/F,MAAM,QAAQ,CAAC;YAAE,2BAA2B;QAA8B;QAC1E,OAAO;YACH,MAAM,eAAe,MAAM,QAAQ,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;YACtF,MAAM,QAAQ,CAAC;gBAAE,2BAA2B;YAAa;QAC7D;IACJ,GAAG;QAAC;KAAS;AACjB;AAEA,MAAM,aAAa,CAAC,UAAY,CAAC;QAC7B,IAAI,CAAC,QAAQ,kBAAkB,EAAE;YAC7B,OAAO,EAAE,gBAAgB;QAC7B;QACA,IAAI,EAAE,UAAU,CAAC,IAAI,KAAK,GAAG;YACzB,OAAO;QACX;QACA,KAAK,MAAM,GAAG,EAAE,SAAS,EAAE,CAAC,IAAI,EAAE,UAAU,CAAE;YAC1C,IAAI,UAAU,YAAY,KAAK,aAAa,CAAC,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ,GAAG;gBAChF,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgCC,GACD,SAAS,oBAAoB,UAAU;IACnC,oBAAoB;AACxB,CAAC;IACG,MAAM,cAAc,SAAS,WAAW;IACxC,OAAO;AACX;AAEA;;;;;;CAMC,GACD,SAAS,qBAAqB,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAG;IACxE,QAAQ,IAAI,CAAC;IACb,MAAM,UAAU;IAChB,MAAM,gBAAgB,UAAU;IAChC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,cAAc,SAAS,CAAC,QAAU,MAAM,gBAAgB,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,GAAG,0JAAA,CAAA,yBAAsB;IAC3I,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,6FAA6F;QAC7F,IAAI,gBAAgB,OAAO,IAAI,gBAAgB,OAAO,KAAK,aAAa;YACpE,MAAM,eAAe,eAAe,IAAI;YACxC,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,gBAAgB,OAAO,EAAE,cAAc;YAC9D,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc,gBAAgB,OAAO,EAAE;QAClE;QACA,gBAAgB,OAAO,GAAG,eAAe,IAAI;IACjD,GAAG;QAAC;QAAa;QAAW;KAAa;IACzC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,MAAM,IAAI,CAAC,aAAa,YAAY,EAAE,GAAG;QAAC;KAAY;AAC/E;AAEA,MAAM,WAAW,0JAAA,CAAA,gBAAa,CAAC,WAAW;AAC1C;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,SAAS,mBAAmB,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAG,GAAG,CAAC,CAAC;IACnF,MAAM,SAAS;IACf,MAAM,gBAAgB,MAAM;IAC5B,IAAI,CAAC,eAAe;QAChB,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,cAAc,SAAS,CAAC,QAAU,MAAM,gBAAgB,CAAC,GAAG,CAAC,GAAG,gBAAgB,aAAc,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,YAAY,GAAI,IAAI,GAAG,0JAAA,CAAA,yBAAsB;IAC/L,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,8FAA8F;QAC9F,IAAI,gBAAgB,OAAO,IAAI,gBAAgB,OAAO,KAAK,aAAa;YACpE,MAAM,eAAe,eAAe,IAAI;YACxC,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,gBAAgB,OAAO,EAAE,cAAc;YAC9D,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc,gBAAgB,OAAO,EAAE;QAClE;QACA,gBAAgB,OAAO,GAAG,eAAe,IAAI;IACjD,GAAG;QAAC;QAAa;QAAW;KAAa;IACzC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,MAAM,IAAI,CAAC,aAAa,YAAY,EAAE,GAAG;QAAC;KAAY;AAC/E;AAEA,8DAA8D;AAC9D,SAAS,aAAa,OAAO;IACzB,MAAM,YAAY,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,OAAO,EAAE;QACf,MAAM,eAAe,MAAM,OAAO,CAAC;QACnC,MAAM,WAAW,eAAe,UAAU;YAAC;SAAQ;QACnD,KAAK,MAAM,UAAU,SAAU;YAC3B,MAAM,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC;YAC9B,IAAI,MAAM;gBACN,KAAK,IAAI,CAAC;oBACN,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACnB;YACJ;QACJ;QACA,OAAO,eAAe,OAAO,IAAI,CAAC,EAAE,IAAI;IAC5C,GAAG;QAAC;KAAQ,GAAG,0JAAA,CAAA,kBAAe;IAC9B,OAAO;AACX;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,SAAS,gBAAgB,EAAE;IACvB,MAAM,OAAO,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAM,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK;QAAC;KAAG,GAAG,0IAAA,CAAA,UAAO;IAC7E,OAAO;AACX;AAEA,SAAS,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE;IAC9D,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;QAAE,aAAa;QAAW,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAkC;YAAS;SAAU;IAAE;AAClN;AACA,SAAS,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE;IACrC,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;QAAE,IAAI;QAAQ,IAAI;QAAQ,GAAG;QAAQ,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAkC;YAAQ;SAAU;IAAE;AACpI;AAEA;;;;;CAKC,GACD,IAAI;AACJ,CAAC,SAAU,iBAAiB;IACxB,iBAAiB,CAAC,QAAQ,GAAG;IAC7B,iBAAiB,CAAC,OAAO,GAAG;IAC5B,iBAAiB,CAAC,QAAQ,GAAG;AACjC,CAAC,EAAE,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;AAE/C,MAAM,cAAc;IAChB,CAAC,kBAAkB,IAAI,CAAC,EAAE;IAC1B,CAAC,kBAAkB,KAAK,CAAC,EAAE;IAC3B,CAAC,kBAAkB,KAAK,CAAC,EAAE;AAC/B;AACA,MAAM,aAAa,CAAC,IAAM,CAAC;QAAE,WAAW,EAAE,SAAS;QAAE,WAAW,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE;IAAC,CAAC;AACrF,SAAS,oBAAoB,EAAE,EAAE,EAAE,UAAU,kBAAkB,IAAI,EACnE,+BAA+B;AAC/B,MAAM,EAAE,EACR,gCAAgC;AAChC,IAAI,EAAE,YAAY,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAG;IAClF,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IAC7D,MAAM,cAAc,QAAQ,WAAW,CAAC,QAAQ;IAChD,MAAM,SAAS,YAAY,kBAAkB,IAAI;IACjD,MAAM,UAAU,YAAY,kBAAkB,KAAK;IACnD,MAAM,QAAQ,MAAM,OAAO,CAAC,OAAO,MAAM;QAAC;QAAK;KAAI;IACnD,MAAM,YAAY;QAAC,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI;QAAG,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI;KAAE;IAC9E,MAAM,aAAa,cAAc,SAAS,CAAC,EAAE;IAC7C,MAAM,WAAW,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;QAAQ;KAAO;IAClE,MAAM,oBAAoB,UAAU;QAAC;QAAY;KAAW,GAAG;IAC/D,MAAM,eAAe;QACjB,QAAQ,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI,IAAI,iBAAiB,CAAC,EAAE,GAAG;QACzD,QAAQ,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI,IAAI,iBAAiB,CAAC,EAAE,GAAG;KAC5D;IACD,MAAM,aAAa,GAAG,YAAY,KAAK,KAAK,IAAI;IAChD,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAA0B;SAAU;QAAG,OAAO;YAC3E,GAAG,KAAK;YACR,GAAG,cAAc;YACjB,+BAA+B;YAC/B,uCAAuC;QAC3C;QAAG,KAAK;QAAK,eAAe;QAAkB,UAAU;YAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,WAAW;gBAAE,IAAI;gBAAY,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;gBAAE,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;gBAAE,OAAO,SAAS,CAAC,EAAE;gBAAE,QAAQ,SAAS,CAAC,EAAE;gBAAE,cAAc;gBAAkB,kBAAkB,CAAC,WAAW,EAAE,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;gBAAE,UAAU,SAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,YAAY;oBAAE,QAAQ,aAAa;oBAAG,WAAW;gBAAiB,KAAO,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,aAAa;oBAAE,YAAY;oBAAmB,WAAW;oBAAW,SAAS;oBAAS,WAAW;gBAAiB;YAAI;YAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG;gBAAK,GAAG;gBAAK,OAAO;gBAAQ,QAAQ;gBAAQ,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAAC;SAAG;IAAC;AACjnB;AACA,oBAAoB,WAAW,GAAG;AAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoDC,GACD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAExB,SAAS;IACL,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,OAAO;QAA8B,SAAS;QAAa,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,GAAG;QAAwE;IAAG;AAC1L;AAEA,SAAS;IACL,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,OAAO;QAA8B,SAAS;QAAY,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,GAAG;QAAiB;IAAG;AAClI;AAEA,SAAS;IACL,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,OAAO;QAA8B,SAAS;QAAa,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,GAAG;QAA8X;IAAG;AAChf;AAEA,SAAS;IACL,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,OAAO;QAA8B,SAAS;QAAa,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,GAAG;QAAic;IAAG;AACnjB;AAEA,SAAS;IACL,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,OAAO;QAA8B,SAAS;QAAa,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,GAAG;QAAuY;IAAG;AACzf;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,MAAM;IACnD,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;QAAE,MAAM;QAAU,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAA+B;SAAU;QAAG,GAAG,IAAI;QAAE,UAAU;IAAS;AACnI;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,eAAe,EAAE,cAAc,IAAI,EAAE,gBAAgB,IAAI,EAAE,kBAAkB;QAC7E,gBAAgB,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO;QAC3C,gBAAgB,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO;QAC3C,iBAAiB,EAAE,eAAe;IACtC,CAAC;AACD,SAAS,kBAAkB,EAAE,KAAK,EAAE,WAAW,IAAI,EAAE,cAAc,IAAI,EAAE,kBAAkB,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,mBAAmB,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,aAAa,EAAE,cAAc,UAAU,EAAE,cAAc,SAAS,EAAG;IACrQ,MAAM,QAAQ;IACd,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IACvG,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IACrC,MAAM,kBAAkB;QACpB;QACA;IACJ;IACA,MAAM,mBAAmB;QACrB;QACA;IACJ;IACA,MAAM,mBAAmB;QACrB,QAAQ;QACR;IACJ;IACA,MAAM,wBAAwB;QAC1B,MAAM,QAAQ,CAAC;YACX,gBAAgB,CAAC;YACjB,kBAAkB,CAAC;YACnB,oBAAoB,CAAC;QACzB;QACA,sBAAsB,CAAC;IAC3B;IACA,MAAM,mBAAmB,gBAAgB,eAAe,eAAe;IACvE,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAwB;YAAkB;SAAU;QAAG,UAAU;QAAU,OAAO;QAAO,eAAe;QAAgB,cAAc,aAAa,eAAe,CAAC,qBAAqB;QAAE,UAAU;YAAC,YAAa,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;gBAAE,UAAU;oBAAC,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;wBAAE,SAAS;wBAAiB,WAAW;wBAA+B,OAAO,eAAe,CAAC,4BAA4B;wBAAE,cAAc,eAAe,CAAC,4BAA4B;wBAAE,UAAU;wBAAgB,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU,CAAC;oBAAG;oBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;wBAAE,SAAS;wBAAkB,WAAW;wBAAgC,OAAO,eAAe,CAAC,6BAA6B;wBAAE,cAAc,eAAe,CAAC,6BAA6B;wBAAE,UAAU;wBAAgB,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,WAAW,CAAC;oBAAG;iBAAG;YAAC;YAAK,eAAgB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;gBAAE,WAAW;gBAAgC,SAAS;gBAAkB,OAAO,eAAe,CAAC,6BAA6B;gBAAE,cAAc,eAAe,CAAC,6BAA6B;gBAAE,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,aAAa,CAAC;YAAG;YAAK,mBAAoB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;gBAAE,WAAW;gBAAoC,SAAS;gBAAuB,OAAO,eAAe,CAAC,iCAAiC;gBAAE,cAAc,eAAe,CAAC,iCAAiC;gBAAE,UAAU,gBAAgB,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,YAAY,CAAC,KAAK,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU,CAAC;YAAG;YAAK;SAAS;IAAC;AAC/2C;AACA,kBAAkB,WAAW,GAAG;AAChC;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEtB,SAAS,qBAAqB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAG;IAC1J,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,SAAS,CAAC;IAClD,MAAM,OAAQ,SAAS,cAAc;IACrC,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAA4B;gBAAE;YAAS;YAAG;SAAU;QAAG,GAAG;QAAG,GAAG;QAAG,IAAI;QAAc,IAAI;QAAc,OAAO;QAAO,QAAQ;QAAQ,OAAO;YACzK;YACA,QAAQ;YACR;QACJ;QAAG,gBAAgB;QAAgB,SAAS,UAAU,CAAC,QAAU,QAAQ,OAAO,MAAM;IAAU;AACxG;AACA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEzB,MAAM,kBAAkB,CAAC,IAAM,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;AAC5D,MAAM,kBAAkB,CAAC,OAAS,gBAAgB,WAAW,OAAO,IAAM;AAC1E,SAAS,aAAa,EAAE,eAAe,EAAE,SAAS,EAAE,gBAAgB,EAAE,EAAE,mBAAmB,CAAC,EAAE,eAAe,EAC7G;;;CAGC,GACD,eAAe,gBAAgB,WAAW,EAAE,OAAO,EAAG;IAClD,MAAM,UAAU,SAAS,iBAAiB,0IAAA,CAAA,UAAO;IACjD,MAAM,gBAAgB,gBAAgB;IACtC,MAAM,sBAAsB,gBAAgB;IAC5C,MAAM,oBAAoB,gBAAgB;IAC1C,MAAM,iBAAiB,OAAO,WAAW,eAAe,CAAC,CAAC,OAAO,MAAM,GAAG,eAAe;IACzF,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU,QAAQ,GAAG,CAAC,CAAC,SAC3C;;;;;;SAMC,GACD,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,sBAAsB;gBAAE,IAAI;gBAAQ,eAAe;gBAAe,qBAAqB;gBAAqB,mBAAmB;gBAAmB,kBAAkB;gBAAkB,iBAAiB;gBAAiB,eAAe;gBAAe,SAAS;gBAAS,gBAAgB;YAAe,GAAG;IAAU;AAChU;AACA,SAAS,0BAA0B,EAAE,EAAE,EAAE,aAAa,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAG;IACxK,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;QAC5C,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC;QACvC,MAAM,OAAO,UAAU,QAAQ;QAC/B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,gBAAgB;QAC3C,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE;QAC5C,OAAO;YACH;YACA;YACA;YACA;YACA;QACJ;IACJ,GAAG,0IAAA,CAAA,UAAO;IACV,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;QAClD,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;QAAE,GAAG;QAAG,GAAG;QAAG,OAAO;QAAO,QAAQ;QAAQ,OAAO,KAAK,KAAK;QAAE,UAAU,CAAC,CAAC,KAAK,QAAQ;QAAE,WAAW,kBAAkB;QAAO,OAAO,cAAc;QAAO,cAAc;QAAkB,aAAa,oBAAoB;QAAO,aAAa;QAAiB,gBAAgB;QAAgB,SAAS;QAAS,IAAI,KAAK,EAAE;IAAC;AAC7V;AACA,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAClC,IAAI,iBAAiB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE1B,MAAM,eAAe;AACrB,MAAM,gBAAgB;AACtB,MAAM,eAAe,CAAC,OAAS,CAAC,KAAK,MAAM;AAC3C,MAAM,aAAa,CAAC;IAChB,MAAM,SAAS;QACX,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,EAAE;QACnC,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,EAAE;QACnC,OAAO,EAAE,KAAK,GAAG,EAAE,SAAS,CAAC,EAAE;QAC/B,QAAQ,EAAE,MAAM,GAAG,EAAE,SAAS,CAAC,EAAE;IACrC;IACA,OAAO;QACH;QACA,cAAc,EAAE,UAAU,CAAC,IAAI,GAAG,IAC5B,CAAA,GAAA,0JAAA,CAAA,mBAAgB,AAAD,EAAE,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE,EAAE,UAAU,EAAE;YAAE,QAAQ;QAAa,IAAI,UACjF;QACN,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,OAAO;QAClB,iBAAiB,EAAE,eAAe;QAClC,WAAW,EAAE,KAAK;QAClB,YAAY,EAAE,MAAM;QACpB,iBAAiB,EAAE,eAAe;IACtC;AACJ;AACA,MAAM,iBAAiB;AACvB,SAAS,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAE,eAAe,EAAE,SAAS,EAAE,gBAAgB,EAAE,EAAE,mBAAmB,CAAC,EAAE,eAAe,EACnI;;;CAGC,GACD,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,cAAc,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,KAAK,EAAE,WAAW,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,cAAc,CAAC,EAAG;IAC9M,MAAM,QAAQ;IACd,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,SAAS,YAAY,0IAAA,CAAA,UAAO;IACrI,MAAM,eAAe,OAAO,SAAS;IACrC,MAAM,gBAAgB,OAAO,UAAU;IACvC,MAAM,cAAc,aAAa,KAAK,GAAG;IACzC,MAAM,eAAe,aAAa,MAAM,GAAG;IAC3C,MAAM,YAAY,KAAK,GAAG,CAAC,aAAa;IACxC,MAAM,YAAY,YAAY;IAC9B,MAAM,aAAa,YAAY;IAC/B,MAAM,SAAS,cAAc;IAC7B,MAAM,IAAI,aAAa,CAAC,GAAG,CAAC,YAAY,aAAa,KAAK,IAAI,IAAI;IAClE,MAAM,IAAI,aAAa,CAAC,GAAG,CAAC,aAAa,aAAa,MAAM,IAAI,IAAI;IACpE,MAAM,QAAQ,YAAY,SAAS;IACnC,MAAM,SAAS,aAAa,SAAS;IACrC,MAAM,aAAa,GAAG,eAAe,CAAC,EAAE,MAAM;IAC9C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC7B,aAAa,OAAO,GAAG;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,IAAI,OAAO,IAAI,SAAS;YACxB,gBAAgB,OAAO,GAAG,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE;gBAChC,SAAS,IAAI,OAAO;gBACpB;gBACA,cAAc,IAAM,MAAM,QAAQ,GAAG,SAAS;gBAC9C,cAAc,IAAM,aAAa,OAAO;YAC5C;YACA,OAAO;gBACH,gBAAgB,OAAO,EAAE;YAC7B;QACJ;IACJ,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,gBAAgB,OAAO,EAAE,OAAO;YAC5B;YACA,OAAO;YACP,QAAQ;YACR;YACA;YACA;YACA;QACJ;IACJ,GAAG;QAAC;QAAU;QAAU;QAAY;QAAU;QAAiB;QAAW;KAAW;IACrF,MAAM,aAAa,UACb,CAAC;QACC,MAAM,CAAC,GAAG,EAAE,GAAG,gBAAgB,OAAO,EAAE,QAAQ,UAAU;YAAC;YAAG;SAAE;QAChE,QAAQ,OAAO;YAAE;YAAG;QAAE;IAC1B,IACE;IACN,MAAM,iBAAiB,cACjB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAO;QAClB,MAAM,OAAO,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,SAAS,CAAC,QAAQ;QACvE,YAAY,OAAO;IACvB,GAAG,EAAE,IACH;IACN,MAAM,aAAa,aAAa,eAAe,CAAC,oBAAoB;IACpE,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,UAAU;QAAU,OAAO;YACxC,GAAG,KAAK;YACR,uCAAuC,OAAO,YAAY,WAAW,UAAU;YAC/E,4CAA4C,OAAO,cAAc,WAAW,YAAY;YACxF,wCAAwC,OAAO,oBAAoB,WAAW,kBAAkB;YAChG,wCAAwC,OAAO,oBAAoB,WAAW,kBAAkB,YAAY;YAC5G,4CAA4C,OAAO,cAAc,WAAW,YAAY;YACxF,wCAAwC,OAAO,oBAAoB,WAAW,kBAAkB;YAChG,wCAAwC,OAAO,oBAAoB,WAAW,kBAAkB;QACpG;QAAG,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAuB;SAAU;QAAG,eAAe;QAAe,UAAU,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;YAAE,OAAO;YAAc,QAAQ;YAAe,SAAS,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ;YAAE,WAAW;YAA2B,MAAM;YAAO,mBAAmB;YAAY,KAAK;YAAK,SAAS;YAAY,UAAU;gBAAC,cAAc,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,SAAS;oBAAE,IAAI;oBAAY,UAAU;gBAAW;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gBAAgB;oBAAE,SAAS;oBAAgB,WAAW;oBAAW,iBAAiB;oBAAiB,kBAAkB;oBAAkB,eAAe;oBAAe,iBAAiB;oBAAiB,eAAe;gBAAc;gBAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;oBAAE,WAAW;oBAA4B,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,EAAE,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC,QAAQ,SAAS,EAAE;SACvwB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC;oBAAE,UAAU;oBAAW,eAAe;gBAAO;aAAG;QAAC;IAAG;AACxI;AACA,iBAAiB,WAAW,GAAG;AAC/B;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAErB,MAAM,gBAAgB,CAAC,iBAAmB,CAAC,QAAU,iBAAiB,GAAG,KAAK,GAAG,CAAC,IAAI,MAAM,SAAS,CAAC,EAAE,EAAE,IAAI,GAAG;AACjH,MAAM,mBAAmB;IACrB,CAAC,0JAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,EAAE;IAC7B,CAAC,0JAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,EAAE;AACnC;AACA,SAAS,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,0JAAA,CAAA,uBAAoB,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,YAAY,EAAE,EAAE,WAAW,OAAO,SAAS,EAAE,YAAY,OAAO,SAAS,EAAE,kBAAkB,KAAK,EAAE,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAG;IACxU,MAAM,gBAAgB;IACtB,MAAM,KAAK,OAAO,WAAW,WAAW,SAAS;IACjD,MAAM,QAAQ;IACd,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,kBAAkB,YAAY,0JAAA,CAAA,uBAAoB,CAAC,MAAM;IAC/D,MAAM,QAAQ,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,cAAc,mBAAmB,YAAY;QAAC;QAAiB;KAAU,GAAG,0IAAA,CAAA,UAAO;IACtH,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,kBAAkB,YAAY,gBAAgB,CAAC,QAAQ;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,iBAAiB,OAAO,IAAI,CAAC,IAAI;YAClC;QACJ;QACA,IAAI,CAAC,QAAQ,OAAO,EAAE;YAClB,QAAQ,OAAO,GAAG,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE;gBACxB,SAAS,iBAAiB,OAAO;gBACjC,QAAQ;gBACR,eAAe;oBACX,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;oBAC3F,OAAO;wBACH;wBACA;wBACA;wBACA;wBACA;wBACA,aAAa;oBACjB;gBACJ;gBACA,UAAU,CAAC,QAAQ;oBACf,MAAM,EAAE,kBAAkB,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;oBACnF,MAAM,UAAU,EAAE;oBAClB,MAAM,eAAe;wBAAE,GAAG,OAAO,CAAC;wBAAE,GAAG,OAAO,CAAC;oBAAC;oBAChD,MAAM,OAAO,WAAW,GAAG,CAAC;oBAC5B,IAAI,QAAQ,KAAK,YAAY,IAAI,KAAK,QAAQ,EAAE;wBAC5C,MAAM,SAAS,KAAK,MAAM,IAAI;wBAC9B,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,IAAI;wBACrD,MAAM,SAAS,OAAO,MAAM,IAAI,KAAK,QAAQ,CAAC,MAAM,IAAI;wBACxD,MAAM,QAAQ;4BACV,IAAI,KAAK,EAAE;4BACX,UAAU,KAAK,QAAQ;4BACvB,MAAM;gCACF;gCACA;gCACA,GAAG,CAAA,GAAA,0JAAA,CAAA,2BAAwB,AAAD,EAAE;oCACxB,GAAG,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;oCAC9B,GAAG,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;gCAClC,GAAG;oCAAE;oCAAO;gCAAO,GAAG,KAAK,QAAQ,EAAE,YAAY,OAAO;4BAC5D;wBACJ;wBACA,MAAM,sBAAsB,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE;4BAAC;yBAAM,EAAE,YAAY,cAAc;wBAClF,QAAQ,IAAI,IAAI;wBAChB;;;yBAGC,GACD,aAAa,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,IAAI;wBACpE,aAAa,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,QAAQ,OAAO,CAAC,IAAI;oBACzE;oBACA,IAAI,aAAa,CAAC,KAAK,aAAa,aAAa,CAAC,KAAK,WAAW;wBAC9D,MAAM,iBAAiB;4BACnB;4BACA,MAAM;4BACN,UAAU;gCAAE,GAAG,YAAY;4BAAC;wBAChC;wBACA,QAAQ,IAAI,CAAC;oBACjB;oBACA,IAAI,OAAO,KAAK,KAAK,aAAa,OAAO,MAAM,KAAK,WAAW;wBAC3D,MAAM,gBAAgB,CAAC,kBAAkB,OAAO,oBAAoB,eAAe,UAAU;wBAC7F,MAAM,kBAAkB;4BACpB;4BACA,MAAM;4BACN,UAAU;4BACV;4BACA,YAAY;gCACR,OAAO,OAAO,KAAK;gCACnB,QAAQ,OAAO,MAAM;4BACzB;wBACJ;wBACA,QAAQ,IAAI,CAAC;oBACjB;oBACA,KAAK,MAAM,eAAe,aAAc;wBACpC,MAAM,iBAAiB;4BACnB,GAAG,WAAW;4BACd,MAAM;wBACV;wBACA,QAAQ,IAAI,CAAC;oBACjB;oBACA,mBAAmB;gBACvB;gBACA,OAAO,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE;oBACrB,MAAM,kBAAkB;wBACpB,IAAI;wBACJ,MAAM;wBACN,UAAU;wBACV,YAAY;4BACR;4BACA;wBACJ;oBACJ;oBACA,MAAM,QAAQ,GAAG,kBAAkB,CAAC;wBAAC;qBAAgB;gBACzD;YACJ;QACJ;QACA,QAAQ,OAAO,CAAC,MAAM,CAAC;YACnB;YACA,YAAY;gBACR;gBACA;gBACA;gBACA;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;QACJ;QACA,OAAO;YACH,QAAQ,OAAO,EAAE;QACrB;IACJ,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM,qBAAqB,gBAAgB,KAAK,CAAC;IACjD,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAA8B;eAAa;YAAoB;YAAS;SAAU;QAAG,KAAK;QAAkB,OAAO;YAC/I,GAAG,KAAK;YACR;YACA,GAAI,SAAS;gBAAE,CAAC,kBAAkB,oBAAoB,cAAc,EAAE;YAAM,CAAC;QACjF;QAAG,UAAU;IAAS;AAC9B;AACA;;;;CAIC,GACD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE/B;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,SAAS,YAAY,EAAE,MAAM,EAAE,YAAY,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,YAAY,EAAE,EAAE,WAAW,OAAO,SAAS,EAAE,YAAY,OAAO,SAAS,EAAE,kBAAkB,KAAK,EAAE,YAAY,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAG;IACtS,IAAI,CAAC,WAAW;QACZ,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU;YAAC,0JAAA,CAAA,4BAAyB,CAAC,GAAG,CAAC,CAAC,WAAc,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,mBAAmB;oBAAE,WAAW;oBAAe,OAAO;oBAAW,QAAQ;oBAAQ,UAAU;oBAAU,SAAS,0JAAA,CAAA,uBAAoB,CAAC,IAAI;oBAAE,OAAO;oBAAO,UAAU;oBAAU,WAAW;oBAAW,UAAU;oBAAU,WAAW;oBAAW,eAAe;oBAAe,iBAAiB;oBAAiB,WAAW;oBAAW,cAAc;oBAAc,UAAU;oBAAU,aAAa;gBAAY,GAAG;YAAa,0JAAA,CAAA,8BAA2B,CAAC,GAAG,CAAC,CAAC,WAAc,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,mBAAmB;oBAAE,WAAW;oBAAiB,OAAO;oBAAa,QAAQ;oBAAQ,UAAU;oBAAU,OAAO;oBAAO,UAAU;oBAAU,WAAW;oBAAW,UAAU;oBAAU,WAAW;oBAAW,eAAe;oBAAe,iBAAiB;oBAAiB,WAAW;oBAAW,cAAc;oBAAc,UAAU;oBAAU,aAAa;gBAAY,GAAG;SAAY;IAAC;AACh6B;AAEA,MAAM,WAAW,CAAC,QAAU,MAAM,OAAO,EAAE,cAAc;AACzD,SAAS,kBAAkB,EAAE,QAAQ,EAAE;IACnC,MAAM,aAAa,SAAS;IAC5B,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,OAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,UAAU;AAClC;AAEA,MAAM,iBAAiB,CAAC,GAAG,IAAM,GAAG,UAAU,iBAAiB,MAAM,GAAG,UAAU,iBAAiB,KAC/F,GAAG,UAAU,iBAAiB,MAAM,GAAG,UAAU,iBAAiB,KAClE,GAAG,SAAS,UAAU,GAAG,SAAS,SAClC,GAAG,SAAS,WAAW,GAAG,SAAS,UACnC,GAAG,aAAa,GAAG,YACnB,GAAG,UAAU,MAAM,GAAG,UAAU;AACpC,MAAM,kBAAkB,CAAC,GAAG;IACxB,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;QACnB,OAAO;IACX;IACA,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,EAAG;QACzB,IAAI,eAAe,MAAM,EAAE,GAAG,CAAC,OAAO;YAClC,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,MAAM,gBAAgB,CAAC,QAAU,CAAC;QAC9B,GAAG,MAAM,SAAS,CAAC,EAAE;QACrB,GAAG,MAAM,SAAS,CAAC,EAAE;QACrB,MAAM,MAAM,SAAS,CAAC,EAAE;QACxB,oBAAoB,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,EAAE,MAAM;IAC1E,CAAC;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCC,GACD,SAAS,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,0JAAA,CAAA,WAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,QAAQ,QAAQ,EAAE,GAAG,MAAM;IACnI,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,SAAS;YAAC,UAAU,iBAAiB;SAAG;QAChF,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAC,KAAK;YACvC,MAAM,OAAO,MAAM,UAAU,CAAC,GAAG,CAAC;YAClC,IAAI,MAAM;gBACN,IAAI,GAAG,CAAC,KAAK,EAAE,EAAE;YACrB;YACA,OAAO;QACX,GAAG,IAAI;QACP,OAAO;IACX,GAAG;QAAC;QAAQ;KAAc;IAC1B,MAAM,QAAQ,SAAS,eAAe;IACtC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,GAAG,SAAS,eAAe,0IAAA,CAAA,UAAO;IAC1E,0GAA0G;IAC1G,MAAM,WAAW,OAAO,cAAc,YAChC,YACA,MAAM,IAAI,KAAK,KAAK,MAAM,MAAM,GAAG,IAAI,GAAG,KAAK,EAAE,YAAY,uBAAuB;IAC1F,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,EAAE;QAC1B,OAAO;IACX;IACA,MAAM,WAAW,CAAA,GAAA,0JAAA,CAAA,yBAAsB,AAAD,EAAE;IACxC,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM,MAAM;IAC1C,MAAM,SAAS,KAAK,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC,OAAS,KAAK,SAAS,CAAC,CAAC,GAAG;IACvE,MAAM,eAAe;QACjB,UAAU;QACV,WAAW,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,UAAU;YAAE;YAAG;YAAG;QAAK,GAAG,UAAU,QAAQ;QAC/E;QACA,GAAG,KAAK;IACZ;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,mBAAmB;QAAE,UAAU,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAAE,OAAO;YAAc,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;gBAAC;gBAA4B;aAAU;YAAG,GAAG,IAAI;YAAE,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,GAAG,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI;YAAI,UAAU;QAAS;IAAG;AACrP", "ignoreList": [0], "debugId": null}}]}