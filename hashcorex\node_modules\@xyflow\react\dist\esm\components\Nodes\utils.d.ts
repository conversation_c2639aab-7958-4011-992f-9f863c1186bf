import type { RefObject } from 'react';
import type { StoreApi } from 'zustand';
import type { ReactFlowState } from '../../types';
export declare function handleNodeClick({ id, store, unselect, nodeRef, }: {
    id: string;
    store: {
        getState: StoreApi<ReactFlowState>['getState'];
        setState: StoreApi<ReactFlowState>['setState'];
    };
    unselect?: boolean;
    nodeRef?: RefObject<HTMLDivElement>;
}): void;
//# sourceMappingURL=utils.d.ts.map