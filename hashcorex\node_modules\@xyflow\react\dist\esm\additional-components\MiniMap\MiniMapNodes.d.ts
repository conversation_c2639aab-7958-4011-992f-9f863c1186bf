import type { Node } from '../../types';
import type { MiniMapNodes as MiniMapNodesProps } from './types';
declare function MiniMapNodes<NodeType extends Node>({ nodeStrokeColor, nodeColor, nodeClassName, nodeBorderRadius, nodeStrokeWidth, nodeComponent: NodeComponent, onClick, }: MiniMapNodesProps<NodeType>): import("react/jsx-runtime").JSX.Element;
declare const _default: typeof MiniMapNodes;
export default _default;
//# sourceMappingURL=MiniMapNodes.d.ts.map