{"name": "@xyflow/system", "version": "0.0.62", "description": "xyflow core system that powers React Flow and Svelte Flow.", "keywords": ["node-based UI", "graph", "diagram", "workflow", "reactflow", "svelteflow", "xyflow"], "files": ["dist"], "source": "src/index.ts", "main": "dist/umd/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {".": {"node": {"types": "./dist/esm/index.d.ts", "module": "./dist/esm/index.js", "require": "./dist/umd/index.js", "import": "./dist/esm/index.mjs"}, "browser": {"import": "./dist/esm/index.js", "require": "./dist/umd/index.js"}, "default": "./dist/esm/index.js"}}, "sideEffects": false, "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/xyflow/xyflow.git", "directory": "packages/system"}, "dependencies": {"@types/d3-drag": "^3.0.7", "@types/d3-interpolate": "^3.0.4", "@types/d3-selection": "^3.0.10", "@types/d3-transition": "^3.0.8", "@types/d3-zoom": "^3.0.8", "d3-drag": "^3.0.0", "d3-interpolate": "^3.0.1", "d3-selection": "^3.0.0", "d3-zoom": "^3.0.0"}, "devDependencies": {"@types/node": "^18.7.16", "typescript": "5.4.5", "@xyflow/eslint-config": "0.0.1", "@xyflow/rollup-config": "0.0.0", "@xyflow/tsconfig": "0.0.0"}, "rollup": {"globals": {"d3-selection": "d3Selection", "d3-zoom": "d3Zoom", "d3-drag": "d3Drag"}, "vanilla": true, "name": "XYFlowSystem"}, "scripts": {"dev": "concurrently \"rollup --config node:@xyflow/rollup-config --watch\"", "build": "rollup --config node:@xyflow/rollup-config --environment NODE_ENV:production", "lint": "eslint --ext .js,.ts src", "typecheck": "tsc --noEmit"}}