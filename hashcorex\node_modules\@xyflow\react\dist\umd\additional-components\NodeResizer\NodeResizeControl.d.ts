import type { ResizeControlProps, ResizeControlLineProps } from './types';
declare function ResizeControl({ nodeId, position, variant, className, style, children, color, minWidth, minHeight, maxWidth, maxHeight, keepAspectRatio, resizeDirection, autoScale, shouldResize, onResizeStart, onResize, onResizeEnd, }: ResizeControlProps): import("react/jsx-runtime").JSX.Element;
export declare function ResizeControlLine(props: ResizeControlLineProps): import("react/jsx-runtime").JSX.Element;
/**
 * To create your own resizing UI, you can use the `NodeResizeControl` component where you can pass children (such as icons).
 * @public
 *
 */
export declare const NodeResizeControl: import("react").MemoExoticComponent<typeof ResizeControl>;
export {};
//# sourceMappingURL=NodeResizeControl.d.ts.map