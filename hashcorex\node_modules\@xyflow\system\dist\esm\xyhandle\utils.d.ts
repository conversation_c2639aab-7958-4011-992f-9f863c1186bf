import type { HandleType, XYPosition, Handle, NodeLookup, ConnectionMode } from '../types';
export declare function getClosestHandle(position: XYPosition, connectionRadius: number, nodeLookup: NodeLookup, fromHandle: {
    nodeId: string;
    type: HandleType;
    id?: string | null;
}): Handle | null;
export declare function getHandle(nodeId: string, handleType: HandleType, handleId: string | null, nodeLookup: NodeLookup, connectionMode: ConnectionMode, withAbsolutePosition?: boolean): Handle | null;
export declare function getHandleType(edgeUpdaterType: HandleType | undefined, handleDomNode: Element | null): HandleType | null;
export declare function isConnectionValid(isInsideConnectionRadius: boolean, isHandleValid: boolean): boolean | null;
//# sourceMappingURL=utils.d.ts.map