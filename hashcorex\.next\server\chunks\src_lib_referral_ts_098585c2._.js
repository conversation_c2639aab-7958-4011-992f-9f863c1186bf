module.exports = {

"[project]/src/lib/referral.ts [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/src_lib_referral_ts_dfa549b6._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/referral.ts [app-route] (ecmascript)");
    });
});
}}),

};